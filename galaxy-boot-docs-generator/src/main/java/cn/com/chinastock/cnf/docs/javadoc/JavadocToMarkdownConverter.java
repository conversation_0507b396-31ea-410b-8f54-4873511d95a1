package cn.com.chinastock.cnf.docs.javadoc;

import org.jsoup.Jsoup;
import org.jsoup.nodes.*;
import org.jsoup.select.Elements;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JavadocToMarkdownConverter {
    public static final Pattern LINK_PATTERN = Pattern.compile("\\{@link\\s+([^}]+)\\}");
    public static final Pattern SEE_PATTERN = Pattern.compile("@see\\s+(\\w+(#)?\\w+[^\n]*)");
    private static final Pattern CODE_PATTERN = Pattern.compile("<pre>\\n\\s*\\s+\\{@(\\w+)\\s+((.*)?)}\\n\\s*</pre>", Pattern.DOTALL);
    private static final Pattern AUTHOR_PATTERN = Pattern.compile("@(author|date|version|param|return)\\s+(.*)");

    /**
     * 将 Javadoc 字符串转换为 Markdown 格式
     *
     * <pre>
     *    {@code
     *        JavadocToMarkdownConverter converter = new JavadocToMarkdownConverter();
     *        String markdown = converter.convert(javadocString, className);
     *        // markdown 包含转换后的 Markdown 内容
     *    }
     * </pre>
     * @param javadoc  Javadoc 字符串
     * @param className 类名
     * @return 转换后的 Markdown 字符串
     */
    public String convert(String javadoc, String className) {
        // 首先，处理内联标签，例如 {@code ...}
        String processedJavadoc = processCode(javadoc);

        // 然后，处理 {@link ...} 标签
        processedJavadoc = processLink(processedJavadoc, className);

        // 然后，将处理后的 Javadoc 作为 HTML 解析
        Document document = Jsoup.parseBodyFragment(processedJavadoc);
        Element body = document.body();
        StringBuilder markdown = new StringBuilder();
        for (Node node : body.childNodes()) {
            markdown.append(processNode(node));
        }

        return markdown.toString().trim().replaceAll("\n{3,}", "\n\n");
    }

    private String processLink(String javadoc, String className) {
        javadoc = LINK_PATTERN.matcher(javadoc).replaceAll(matchResult -> processLinkTag(matchResult.group(1), className));
        javadoc = SEE_PATTERN.matcher(javadoc).replaceAll("");
        javadoc = AUTHOR_PATTERN.matcher(javadoc).replaceAll("");
        return javadoc;
    }

    private String processCode(String javadoc) {
        Matcher matcher = CODE_PATTERN.matcher(javadoc);

        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String tag = matcher.group(1);
            String content = matcher.group(2).trim();

            String replacement;

            if (tag.equals("code")) {
                if (content.contains("\n")) {
                    replacement = "<pre><code>" + adjustIndentation(content) + "</code></pre>";
                } else {
                    replacement = content;
                }
            } else {
                replacement = matcher.group(0);
            }

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    private String adjustIndentation(String content) {
        String[] lines = content.split("\n");
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            // 去除每行前面的空格、制表符和 '*'
            line = line.replaceFirst("^[ \\t]*\\*?[ \\t]*", "");
            sb.append(line).append("\n");
        }

        return sb.toString().trim();
    }

    private String processLinkTag(String content, String className) {
        if (content.startsWith("#")) {
            content = className + content;
        }

        String label = content;
        String linkTarget = content;

        linkTarget = linkTarget.replaceAll("\\$$.*\\$$", "");

        String anchor = linkTarget.replace('#', '-')
                .replace('.', '-')
                .replace('(', '-')
                .replace(')', '-')
                .replace(',', '-')
                .replace(' ', '-')
                .toLowerCase();

        if (anchor.endsWith("-")) {
            anchor = anchor.substring(0, anchor.length() - 1);
        }

        return "[" + label + "](#" + anchor + ")";
    }

    private String processNode(Node node) {
        switch (node) {
            case TextNode textNode:
                String text = textNode.text().trim();
                if (text.isEmpty()) {
                    return "";
                }

                return text.replaceAll(" +", " ");
            case Element element:
                return processElement(element);
            case null:
            default:
                return "";
        }
    }

    private String processElement(Element element) {
        String tagName = element.tagName();
        return switch (tagName) {
            case "p" -> "\n\n" + processChildren(element) + "\n\n";
            case "b", "strong" -> "**" + processChildren(element) + "**";
            case "i", "em" -> "*" + processChildren(element) + "*";
            case "code" -> "`" + processChildren(element) + "`";
            case "pre" -> "\n\n```\n" + element.text() + "\n```\n";
            case "a" -> processAnchor(element);
            case "ul" -> processList(element, "* ");
            case "ol" -> processList(element, "1. ");
            case "li" -> processChildren(element) + "\n";
            case "img" -> processImage(element);
            case "blockquote" -> "\n\n> " + processChildren(element) + "\n\n";
            case "br" -> "  \n";
            case "h1" -> "\n# " + processChildren(element) + "\n";
            case "h2" -> "\n## " + processChildren(element) + "\n";
            case "h3" -> "\n### " + processChildren(element) + "\n";
            case "table" -> processTable(element);
            default -> processChildren(element);
        };
    }

    private String processChildren(Element element) {
        StringBuilder content = new StringBuilder();
        for (Node child : element.childNodes()) {
            content.append(processNode(child));
        }

        return content.toString();
    }

    private String processAnchor(Element element) {
        String href = element.attr("href");
        String text = processChildren(element);
        if (href.isEmpty()) {
            return text;
        }
        return "[" + text + "](" + href + ")";
    }

    private String processList(Element element, String prefix) {
        StringBuilder listContent = new StringBuilder("\n");
        for (Element li : element.select("> li")) {
            listContent.append(prefix).append(processChildren(li)).append("\n");
        }

        return listContent.toString();
    }

    private String processImage(Element element) {
        String src = element.attr("src");
        String alt = element.attr("alt");
        return "![" + alt + "](" + src + ")";
    }

    private String processTable(Element element) {
        StringBuilder tableContent = new StringBuilder("\n\n");
        Elements rows = element.select("tr");
        if (rows.isEmpty()) {
            return "";
        }

        Elements headers = Objects.requireNonNull(rows.first()).select("th, td");
        for (Element header : headers) {
            tableContent.append("| ").append(processChildren(header)).append(" ");
        }
        tableContent.append("|\n");

        tableContent.append("|---".repeat(headers.size()));
        tableContent.append("|\n");

        // 处理表格行
        for (int i = 1; i < rows.size(); i++) {
            Elements cols = rows.get(i).select("td");
            for (Element col : cols) {
                tableContent.append("| ").append(processChildren(col)).append(" ");
            }
            tableContent.append("|\n");
        }

        tableContent.append("\n");
        return tableContent.toString();
    }
}
