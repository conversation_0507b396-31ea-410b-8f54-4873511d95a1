package cn.com.chinastock.cnf.docs.maven;

import cn.com.chinastock.cnf.docs.model.StarterInfo;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.maven.model.Dependency;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 远程 Maven 仓库工具类
 */
public class RemoteMavenUtil {
    private static final String MAVEN_REPO_BASE_URL = "https://bkrepo.chinastock.com.cn/maven/n614c6/maven-release/";
    private static final String GROUP_ID = "cn.com.chinastock";
    private static final String JDK8_ARTIFACT_ID = "galaxy-boot-dependencies-jdk8";
    private static final String JDK21_ARTIFACT_ID = "galaxy-boot-dependencies";
    
    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .build();

    /**
     * 获取 JDK21 dependencies 的最新稳定版本
     * 排除 alpha、beta、rc、snapshot 等预发布版本
     *
     * @return 最新稳定版本号
     * @throws Exception 如果获取失败
     */
    public static String getLatestStableJdk21Version() throws Exception {
        String metadataUrl = buildMetadataUrl(JDK21_ARTIFACT_ID);
        String metadataXml = fetchUrl(metadataUrl);
        return MavenVersionParser.parseLatestStableVersion(metadataXml);
    }

    /**
     * 获取 JDK8 dependencies 的最新稳定版本
     * 排除 alpha、beta、rc、snapshot 等预发布版本
     *
     * @return 最新稳定版本号
     * @throws Exception 如果获取失败
     */
    public static String getLatestStableJdk8Version() throws Exception {
        String metadataUrl = buildMetadataUrl(JDK8_ARTIFACT_ID);
        String metadataXml = fetchUrl(metadataUrl);
        return MavenVersionParser.parseLatestStableVersion(metadataXml);
    }

    /**
     * 获取指定版本的 JDK21 dependencies POM 文件内容
     *
     * @param version 版本号
     * @return POM 文件内容
     * @throws Exception 如果获取失败
     */
    public static String getJdk21DependenciesPom(String version) throws Exception {
        String pomUrl = buildPomUrl(GROUP_ID, JDK21_ARTIFACT_ID, version);
        return fetchUrl(pomUrl);
    }

    /**
     * 获取指定版本的 JDK8 dependencies POM 文件内容
     *
     * @param version 版本号
     * @return POM 文件内容
     * @throws Exception 如果获取失败
     */
    public static String getJdk8DependenciesPom(String version) throws Exception {
        String pomUrl = buildPomUrl(GROUP_ID, JDK8_ARTIFACT_ID, version);
        return fetchUrl(pomUrl);
    }

    /**
     * 从 POM 内容中提取 starter 列表
     *
     * @param pomContent  POM 文件内容
     * @param version 版本号
     * @return starter 列表
     * @throws Exception 如果解析失败
     */
    public static List<StarterInfo> extractStartersFromPom(String pomContent, String version) throws Exception {
        List<StarterInfo> starters = new ArrayList<>();
        
        MavenXpp3Reader reader = new MavenXpp3Reader();
        Model model = reader.read(new StringReader(pomContent));
        
        if (model.getDependencyManagement() != null && 
            model.getDependencyManagement().getDependencies() != null) {
            
            for (Dependency dependency : model.getDependencyManagement().getDependencies()) {
                String artifactId = dependency.getArtifactId();
                
                // 只处理 galaxy-boot-starter 相关的依赖
                if (artifactId != null && (artifactId.startsWith("galaxy-boot-"))) {
                    StarterInfo starter = new StarterInfo();
                    starter.setGroupId(dependency.getGroupId());
                    starter.setArtifactId(artifactId);
                    starter.setVersion(version);
                    starters.add(starter);
                }
            }
        }
        
        return starters;
    }

    private static String buildMetadataUrl(String artifactId) {
        String groupPath = RemoteMavenUtil.GROUP_ID.replace('.', '/');
        return MAVEN_REPO_BASE_URL + groupPath + "/" + artifactId + "/maven-metadata.xml";
    }

    private static String buildPomUrl(String groupId, String artifactId, String version) {
        String groupPath = groupId.replace('.', '/');
        return MAVEN_REPO_BASE_URL + groupPath + "/" + artifactId + "/" + version + "/" + 
               artifactId + "-" + version + ".pom";
    }

    private static String fetchUrl(String url) throws Exception {
        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("Failed to fetch URL: " + url + ", response code: " + response.code());
            }
            
            if (response.body() == null) {
                throw new Exception("Empty response body for URL: " + url);
            }
            
            return response.body().string();
        }
    }


}
