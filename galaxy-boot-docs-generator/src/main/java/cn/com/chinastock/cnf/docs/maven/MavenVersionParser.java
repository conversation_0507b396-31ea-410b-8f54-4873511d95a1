package cn.com.chinastock.cnf.docs.maven;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Maven 版本解析器
 * 负责解析 maven-metadata.xml 并提供版本相关功能
 */
public class MavenVersionParser {
    
    /**
     * 从 maven-metadata.xml 内容中解析最新版本
     * 
     * @param metadataXml maven-metadata.xml 内容
     * @return 最新版本号
     * @throws Exception 如果解析失败
     */
    public static String parseLatestVersion(String metadataXml) throws Exception {
        Document document = DocumentHelper.parseText(metadataXml);
        Element root = document.getRootElement();
        
        Element versioning = root.element("versioning");
        if (versioning == null) {
            throw new Exception("No versioning element found in metadata");
        }
        
        Element release = versioning.element("release");
        if (release != null) {
            return release.getTextTrim();
        }
        
        Element latest = versioning.element("latest");
        if (latest != null) {
            return latest.getTextTrim();
        }
        
        // 如果没有 release 或 latest，取 versions 中的最后一个
        Element versions = versioning.element("versions");
        if (versions != null) {
            List<Element> versionList = versions.elements("version");
            if (!versionList.isEmpty()) {
                return versionList.getLast().getTextTrim();
            }
        }
        
        throw new Exception("No version found in metadata");
    }
    
    /**
     * 从 maven-metadata.xml 内容中解析最新稳定版本
     * 排除 alpha、beta、rc、snapshot 等预发布版本
     * 
     * @param metadataXml maven-metadata.xml 内容
     * @return 最新稳定版本号
     * @throws Exception 如果解析失败或没有找到稳定版本
     */
    public static String parseLatestStableVersion(String metadataXml) throws Exception {
        List<String> allVersions = parseAllVersions(metadataXml);
        
        // 过滤出稳定版本并按版本号排序
        List<String> stableVersions = allVersions.stream()
                .filter(version -> !VersionComparator.isPreRelease(version))
                .sorted(VersionComparator::compare)
                .toList();
        
        if (stableVersions.isEmpty()) {
            throw new Exception("No stable version found in metadata");
        }
        
        return stableVersions.getLast();
    }
    
    /**
     * 从 maven-metadata.xml 内容中解析所有版本
     * 
     * @param metadataXml maven-metadata.xml 内容
     * @return 所有版本列表
     * @throws Exception 如果解析失败
     */
    public static List<String> parseAllVersions(String metadataXml) throws Exception {
        Document document = DocumentHelper.parseText(metadataXml);
        Element root = document.getRootElement();
        
        Element versioning = root.element("versioning");
        if (versioning == null) {
            throw new Exception("No versioning element found in metadata");
        }
        
        Element versions = versioning.element("versions");
        if (versions == null) {
            throw new Exception("No versions element found in metadata");
        }
        
        List<Element> versionElements = versions.elements("version");
        if (versionElements.isEmpty()) {
            throw new Exception("No version found in metadata");
        }
        
        List<String> allVersions = new ArrayList<>();
        for (Element versionElement : versionElements) {
            String version = versionElement.getTextTrim();
            if (version != null && !version.isEmpty()) {
                allVersions.add(version);
            }
        }
        
        return allVersions;
    }
    
    /**
     * 从 maven-metadata.xml 内容中解析稳定版本列表
     * 排除 alpha、beta、rc、snapshot 等预发布版本
     * 
     * @param metadataXml maven-metadata.xml 内容
     * @return 稳定版本列表，按版本号排序
     * @throws Exception 如果解析失败
     */
    public static List<String> parseStableVersions(String metadataXml) throws Exception {
        List<String> allVersions = parseAllVersions(metadataXml);
        
        return allVersions.stream()
                .filter(version -> !VersionComparator.isPreRelease(version))
                .sorted(VersionComparator::compare)
                .collect(Collectors.toList());
    }
    
    /**
     * 从 maven-metadata.xml 内容中解析预发布版本列表
     * 包含 alpha、beta、rc、snapshot 等预发布版本
     * 
     * @param metadataXml maven-metadata.xml 内容
     * @return 预发布版本列表，按版本号排序
     * @throws Exception 如果解析失败
     */
    public static List<String> parsePreReleaseVersions(String metadataXml) throws Exception {
        List<String> allVersions = parseAllVersions(metadataXml);
        
        return allVersions.stream()
                .filter(VersionComparator::isPreRelease)
                .sorted(VersionComparator::compare)
                .collect(Collectors.toList());
    }
}
