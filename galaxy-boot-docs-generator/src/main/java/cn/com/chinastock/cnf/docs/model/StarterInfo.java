package cn.com.chinastock.cnf.docs.model;

/**
 * Starter 信息模型
 */
public class StarterInfo {
    private String artifactId;
    private String groupId;
    private String version;
    private boolean existsInJdk21;
    private boolean existsInJdk8;

    public StarterInfo() {
    }

    public String getArtifactId() {
        return artifactId;
    }

    public void setArtifactId(String artifactId) {
        this.artifactId = artifactId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public boolean isExistsInJdk21() {
        return existsInJdk21;
    }

    public void setExistsInJdk21(boolean existsInJdk21) {
        this.existsInJdk21 = existsInJdk21;
    }

    public boolean isExistsInJdk8() {
        return existsInJdk8;
    }

    public void setExistsInJdk8(boolean existsInJdk8) {
        this.existsInJdk8 = existsInJdk8;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        StarterInfo that = (StarterInfo) o;

        if (!artifactId.equals(that.artifactId)) return false;
        return groupId.equals(that.groupId);
    }

    @Override
    public int hashCode() {
        int result = artifactId.hashCode();
        result = 31 * result + groupId.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "StarterInfo{" +
                "artifactId='" + artifactId + '\'' +
                ", groupId='" + groupId + '\'' +
                ", version='" + version + '\'' +
                ", existsInJdk21=" + existsInJdk21 +
                ", existsInJdk8=" + existsInJdk8 +
                '}';
    }
}
