package cn.com.chinastock.cnf.docs.maven;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 版本比较工具类
 * 支持语义化版本比较和预发布版本识别
 */
public class VersionComparator {
    
    // 预发布版本标识符模式
    private static final Pattern PRE_RELEASE_PATTERN = Pattern.compile(
        ".*[-.]?(alpha|beta|rc|snapshot|m|milestone|preview|pre|dev)\\d*$", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 判断是否为预发布版本
     * 
     * @param version 版本号
     * @return true 如果是预发布版本
     */
    public static boolean isPreRelease(String version) {
        if (version == null || version.trim().isEmpty()) {
            return false;
        }
        return PRE_RELEASE_PATTERN.matcher(version.trim()).matches();
    }
    
    /**
     * 比较两个版本号
     * 
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    public static int compare(String version1, String version2) {
        if (version1 == null && version2 == null) {
            return 0;
        }
        if (version1 == null) {
            return -1;
        }
        if (version2 == null) {
            return 1;
        }
        
        // 解析版本号
        VersionParts parts1 = parseVersion(version1);
        VersionParts parts2 = parseVersion(version2);
        
        // 比较数字部分
        int result = compareNumericParts(parts1.numericParts, parts2.numericParts);
        if (result != 0) {
            return result;
        }
        
        // 如果数字部分相同，比较预发布标识符
        return comparePreReleaseParts(parts1.preReleasePart, parts2.preReleasePart);
    }
    
    /**
     * 解析版本号为数字部分和预发布部分
     *
     * @param version 版本号
     * @return 版本号组成部分
     */
    private static VersionParts parseVersion(String version) {
        String cleanVersion = version.trim();
        
        // 分离数字部分和预发布部分
        String[] mainAndPre = splitMainAndPreRelease(cleanVersion);
        String mainPart = mainAndPre[0];
        String preReleasePart = mainAndPre[1];
        
        // 解析数字部分
        String[] numericStrings = mainPart.split("\\.");
        int[] numericParts = new int[numericStrings.length];
        
        for (int i = 0; i < numericStrings.length; i++) {
            try {
                numericParts[i] = Integer.parseInt(numericStrings[i]);
            } catch (NumberFormatException e) {
                numericParts[i] = 0;
            }
        }
        
        return new VersionParts(numericParts, preReleasePart);
    }
    
    /**
     * 分离主版本号和预发布标识符
     *
     * @param version 版本号
     * @return 包含主版本号和预发布标识符的数组
     */
    private static String[] splitMainAndPreRelease(String version) {
        // 查找预发布标识符的位置
        String lowerVersion = version.toLowerCase();
        String[] preReleaseIdentifiers = {"alpha", "beta", "rc", "snapshot", "m", "milestone", "preview", "pre", "dev"};
        
        for (String identifier : preReleaseIdentifiers) {
            int index = lowerVersion.indexOf("-" + identifier);
            if (index == -1) {
                index = lowerVersion.indexOf("." + identifier);
            }
            if (index != -1) {
                return new String[]{version.substring(0, index), version.substring(index + 1)};
            }
        }
        
        return new String[]{version, ""};
    }
    
    /**
     * 比较数字部分
     *
     * @param parts1 版本1的数字部分
     * @param parts2 版本2的数字部分
     * @return 比较结果
     */
    private static int compareNumericParts(int[] parts1, int[] parts2) {
        int maxLength = Math.max(parts1.length, parts2.length);
        
        for (int i = 0; i < maxLength; i++) {
            int part1 = i < parts1.length ? parts1[i] : 0;
            int part2 = i < parts2.length ? parts2[i] : 0;
            
            if (part1 != part2) {
                return Integer.compare(part1, part2);
            }
        }
        
        return 0;
    }
    
    /**
     * 比较预发布部分
     * 稳定版本 > 预发布版本
     *
     * @param pre1 版本1的预发布部分
     * @param pre2 版本2的预发布部分
     * @return 比较结果
     */
    private static int comparePreReleaseParts(String pre1, String pre2) {
        boolean isEmpty1 = pre1 == null || pre1.isEmpty();
        boolean isEmpty2 = pre2 == null || pre2.isEmpty();
        
        if (isEmpty1 && isEmpty2) {
            return 0;
        }
        if (isEmpty1) {
            return 1; // 稳定版本 > 预发布版本
        }
        if (isEmpty2) {
            return -1; // 预发布版本 < 稳定版本
        }
        
        // 两个都是预发布版本，按字符串比较
        return pre1.compareToIgnoreCase(pre2);
    }
    
    /**
     * 版本号组成部分
     */
    private static class VersionParts {
        final int[] numericParts;
        final String preReleasePart;
        
        VersionParts(int[] numericParts, String preReleasePart) {
            this.numericParts = numericParts;
            this.preReleasePart = preReleasePart;
        }
    }
}
