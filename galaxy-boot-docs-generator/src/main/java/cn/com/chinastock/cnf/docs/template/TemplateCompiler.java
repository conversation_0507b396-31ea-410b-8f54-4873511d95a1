package cn.com.chinastock.cnf.docs.template;

import freemarker.template.*;
import freemarker.cache.StringTemplateLoader;
import java.io.*;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * TemplateCompiler 类用于处理模板编译任务，包括将模板内容编译成文件以及加载模板文件的内容。
 */
public class TemplateCompiler {
    private static final String TEMPLATE_NAME = "default";
    private static final String ENCODING = "UTF-8";

    /**
     * 将给定的模板内容编译成文件。
     *
     * <pre>
     *    {@code
     *        String templateContent = "#if (key) { ${key} }";
     *        File outputFile = new File("/path/to/output/file");
     *        Properties parentProperties = new Properties();
     *        parentProperties.setProperty("key", "value");
     *        TemplateCompiler.compileTemplate(templateContent, outputFile, parentProperties);
     *    }
     *    </pre>
     *
     * @param templateContent 要编译的模板内容字符串
     * @param outputFile 编译后输出的文件对象
     * @param parentProperties 用于模板替换的属性集合
     */
    public static void compileTemplate(String templateContent, File outputFile, Properties parentProperties) {
        Configuration configuration = createConfiguration(templateContent);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            Template template = configuration.getTemplate(TEMPLATE_NAME);
            template.process(parentProperties, writer);
        } catch (TemplateException | IOException e) {
            throw new RuntimeException("Error processing template", e);
        }
    }

    /**
     * 加载用户手册模板的内容。
     *
     * <pre>
     *    {@code
     *        File templateFile = new File("/path/to/template");
     *        String templateContent = loadTemplateContent(templateFile);
     *        // 使用 templateContent 进行后续处理
     *    }
     * </pre>
     *
     * @param userManualTemplate 指定的用户手册模板文件
     * @return 返回模板文件的字符串内容
     * @throws IOException 如果读取模板文件时发生错误
     */
    public static String loadTemplateContent(File userManualTemplate) throws IOException {
        String templateContent;
        try (BufferedReader br = new BufferedReader(new FileReader(userManualTemplate))) {
            templateContent = br.lines().collect(Collectors.joining(System.lineSeparator()));
        }

        return templateContent;
    }

    private static Configuration createConfiguration(String templateContent) {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_33);
        configuration.setDefaultEncoding(ENCODING);

        StringTemplateLoader templateLoader = new StringTemplateLoader();
        templateLoader.putTemplate(TEMPLATE_NAME, templateContent);
        configuration.setTemplateLoader(templateLoader);

        return configuration;
    }

    /**
     * Compiles a template file into an output file using provided parent properties.
     *
     * <pre>
     *    {@code
     *        File templateFile = new File("/path/to/template.vm");
     *        File outputFile = new File("/path/to/output.txt");
     *        Properties parentProperties = new Properties();
     *        parentProperties.setProperty("key", "value");
     *        TemplateCompiler.compile(templateFile, outputFile, parentProperties);
     *    }
     * </pre>
     *
     * @param templateFile The input template file to be compiled.
     * @param outputFile The output file where the compiled content will be written.
     * @param parentProperties The parent properties to be used during the compilation process.
     */
    public static void compile(File templateFile, File outputFile, Properties parentProperties) {
        try {
            String templateContent = loadTemplateContent(templateFile);
            compileTemplate(templateContent, outputFile, parentProperties);
        } catch (IOException e) {
            throw new RuntimeException("Error loading template content", e);
        }
    }
}
