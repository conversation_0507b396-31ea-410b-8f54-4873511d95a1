package cn.com.chinastock.cnf.docs;

import cn.com.chinastock.cnf.docs.confluence.ConfluenceUploader;
import cn.com.chinastock.cnf.docs.confluence.PageMapping;
import cn.com.chinastock.cnf.docs.maven.MavenUtil;
import cn.com.chinastock.cnf.docs.template.TemplateCompiler;
import cn.com.chinastock.cnf.docs.template.TemplateValuePreparer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

import java.io.*;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class Main {

    public static final String POM_XML = "pom.xml";

    public static void main(String[] args) throws Exception {
        Model model = new MavenXpp3Reader().read(new FileReader(POM_XML));
        File rootDir = model.getProjectDirectory();

        System.out.println("1. 生成主用户手册...");
        generateMainUserManual(rootDir);

        System.out.println("2. 生成各模块用户手册...");
        ModuleUserManualGenerator.generate(rootDir, model);

        System.out.println("3. 复制 CHANGELOG.md...");
        copyChangelog(rootDir);

        System.out.println("4. 生成JDK8和JDK21版本对比文档...");
        ModuleUserManualGenerator.generateVersionComparison(rootDir);

        System.out.println("5. 上传 Confluence...");
        HashMap<String, PageMapping> pageInfos = readConfluenceConfig(rootDir);
        System.out.println("Confluence pageInfos: " + pageInfos);

        String USERNAME = System.getenv("CONFLUENCE_USERNAME");
        String PASSWORD = System.getenv("CONFLUENCE_PASSWORD");

        ConfluenceUploader confluenceUploader = new ConfluenceUploader(USERNAME, PASSWORD);
        if (USERNAME == null || PASSWORD == null) {
            throw new RuntimeException("Please set CONFLUENCE_USERNAME and CONFLUENCE_PASSWORD environment variables");
        }

        confluenceUploader.upload(pageInfos);
        System.out.println("Done.");
    }

    private static HashMap<String, PageMapping> readConfluenceConfig(File rootDir) throws IOException {
        File jsonFile = new File(rootDir, "docs" + File.separatorChar + "Confluence.json");
        ObjectMapper objectMapper = new ObjectMapper();
        TypeReference<HashMap<String, PageMapping>> typeReference = new TypeReference<>() {

        };
        HashMap<String, PageMapping> pageInfos = objectMapper.readValue(jsonFile, typeReference);
        return pageInfos;
    }

    private static void generateMainUserManual(File rootDir) throws Exception {
        File parentPom = new File(rootDir, "galaxy-boot-parent" + File.separatorChar + "pom.xml");
        Model parentModel = new MavenXpp3Reader().read(new FileReader(parentPom));
        Properties parentProperties = parentModel.getProperties();

        // 1. 遍历所有模块
        Map<Path, Model> pathModelMap = MavenUtil.iterateAllModules(new File(POM_XML));

        // 2. 准备模板所需的属性
        Properties docProperties = TemplateValuePreparer.prepareProperties(pathModelMap, rootDir);
        parentProperties.putAll(docProperties);

        // 3. 使用 Freemarker 编译模板
        File templateFile = new File(rootDir, "docs" + File.separatorChar + "UserManual.template.md");
        File outputFile = new File(rootDir, "docs" + File.separatorChar + "UserManual.md");
        TemplateCompiler.compile(templateFile, outputFile, parentProperties);
    }


    private static void copyChangelog(File rootDir) throws InterruptedException, IOException {
        File changelog = new File(rootDir, "CHANGELOG.md");
        if (!changelog.exists()) {
            System.out.println("CHANGELOG.md not found, generating...");
            Process process;
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                process = Runtime.getRuntime().exec("mvnw.cmd generate-resources", null, rootDir);
                process.waitFor();
            } else {
                process = Runtime.getRuntime().exec("./mvnw generate-resources", null, rootDir);
                process.waitFor();
            }
            process.waitFor();
        }

        // copy from root/CHANGELOG.md to root/docs/CHANGELOG.md
        File target = new File(rootDir, "docs" + File.separatorChar + "CHANGELOG.md");
        if (target.exists()) {
            target.delete();
        }

        java.nio.file.Files.copy(changelog.toPath(), target.toPath());
    }
}
