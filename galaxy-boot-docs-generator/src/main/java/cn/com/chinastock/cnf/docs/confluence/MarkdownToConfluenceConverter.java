package cn.com.chinastock.cnf.docs.confluence;

import com.vladsch.flexmark.ast.FencedCodeBlock;
import com.vladsch.flexmark.ast.Link;
import com.vladsch.flexmark.ext.gfm.strikethrough.StrikethroughExtension;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.html.HtmlWriter;
import com.vladsch.flexmark.html.renderer.NodeRenderer;
import com.vladsch.flexmark.html.renderer.NodeRendererContext;
import com.vladsch.flexmark.html.renderer.NodeRenderingHandler;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.profile.pegdown.Extensions;
import com.vladsch.flexmark.profile.pegdown.PegdownOptionsAdapter;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.DataHolder;
import com.vladsch.flexmark.util.data.MutableDataHolder;
import com.vladsch.flexmark.util.data.MutableDataSet;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class MarkdownToConfluenceConverter {
    final private static DataHolder OPTIONS = PegdownOptionsAdapter.flexmarkOptions(true,
            Extensions.ALL
    );

    /**
     * 将Markdown文本转换为Confluence友好的Html格式。
     *
     * <pre>
     *    {@code
     *        String htmlContent = MarkdownToConfluenceConverter.convert("# This is a heading");
     *        // htmlContent 包含转换后的HTML表示形式，适用于Confluence显示
     *    }
     * </pre>
     *
     * @param markdown 要转换的Markdown字符串
     * @return 返回转换后的Html字符串，适用于Confluence显示
     */
    public static String convert(String markdown) {
        MutableDataSet options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, Arrays.asList(
                StrikethroughExtension.create(),
                TablesExtension.create(),
                JiraLinkExtension.create(),
                PlantUmlExtension.create(),
                CodeBlockExtension.create()
        ));
        
        options.set(HtmlRenderer.PERCENT_ENCODE_URLS, true);
        options.set(HtmlRenderer.SOFT_BREAK, "\n");

        MutableDataSet allOptions = MutableDataSet.merge(OPTIONS, options);

        Parser parser = Parser.builder(allOptions).build();
        HtmlRenderer renderer = HtmlRenderer.builder(allOptions).build();

        Node document = parser.parse(markdown);
        return renderer.render(document);
    }
}

class JiraLinkExtension implements HtmlRenderer.HtmlRendererExtension {
    public static JiraLinkExtension create() {
        return new JiraLinkExtension();
    }

    @Override
    public void rendererOptions(final @NotNull MutableDataHolder options) {
    }

    @Override
    public void extend(final HtmlRenderer.Builder rendererBuilder, final @NotNull String rendererType) {
        rendererBuilder.nodeRendererFactory(JiraLinkNodeRenderer::new);
    }
}

class JiraLinkNodeRenderer implements NodeRenderer {

    public JiraLinkNodeRenderer(@NotNull DataHolder dataHolder) {
    }

    @Override
    public Set<NodeRenderingHandler<?>> getNodeRenderingHandlers() {
        HashSet<NodeRenderingHandler<?>> set = new HashSet<>();
        set.add(new NodeRenderingHandler<>(Link.class, this::render));
        return set;
    }

    private void render(Link node, NodeRendererContext context, HtmlWriter html) {
        String url = node.getUrl().toString();

        if (url.matches("https?://pmc\\.chinastock\\.com\\.cn/jira/browse/[a-zA-Z]+-\\d+")) {
            String issueKey = url.substring(url.lastIndexOf('/') + 1);
            String macroId = UUID.randomUUID().toString();

            String sb = """
                    <ac:structured-macro ac:macro-id="%s" ac:name="jira" ac:schema-version="1">
                        <ac:parameter ac:name="server">JIRA</ac:parameter>
                        <ac:parameter ac:name="columnIds">issuekey,summary,issuetype,created,updated,duedate,assignee,reporter,priority,status,resolution</ac:parameter>
                        <ac:parameter ac:name="columns">key,summary,type,created,updated,due,assignee,reporter,priority,status,resolution</ac:parameter>
                        <ac:parameter ac:name="serverId">34a7e255-c22c-322a-9f76-0f2c5871bd67</ac:parameter>
                        <ac:parameter ac:name="key">%s</ac:parameter>
                    </ac:structured-macro>
                    """.formatted(macroId, issueKey);
            html.raw(sb);
        } else {
            // Default rendering
            context.delegateRender();
        }
    }
}

class PlantUmlExtension implements HtmlRenderer.HtmlRendererExtension {
    public static PlantUmlExtension create() {
        return new PlantUmlExtension();
    }

    @Override
    public void rendererOptions(final @NotNull MutableDataHolder options) {
    }

    @Override
    public void extend(final HtmlRenderer.Builder rendererBuilder, final @NotNull String rendererType) {
        rendererBuilder.nodeRendererFactory(PlantUmlNodeRenderer::new);
    }
}

class PlantUmlNodeRenderer implements NodeRenderer {
    public PlantUmlNodeRenderer(@NotNull DataHolder dataHolder) {

    }

    @Override
    public Set<NodeRenderingHandler<?>> getNodeRenderingHandlers() {
        HashSet<NodeRenderingHandler<?>> set = new HashSet<>();
        set.add(new NodeRenderingHandler<>(FencedCodeBlock.class, this::render));
        return set;
    }

    private void render(FencedCodeBlock node, NodeRendererContext context, HtmlWriter html) {
        String info = node.getInfo().toString().trim();

        if ("plantuml".equalsIgnoreCase(info)) {
            String code = node.getContentChars().normalizeEOL();

            String macroId = UUID.randomUUID().toString();

            StringBuilder sb = new StringBuilder();
            sb.append("""
                    <ac:structured-macro ac:macro-id="%s" ac:name="plantumlrender" ac:schema-version="1">
                        <ac:rich-text-body>
                    """.formatted(macroId));

            String[] lines = code.split("\n");
            for (String line : lines) {
                sb.append("       <p>").append(escapeHtml(line)).append("</p>\n");
            }

            sb.append("""
                        </ac:rich-text-body>
                    </ac:structured-macro>
                    """);

            html.raw(sb.toString());
        } else {
            // Default rendering
            context.delegateRender();
        }
    }

    private String escapeHtml(String line) {
        return line
                .replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }
}

class CodeBlockExtension implements HtmlRenderer.HtmlRendererExtension {
    public static CodeBlockExtension create() {
        return new CodeBlockExtension();
    }

    @Override
    public void rendererOptions(final @NotNull MutableDataHolder options) {
    }

    @Override
    public void extend(final HtmlRenderer.Builder rendererBuilder, final @NotNull String rendererType) {
        rendererBuilder.nodeRendererFactory(CodeBlockNodeRenderer::new);
    }
}

class CodeBlockNodeRenderer implements NodeRenderer {
    public CodeBlockNodeRenderer(@NotNull DataHolder dataHolder) {
    }

    @Override
    public Set<NodeRenderingHandler<?>> getNodeRenderingHandlers() {
        HashSet<NodeRenderingHandler<?>> set = new HashSet<>();
        set.add(new NodeRenderingHandler<>(FencedCodeBlock.class, this::render));
        return set;
    }

    private void render(FencedCodeBlock node, NodeRendererContext context, HtmlWriter html) {
        String language = node.getInfo().toString().trim();
        String code = node.getContentChars().normalizeEOL();
        String macroId = UUID.randomUUID().toString();

        if ("plantuml".equalsIgnoreCase(language)) {
            context.delegateRender();
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("""
                <ac:structured-macro ac:macro-id="%s" ac:name="code" ac:schema-version="1">""", macroId));
        
        // 只在 language 不为空时添加语言参数
        if (!language.isEmpty()) {
            sb.append(String.format("""
                
                <ac:parameter ac:name="language">%s</ac:parameter>""", language));

            sb.append("""
                <ac:parameter ac:name="theme">Midnight</ac:parameter>""");
        }
        
        sb.append("""
                
                <ac:plain-text-body><![CDATA[""");
        
        sb.append(code);
        
        sb.append("]]></ac:plain-text-body>\n</ac:structured-macro>");

        html.rawIndentedPre(sb.toString());
    }
}