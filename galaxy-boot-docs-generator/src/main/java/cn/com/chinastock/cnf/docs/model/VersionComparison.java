package cn.com.chinastock.cnf.docs.model;

import java.util.List;

/**
 * 版本对比结果模型
 */
public class VersionComparison {
    private String jdk21Version;
    private String jdk8Version;
    private List<StarterInfo> allStarters;
    private List<StarterInfo> jdk21OnlyStarters;
    private List<StarterInfo> jdk8OnlyStarters;

    public VersionComparison() {
    }

    public String getJdk21Version() {
        return jdk21Version;
    }

    public void setJdk21Version(String jdk21Version) {
        this.jdk21Version = jdk21Version;
    }

    public String getJdk8Version() {
        return jdk8Version;
    }

    public void setJdk8Version(String jdk8Version) {
        this.jdk8Version = jdk8Version;
    }

    public List<StarterInfo> getAllStarters() {
        return allStarters;
    }

    public void setAllStarters(List<StarterInfo> allStarters) {
        this.allStarters = allStarters;
    }

    public List<StarterInfo> getJdk21OnlyStarters() {
        return jdk21OnlyStarters;
    }

    public void setJdk21OnlyStarters(List<StarterInfo> jdk21OnlyStarters) {
        this.jdk21OnlyStarters = jdk21OnlyStarters;
    }

    public void setJdk8OnlyStarters(List<StarterInfo> jdk8OnlyStarters) {
        this.jdk8OnlyStarters = jdk8OnlyStarters;
    }

    @Override
    public String toString() {
        return "VersionComparison{" +
                "jdk21Version='" + jdk21Version + '\'' +
                ", jdk8Version='" + jdk8Version + '\'' +
                ", allStarters=" + (allStarters != null ? allStarters.size() : 0) +
                ", jdk21OnlyStarters=" + (jdk21OnlyStarters != null ? jdk21OnlyStarters.size() : 0) +
                ", jdk8OnlyStarters=" + (jdk8OnlyStarters != null ? jdk8OnlyStarters.size() : 0) +
                '}';
    }
}
