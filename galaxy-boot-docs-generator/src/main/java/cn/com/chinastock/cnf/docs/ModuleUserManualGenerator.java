package cn.com.chinastock.cnf.docs;

import cn.com.chinastock.cnf.docs.javadoc.JavaCommentParser;
import cn.com.chinastock.cnf.docs.javadoc.PackageComment;
import cn.com.chinastock.cnf.docs.maven.MavenUtil;
import cn.com.chinastock.cnf.docs.maven.RemoteMavenUtil;
import cn.com.chinastock.cnf.docs.model.StarterInfo;
import cn.com.chinastock.cnf.docs.model.VersionComparison;
import org.apache.maven.model.Model;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.stream.Collectors;

public class ModuleUserManualGenerator {
    public static final String CORE_PREFIX = "galaxy-boot-core-";
    public static final String CORE_MODULE = "galaxy-boot-core";

    public static void generate(File rootDir, Model model) throws Exception {
        Map<Path, Model> pathModelMap = MavenUtil.iterateAllModules(new File("pom.xml"));

        pathModelMap.forEach((path, childModel) -> {
            Path moduleDir = path.getParent();
            if (moduleDir == null) {
                return;
            }

            File file = new File(rootDir, moduleDir.toString());
            List<PackageComment> markdownFiles = JavaCommentParser.processJavaFiles(file);

            String moduleName = childModel.getArtifactId();
            try {
                if (!CORE_MODULE.equals(moduleName)) {
                    handleForNormalModule(file, markdownFiles, rootDir, moduleName);
                } else {
                    handleForCoreModule(file, markdownFiles, rootDir, moduleName);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * Security, Starters, Test, Utils 模块的逻辑
     *
     * <p>1. 读取 {moduleName}/README.md 文件作为前端的 【组件介绍】      </p>
     * <p>2. 读取 {moduleName}/docs/UserManual.md 来生成 【组件实例教程】</p>
     * <p>3.1 读取<strong>类级别</strong>注释来生成 【组件样例文档】        </p>
     * <p>3.2 读取<strong>类-方法级别</strong>注释来生成 【组件 API 列表】  </p>
     *
     * @param file          模块目录
     * @param markdownFiles Java 文件注释列表
     * @param rootDir       项目根目录
     * @param moduleName    模块名称
     */
    private static void handleForNormalModule(File file, List<PackageComment> markdownFiles, File rootDir, String moduleName) throws IOException {
        StringBuilder markdownFilesBuilder = new StringBuilder();

        // 1. 读取 README.md
        File readmeFile = new File(file, "README.md");
        if (!readmeFile.exists()) {
            return;
        }

        String readmeContent = new String(Files.readAllBytes(readmeFile.toPath()));
        markdownFilesBuilder.append(readmeContent);

        // 2. 读取 docs/UserManual.md
        File userManualFile = new File(file, "docs" + File.separatorChar + "UserManual.md");
        if (userManualFile.exists()) {
            String userManualContent = new String(Files.readAllBytes(userManualFile.toPath()));
            markdownFilesBuilder.append(userManualContent);
        }

        // 3. 生成类注释
        if (!markdownFiles.isEmpty()) {
            assembleMarkdownFromComments(markdownFiles, markdownFilesBuilder);
        }

        File outputFile = new File(rootDir, "docs" + File.separatorChar + moduleName + ".md");
        Files.write(outputFile.toPath(), markdownFilesBuilder.toString().getBytes());
    }

    /**
     * Core 模块的逻辑
     * 1. 复制 {moduleName}/README.md 到 docs/galaxy-boot-core.md
     * 2. 复制 {moduleName}/docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}.md 到 docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}.md
     * 3. 根据 packageSuffix 过滤出对应的 markdownFiles
     * 4. 生成 {packageSuffix} 对应的类注释，方法注释
     *
     * @param file          模块目录
     * @param markdownFiles Java 文件注释列表
     * @param rootDir       项目根目录
     * @param moduleName    模块名称
     */
    private static void handleForCoreModule(File file, List<PackageComment> markdownFiles, File rootDir, String moduleName) throws IOException {
        copyCoreModuleReadme(file, rootDir, moduleName);

        File[] coreDocFiles = new File(file, "docs").listFiles();
        assert coreDocFiles != null;

        // 复制 {moduleName}/docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}
        for (File coreDocFile : coreDocFiles) {
            String fileName = coreDocFile.getName();
            if (fileName.startsWith(CORE_PREFIX) && fileName.endsWith(".md")) {
                ///  创建 docs/galaxy-boot-core
                File coreDir = new File(rootDir, "docs" + File.separatorChar + CORE_MODULE);
                if (!coreDir.exists()) {
                    coreDir.mkdirs();
                }

                String packageSuffix = fileName.substring(CORE_PREFIX.length(), fileName.length() - ".md".length());

                File targetFile = overrideOriginalFileToDocs(rootDir, coreDocFile, fileName);

                // 根据 packageSuffix 过滤出对应的 markdownFiles
                List<PackageComment> filteredMarkdownFiles = markdownFiles.stream()
                        .filter(pkgComment -> pkgComment.packageName.endsWith(packageSuffix))
                        .toList();

                // 添加类注释，方法注释
                StringBuilder markdownFilesBuilder = new StringBuilder();
                assembleMarkdownFromComments(filteredMarkdownFiles, markdownFilesBuilder);
                Files.write(targetFile.toPath(), markdownFilesBuilder.toString().getBytes(), StandardOpenOption.APPEND);
            }
        }
    }

    private static File overrideOriginalFileToDocs(File rootDir, File coreDocFile, String fileName) throws IOException {
        File targetFile = new File(rootDir, "docs" + File.separatorChar + CORE_MODULE + File.separatorChar + fileName);
        if (targetFile.exists()) {
            targetFile.delete();
        }
        Files.write(targetFile.toPath(), Files.readAllBytes(coreDocFile.toPath()));
        return targetFile;
    }

    /**
     * 复制 {moduleName}/README.md 到 docs/galaxy-boot-core.md
     *
     * @param file       模块目录
     * @param rootDir    项目根目录
     * @param moduleName 模块名称
     */
    private static void copyCoreModuleReadme(File file, File rootDir, String moduleName) throws IOException {
        File readmeFile = new File(file, "README.md");
        String readmeContent = new String(Files.readAllBytes(readmeFile.toPath()));
        File outputFile = new File(rootDir, "docs" + File.separatorChar + moduleName + ".md");
        Files.write(outputFile.toPath(), readmeContent.getBytes());
    }

    private static void assembleMarkdownFromComments(List<PackageComment> markdownFiles, StringBuilder markdownFilesBuilder) {
        markdownFilesBuilder.append("\n\n### 组件样例文档\n\n");
        markdownFiles.forEach(packageComment -> markdownFilesBuilder.append(packageComment.classComment).append("\n"));

        // 4. 生成方法注释
        markdownFilesBuilder.append("\n\n### 组件 API 列表\n\n");
        markdownFiles.forEach(packageComment -> {
            markdownFilesBuilder.append("#### ").append(packageComment.className).append("\n\n");
            packageComment.methodComments.forEach(methodComment -> markdownFilesBuilder.append(methodComment).append("\n"));
        });
    }

    /**
     * 生成 JDK8 和 JDK21 版本对比文档
     *
     * @param rootDir 项目根目录
     * @return 对比结果的 Markdown 文档
     * @throws Exception 如果生成失败
     */
    public static String generateVersionComparison(File rootDir) throws Exception {
        System.out.println("开始生成 JDK8 和 JDK21 版本对比文档...");
        try {
            // 1. 获取远程 JDK21 版本的 starter 列表
            String jdk21Version = RemoteMavenUtil.getLatestStableJdk21Version();
            System.out.println("JDK21 最新版本: " + jdk21Version);

            String jdk21PomContent = RemoteMavenUtil.getJdk21DependenciesPom(jdk21Version);
            List<StarterInfo> jdk21Starters = RemoteMavenUtil.extractStartersFromPom(jdk21PomContent, jdk21Version);
            System.out.println("获取到 JDK21 starters: " + jdk21Starters.size() + " 个");

            // 2. 获取远程 JDK8 版本的 starter 列表
            String jdk8Version = RemoteMavenUtil.getLatestStableJdk8Version();
            System.out.println("JDK8 最新版本: " + jdk8Version);

            String jdk8PomContent = RemoteMavenUtil.getJdk8DependenciesPom(jdk8Version);
            List<StarterInfo> jdk8Starters = RemoteMavenUtil.extractStartersFromPom(jdk8PomContent, jdk8Version);
            System.out.println("获取到 JDK8 starters: " + jdk8Starters.size() + " 个");

            // 3. 生成对比结果
            VersionComparison comparison = compareVersions(jdk21Starters, jdk8Starters, jdk8Version, jdk21Version);

            // 4. 生成 Markdown 文档
            String markdownContent = generateComparisonMarkdown(comparison);
            return markdownContent;

        } catch (Exception e) {
            System.err.println("生成版本对比文档失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    private static VersionComparison compareVersions(List<StarterInfo> jdk21Starters,
                                                     List<StarterInfo> jdk8Starters,
                                                     String jdk8Version,
                                                     String jdk21Version) {
        VersionComparison comparison = new VersionComparison();
        comparison.setJdk21Version(jdk21Version);
        comparison.setJdk8Version(jdk8Version);

        // 创建标准化的 artifactId 到 StarterInfo 的映射
        Map<String, StarterInfo> jdk21Map = jdk21Starters.stream()
                .collect(Collectors.toMap(s -> normalizeArtifactId(s.getArtifactId()), s -> s, (a, b) -> a));
        Map<String, StarterInfo> jdk8Map = jdk8Starters.stream()
                .collect(Collectors.toMap(s -> normalizeArtifactId(s.getArtifactId()), s -> s, (a, b) -> a));

        // 获取所有标准化的 artifactId
        Set<String> allNormalizedIds = new HashSet<>();
        allNormalizedIds.addAll(jdk21Map.keySet());
        allNormalizedIds.addAll(jdk8Map.keySet());

        List<StarterInfo> allStarters = new ArrayList<>();
        List<StarterInfo> jdk21OnlyStarters = new ArrayList<>();
        List<StarterInfo> jdk8OnlyStarters = new ArrayList<>();
        List<StarterInfo> commonStarters = new ArrayList<>();

        for (String normalizedId : allNormalizedIds) {
            StarterInfo jdk21Starter = jdk21Map.get(normalizedId);
            StarterInfo jdk8Starter = jdk8Map.get(normalizedId);

            StarterInfo mergedStarter = new StarterInfo();
            // 使用标准化的名称作为显示名称
            mergedStarter.setArtifactId(normalizedId);

            if (jdk21Starter != null && jdk8Starter != null) {
                // 两个版本都有
                mergedStarter.setGroupId(jdk21Starter.getGroupId());
                String jdk21Ver = normalizeVersion(jdk21Starter.getVersion(), jdk21Version);
                String jdk8Ver = jdk8Starter.getVersion();
                mergedStarter.setVersion(jdk21Ver + " / " + jdk8Ver);
                mergedStarter.setExistsInJdk21(true);
                mergedStarter.setExistsInJdk8(true);
                commonStarters.add(mergedStarter);
            } else if (jdk21Starter != null) {
                // 只有 JDK21 有
                mergedStarter.setGroupId(jdk21Starter.getGroupId());
                mergedStarter.setVersion(normalizeVersion(jdk21Starter.getVersion(), jdk21Version));
                mergedStarter.setExistsInJdk21(true);
                mergedStarter.setExistsInJdk8(false);
                jdk21OnlyStarters.add(mergedStarter);
            } else {
                // 只有 JDK8 有
                mergedStarter.setGroupId(jdk8Starter.getGroupId());
                mergedStarter.setVersion(jdk8Starter.getVersion());
                mergedStarter.setExistsInJdk21(false);
                mergedStarter.setExistsInJdk8(true);
                jdk8OnlyStarters.add(mergedStarter);
            }

            allStarters.add(mergedStarter);
        }

        // 按 artifactId 排序
        allStarters.sort(Comparator.comparing(StarterInfo::getArtifactId));
        jdk21OnlyStarters.sort(Comparator.comparing(StarterInfo::getArtifactId));
        jdk8OnlyStarters.sort(Comparator.comparing(StarterInfo::getArtifactId));
        commonStarters.sort(Comparator.comparing(StarterInfo::getArtifactId));

        comparison.setAllStarters(allStarters);
        comparison.setJdk21OnlyStarters(jdk21OnlyStarters);
        comparison.setJdk8OnlyStarters(jdk8OnlyStarters);

        return comparison;
    }

    private static String normalizeArtifactId(String artifactId) {
        if (artifactId != null && artifactId.endsWith("-jdk8")) {
            return artifactId.substring(0, artifactId.length() - 5);
        }
        return artifactId;
    }

    private static String normalizeVersion(String version, String actualVersion) {
        if (version != null && version.contains("${revision}")) {
            return actualVersion;
        }
        return version;
    }

    private static String generateComparisonMarkdown(VersionComparison comparison) {
        StringBuilder markdown = new StringBuilder();

        markdown.append("| 组件名称 | JDK 21 (`").append(comparison.getJdk21Version()).append("`) | JDK 8 (`").append(comparison.getJdk8Version()).append("`) |\n");
        markdown.append("|----------|-------|------|\n");

        for (StarterInfo starter : comparison.getAllStarters()) {
            markdown.append("| ").append(starter.getArtifactId()).append(" | ");

            if (starter.isExistsInJdk21()) {
                markdown.append("✅ ");
            } else {
                markdown.append("❌");
            }

            markdown.append(" | ");

            if (starter.isExistsInJdk8()) {
                markdown.append("✅ ");
            } else {
                markdown.append("❌");
            }

            markdown.append(" |\n");
        }

        return markdown.toString();
    }
}
