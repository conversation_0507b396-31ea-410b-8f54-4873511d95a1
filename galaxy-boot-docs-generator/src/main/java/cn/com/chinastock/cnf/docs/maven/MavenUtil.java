package cn.com.chinastock.cnf.docs.maven;

import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

import java.io.File;
import java.io.FileReader;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MavenUtil {
    /**
     * 遍历所有 Maven 模块，并返回一个包含模块路径和对应模型对象的映射。
     *
     * <pre>
     *    {@code
     *        Map<Path, Model> modules = Main.iterateAllModules(new File("path/to/pom.xml"));
     *        // Sample output:
     *        // {
     *        //     "/path/to/pom.xml" -> Model@123456
     *        //     "/path/to/module1/pom.xml" -> Model@abcdef
     *        // }
     *    }
     * </pre>
     *
     * @param pomFile 指定根模块的 POM 文件
     * @return 一个映射，包含每个模块的路径和它的 Maven 模型
     * @throws Exception 如果读取 POM 文件时发生错误
     */
    public static Map<Path, Model> iterateAllModules(File pomFile) throws Exception {
        Map<Path, Model> modules = new HashMap<>();
        if (!pomFile.exists() || !"pom.xml".equals(pomFile.getName())) {
            return modules;
        }

        MavenXpp3Reader reader = new MavenXpp3Reader();
        Model model;
        try (FileReader fileReader = new FileReader(pomFile)) {
            model = reader.read(fileReader);
        }

        modules.put(pomFile.toPath(), model);

        List<String> subModules = model.getModules();
        if (subModules != null) {
            File baseDir = pomFile.getParentFile();
            for (String moduleName : subModules) {
                File subModulePom = new File(baseDir, moduleName + "/pom.xml");
                modules.putAll(iterateAllModules(subModulePom));
            }
        }

        return modules;
    }
}
