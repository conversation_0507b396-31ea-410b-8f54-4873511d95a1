package cn.com.chinastock.cnf.docs.javadoc;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.comments.Comment;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class JavaCommentParser {
    /**
     * 处理指定目录下的所有 Java 文件，并生成相应的文档 Markdown 文件列表。
     *
     * <pre>
     *    {@code
     *        File directory = new File("/path/to/java/files");
     *        List<String> markdownFiles = JavaCommentParser.processJavaFiles(directory);
     *        // markdownFiles 包含处理后的 Markdown 文件内容
     *    }
     *    </pre>
     *
     * @param file 指定的目录文件
     * @return 包含处理后的 Markdown 文件内容的列表
     */
    public static List<PackageComment> processJavaFiles(File file) {
        List<PackageComment> markdownFiles = new ArrayList<>(List.of());
        File[] files = file.listFiles();
        if (files != null) {
            for (File f : files) {
                if (f.isDirectory()) {
                    markdownFiles.addAll(processJavaFiles(f));
                } else if (f.getName().endsWith(".java")) {
                    try {
                        String content = new String(Files.readAllBytes(f.toPath()));
                        PackageComment output = JavaCommentParser.processJavaFile(content);
                        if (output.classComment != null) {
                            markdownFiles.add(output);
                        }
                    } catch (IOException e) {
                        System.err.println("Error reading file: " + f + " - " + e.getMessage());
                    }
                }
            }
        }

        return markdownFiles;
    }

    /**
     * 处理 Java 文件内容，生成相应的 Markdown 文档。
     *
     * <pre>
     *    {@code
     *        String content = "public class Example { ... }";
     *        String markdown = JavaCommentParser.processJavaFile(content);
     *        // markdown 包含类和方法的文档
     *    }
     * </pre>
     *
     * @param content Java 文件的内容
     * @return 生成的 Markdown 文档字符串
     */
    public static PackageComment processJavaFile(String content) {
        PackageComment packageComment = new PackageComment();
        packageComment.methodComments = new ArrayList<>();

        try {
            JavaParser javaParser = new JavaParser();
            CompilationUnit compilationUnit = javaParser.parse(content).getResult().orElseThrow();

            compilationUnit.findAll(ClassOrInterfaceDeclaration.class).forEach(cls -> {
                Optional<Comment> classComment = cls.getComment();
                classComment.ifPresent(comment -> {
                    String commentContent = Arrays.stream(comment.getContent()
                                    .split("\n")).map(line -> line.replaceFirst("\\s*\\*", ""))
                            .filter(line -> !line.trim().isEmpty())
                            .reduce((a, b) -> a + "\n" + b).orElse("");

                    String convert = new JavadocToMarkdownConverter().convert(commentContent, cls.getName().toString());
                    if (convert.contains("```")) {
                        packageComment.classComment = convert;
                        packageComment.className = cls.getName().toString();
                    }
                });

                if (packageComment.classComment != null && packageComment.classComment.contains("```")) {
                    cls.getMethods()
                            .stream()
                            .filter(MethodDeclaration::isPublic)
                            .forEach(method -> {
                                String header = "##### " + cls.getName() + "#" + method.getName();
                                Optional<Comment> methodComment = method.getComment();
                                if (methodComment.isPresent()) {
                                    String methodSignature = method.getDeclarationAsString();
                                    String commentContent = Arrays.stream(methodComment.get().getContent()
                                                    .split("\n")).map(line -> line.replaceFirst("\\s*\\*", ""))
                                            .filter(line -> !line.trim().isEmpty())
                                            .reduce((a, b) -> a + "\n" + b).orElse("");
                                    packageComment.methodComments.add(header + "\n\n`" + methodSignature + "`\n\n" + new JavadocToMarkdownConverter().convert(commentContent, header));
                                }
                            });
                }
            });

            if (compilationUnit.getPackageDeclaration().isPresent()) {
                packageComment.packageName = compilationUnit.getPackageDeclaration().get().getNameAsString();
            }
        } catch (Exception e) {
            System.err.println("Error processing file: " + e.getMessage());
        }
        return packageComment;
    }
}
