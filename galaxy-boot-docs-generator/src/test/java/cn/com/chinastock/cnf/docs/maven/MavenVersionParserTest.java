package cn.com.chinastock.cnf.docs.maven;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

/**
 * MavenVersionParser 测试类
 */
public class MavenVersionParserTest {

    private static final String SAMPLE_METADATA_XML = """
        <?xml version="1.0" encoding="UTF-8"?>
        <metadata>
          <groupId>cn.com.chinastock</groupId>
          <artifactId>galaxy-boot-dependencies-jdk8</artifactId>
          <versioning>
            <release>0.0.23-alpha2</release>
            <versions>
              <version>0.0.1</version>
              <version>0.0.3</version>
              <version>0.0.7</version>
              <version>0.0.10</version>
              <version>0.0.11</version>
              <version>0.0.12</version>
              <version>0.0.13-ALPHA</version>
              <version>0.0.13</version>
              <version>0.0.14</version>
              <version>0.0.16</version>
              <version>0.0.17</version>
              <version>0.0.15</version>
              <version>0.0.18</version>
              <version>0.0.19</version>
              <version>0.0.20</version>
              <version>0.0.21</version>
              <version>0.0.22</version>
              <version>0.0.23-alpha2</version>
            </versions>
            <lastUpdated>20250423085145</lastUpdated>
          </versioning>
        </metadata>
        """;

    @Test
    public void testParseLatestVersion() throws Exception {
        String latestVersion = MavenVersionParser.parseLatestVersion(SAMPLE_METADATA_XML);
        assertEquals("0.0.23-alpha2", latestVersion);
    }

    @Test
    public void testParseLatestStableVersion() throws Exception {
        String latestStableVersion = MavenVersionParser.parseLatestStableVersion(SAMPLE_METADATA_XML);
        assertEquals("0.0.22", latestStableVersion);
    }

    @Test
    public void testParseAllVersions() throws Exception {
        List<String> allVersions = MavenVersionParser.parseAllVersions(SAMPLE_METADATA_XML);
        assertEquals(18, allVersions.size());
        assertTrue(allVersions.contains("0.0.1"));
        assertTrue(allVersions.contains("0.0.22"));
        assertTrue(allVersions.contains("0.0.23-alpha2"));
        assertTrue(allVersions.contains("0.0.13-ALPHA"));
    }

    @Test
    public void testParseStableVersions() throws Exception {
        List<String> stableVersions = MavenVersionParser.parseStableVersions(SAMPLE_METADATA_XML);
        
        // 应该排除预发布版本
        assertFalse(stableVersions.contains("0.0.23-alpha2"));
        assertFalse(stableVersions.contains("0.0.13-ALPHA"));
        
        // 应该包含稳定版本
        assertTrue(stableVersions.contains("0.0.1"));
        assertTrue(stableVersions.contains("0.0.22"));
        assertTrue(stableVersions.contains("0.0.13"));
        
        // 应该按版本号排序，最后一个应该是最新的稳定版本
        assertEquals("0.0.22", stableVersions.get(stableVersions.size() - 1));
    }

    @Test
    public void testParsePreReleaseVersions() throws Exception {
        List<String> preReleaseVersions = MavenVersionParser.parsePreReleaseVersions(SAMPLE_METADATA_XML);
        
        // 应该只包含预发布版本
        assertTrue(preReleaseVersions.contains("0.0.23-alpha2"));
        assertTrue(preReleaseVersions.contains("0.0.13-ALPHA"));
        
        // 不应该包含稳定版本
        assertFalse(preReleaseVersions.contains("0.0.22"));
        assertFalse(preReleaseVersions.contains("0.0.13"));
        
        assertEquals(2, preReleaseVersions.size());
    }

    @Test
    public void testParseLatestStableVersionWithNoStableVersions() {
        String metadataWithOnlyPreReleases = """
            <?xml version="1.0" encoding="UTF-8"?>
            <metadata>
              <groupId>cn.com.chinastock</groupId>
              <artifactId>test-artifact</artifactId>
              <versioning>
                <release>1.0.0-alpha</release>
                <versions>
                  <version>1.0.0-alpha</version>
                  <version>1.0.0-beta</version>
                </versions>
                <lastUpdated>20250423085145</lastUpdated>
              </versioning>
            </metadata>
            """;

        Exception exception = assertThrows(Exception.class, () -> {
            MavenVersionParser.parseLatestStableVersion(metadataWithOnlyPreReleases);
        });
        
        assertTrue(exception.getMessage().contains("No stable version found"));
    }

    @Test
    public void testParseWithMissingVersioningElement() {
        String invalidMetadata = """
            <?xml version="1.0" encoding="UTF-8"?>
            <metadata>
              <groupId>cn.com.chinastock</groupId>
              <artifactId>test-artifact</artifactId>
            </metadata>
            """;

        Exception exception = assertThrows(Exception.class, () -> {
            MavenVersionParser.parseLatestVersion(invalidMetadata);
        });
        
        assertTrue(exception.getMessage().contains("No versioning element found"));
    }
}
