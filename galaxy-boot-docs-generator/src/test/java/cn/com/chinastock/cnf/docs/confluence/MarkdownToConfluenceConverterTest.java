package cn.com.chinastock.cnf.docs.confluence;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class MarkdownToConfluenceConverterTest {
    @Test
    void shouldConvertMarkdownContentToConfluenceFormat() {
        // Given
        String markdownContent = """
                ## Example Markdown
                
                Here is a Jira link: [JIRA-123](http://pmc.chinastock.com.cn/jira/browse/JIRA-123).
                
                ```plantuml
                @startuml
                Alice -> Bob: Hello Bob!
                @enduml
                ```
                """;

        // When
        String confluenceContent = MarkdownToConfluenceConverter.convert(markdownContent);

                String expectedConfluenceContent = """
        <h2>Example Markdown</h2>
        <p>Here is a Jira link: <ac:structured-macro ac:macro-id="UUID" ac:name="jira" ac:schema-version="1">
        <ac:parameter ac:name="server">JIRA</ac:parameter>
        <ac:parameter ac:name="columnIds">issuekey,summary,issuetype,created,updated,duedate,assignee,reporter,priority,status,resolution</ac:parameter>
        <ac:parameter ac:name="columns">key,summary,type,created,updated,due,assignee,reporter,priority,status,resolution</ac:parameter>
        <ac:parameter ac:name="serverId">34a7e255-c22c-322a-9f76-0f2c5871bd67</ac:parameter>
        <ac:parameter ac:name="key">JIRA-123</ac:parameter>
        </ac:structured-macro>
        .</p>
        <ac:structured-macro ac:macro-id="UUID" ac:name="plantumlrender" ac:schema-version="1">
        <ac:rich-text-body>
        <p>@startuml</p>
        <p>Alice -&gt; Bob: Hello Bob!</p>
        <p>@enduml</p>
        </ac:rich-text-body>
        </ac:structured-macro>
        """;

        confluenceContent = confluenceContent.replaceAll("ac:macro-id=\"[^\"]+\"", "ac:macro-id=\"UUID\"");
        Assertions.assertThat(confluenceContent).isEqualToNormalizingNewlines(expectedConfluenceContent);
    }

    @Test
    void shouldConvertMarkdownTableToConfluenceFormat() {
        // Given
        String markdownContent = """
                | 框架名称             | 性能表现         | 安全性分析          | 数据完整性     | 社区活跃度            | 适用场景            |
                |------------------|--------------|----------------|-----------|------------------|-----------------|
                | **Jackson**      | 性能中等，体积适中    | 对反序列化漏洞的防护表现良好 | 支持完整对象图   | 社区活跃，Issues 相对较少 | 适合需要高安全性的通用场景   |
                | **Fastjson 1.x** | 性能优异，但漏洞风险较大 | 安全性较弱，易受漏洞威胁   | 数据完整性支持一般 | 官方停止维护，Issue 较多  | 不建议在生产环境使用      |
                | **Fastjson 2.x** | 性能显著优化，业界领先  | 安全性显著增强，降低攻击风险 | 数据完整性提升   | 官方维护活跃，Issue 数适中 | 高性能 JSON 解析和序列化 |
                | **Gson**         | 性能中等，体积较小    | 安全性一般，无反序列化防护  | 支持基本数据完整性 | 社区稳定，Issues 较少   | 简单轻量的 JSON 处理任务 |
                | **org.json**     | 性能较差，功能简单    | 安全性一般，无反序列化防护  | 支持基本数据完整性 | 社区稳定，Issues 较少   | 适合小型应用或旧系统兼容    |
                """;

        // When
        String confluenceContent = MarkdownToConfluenceConverter.convert(markdownContent);

        // Then
        String expectedConfluenceContent = """
            <table>
            <thead>
            <tr><th> 框架名称             </th><th> 性能表现         </th><th> 安全性分析          </th><th> 数据完整性     </th><th> 社区活跃度            </th><th> 适用场景            </th></tr>
            </thead>
            <tbody>
            <tr><td> <strong>Jackson</strong>      </td><td> 性能中等，体积适中    </td><td> 对反序列化漏洞的防护表现良好 </td><td> 支持完整对象图   </td><td> 社区活跃，Issues 相对较少 </td><td> 适合需要高安全性的通用场景   </td></tr>
            <tr><td> <strong>Fastjson 1.x</strong> </td><td> 性能优异，但漏洞风险较大 </td><td> 安全性较弱，易受漏洞威胁   </td><td> 数据完整性支持一般 </td><td> 官方停止维护，Issue 较多  </td><td> 不建议在生产环境使用      </td></tr>
            <tr><td> <strong>Fastjson 2.x</strong> </td><td> 性能显著优化，业界领先  </td><td> 安全性显著增强，降低攻击风险 </td><td> 数据完整性提升   </td><td> 官方维护活跃，Issue 数适中 </td><td> 高性能 JSON 解析和序列化 </td></tr>
            <tr><td> <strong>Gson</strong>         </td><td> 性能中等，体积较小    </td><td> 安全性一般，无反序列化防护  </td><td> 支持基本数据完整性 </td><td> 社区稳定，Issues 较少   </td><td> 简单轻量的 JSON 处理任务 </td></tr>
            <tr><td> <strong>org.json</strong>     </td><td> 性能较差，功能简单    </td><td> 安全性一般，无反序列化防护  </td><td> 支持基本数据完整性 </td><td> 社区稳定，Issues 较少   </td><td> 适合小型应用或旧系统兼容    </td></tr>
            </tbody>
            </table>
            """;

        Assertions.assertThat(confluenceContent).isEqualToNormalizingNewlines(expectedConfluenceContent);
    }

    @Test
    void should_convert_code_block_to_confluence_macro() {
        String markdown = """
                # hh1

                ```java
                public class Hello {
                    public static void main(String[] args) {
                        System.out.println("Hello World");
                    }
                }
                ```
                """;

        String html = MarkdownToConfluenceConverter.convert(markdown);
        System.out.println(html);

        String part = """
                <ac:parameter ac:name="language">java</ac:parameter><ac:parameter ac:name="theme">Midnight</ac:parameter>
                <ac:plain-text-body><![CDATA[public class Hello {
                    public static void main(String[] args) {
                        System.out.println("Hello World");
                    }
                }
                ]]></ac:plain-text-body>
                """;

        assertThat(html).contains(part);
    }
}
