package cn.com.chinastock.cnf.docs.javadoc;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class JavadocToMarkdownConverterTest {

    @Test
    void should_convert_Class_javadoc_to_markdown() {
        // Given
        String javadoc = """
                <pre>
                    {@code
                         public static void main(String[] args) {
                         }
                    }
                </pre>
                <p>{@link java.util.List}</p>
                """;
        String className = "ExampleClass";
        JavadocToMarkdownConverter converter = new JavadocToMarkdownConverter();

        // When
        String result = converter.convert(javadoc, className);

        // Then
        String expectedMarkdown = """
                ```
                public static void main(String[] args) {
                }
                ```
                
                [java.util.List](#java-util-list)""";
        assertThat(result).isEqualTo(expectedMarkdown);
    }

    // should parse table
    @Test
    void should_convert_javadoc_table_to_markdown() {
        // Given
        String javadoc = """
                <table>
                    <tr>
                        <th>Header 1</th>
                        <th>Header 2</th>
                    </tr>
                    <tr>
                        <td>Row 1, Cell 1</td>
                        <td>Row 1, Cell 2</td>
                    </tr>
                    <tr>
                        <td>Row 2, Cell 1</td>
                        <td>Row 2, Cell 2</td>
                    </tr>
                </table>
                """;
        String className = "ExampleClass";
        JavadocToMarkdownConverter converter = new JavadocToMarkdownConverter();

        // When
        String result = converter.convert(javadoc, className);

        // Then
        String expectedMarkdown = """
                | Header 1 | Header 2 |
                |---|---|
                | Row 1, Cell 1 | Row 1, Cell 2 |
                | Row 2, Cell 1 | Row 2, Cell 2 |""";
        assertThat(result).isEqualTo(expectedMarkdown);
    }
}
