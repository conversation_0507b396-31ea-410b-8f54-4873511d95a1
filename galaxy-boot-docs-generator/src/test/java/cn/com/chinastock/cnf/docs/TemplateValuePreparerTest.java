package cn.com.chinastock.cnf.docs;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import static cn.com.chinastock.cnf.docs.template.TemplateValuePreparer.convertToUpperCaseCamelCase;

public class TemplateValuePreparerTest {
    @Test
    void shouldConvertToUpperCaseCamelCase() {
        String normal = convertToUpperCaseCamelCase("example-input");
        Assertions.assertThat(normal).isEqualTo("ExampleInput");
    }

    @Test
    void shouldReturnEmptyStringWhenInputIsNull() {
        String result = convertToUpperCaseCamelCase(null);
        Assertions.assertThat(result).isEmpty();
    }

    @Test
    void shouldReturnEmptyStringWhenInputIsEmpty() {
        String input = "";
        String result = convertToUpperCaseCamelCase(input);
        Assertions.assertThat(result).isEmpty();
    }

    @Test
    void shouldHandleMultipleHyphens() {
        String input = "this-is-a-more-complex-example";
        String result = convertToUpperCaseCamelCase(input);
        Assertions.assertThat(result).isEqualTo("ThisIsAMoreComplexExample");
    }

}
