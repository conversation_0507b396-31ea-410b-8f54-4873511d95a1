package cn.com.chinastock.cnf.docs.javadoc;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class JavaCommentParserTest {

    @Test
    void should_processJavaFile_withValidContent_returnMarkdown() {
        // Given
        String content = """
                /**
                 * IdUtils 提供用于生成各种长度的 UUID 功能：8 位、16 位、32 位、64 位或 128 位。
                 *
                 * <p>该类中包含以下静态方法：</p>
                 *
                 * <ul>
                 *     <li>{@link #getId(ID_LEN)}：根据指定的长度生成 UUID。</li>
                 * </ul>
                 * <p>
                 * 使用示例：
                 *
                 * <pre>
                 *     {@code
                 *       String id = IdUtils.getId(ID_LEN.LEN32);
                 *       // 输出一个32位的 UUID 字符串，没有横杠
                 *     }
                 * </pre>
                 *
                 */
                public class IdUtils {
                    /**
                     * 获取一个 8 位不重复的 ID。
                     * <p>
                     * 该方法通过生成一个随机的 UUID，并从中提取部分字符来生成一个 8 位的字符串。
                     * </p>
                     *
                     * @return 一个 8 位不重复的 ID
                     */
                    public static String getId8() {
                    }
                }
                """;

        // When
        PackageComment packageComment = JavaCommentParser.processJavaFile(content);
        Assertions.assertThat(packageComment).isNotNull();
        Assertions.assertThat(packageComment.classComment).isEqualTo("""
                IdUtils 提供用于生成各种长度的 UUID 功能：8 位、16 位、32 位、64 位或 128 位。
                
                该类中包含以下静态方法：
                
                * [IdUtils#getId(ID_LEN)](#idutils-getid-id_len)：根据指定的长度生成 UUID。
                
                使用示例：
                
                ```
                String id = IdUtils.getId(ID_LEN.LEN32);
                // 输出一个32位的 UUID 字符串，没有横杠
                ```""");
        Assertions.assertThat(packageComment.methodComments).hasSize(1);
        String firstComment = packageComment.methodComments.getFirst();
        Assertions.assertThat(firstComment).isEqualTo("""
                ##### IdUtils#getId8

                `public static String getId8()`
                
                获取一个 8 位不重复的 ID。
                
                该方法通过生成一个随机的 UUID，并从中提取部分字符来生成一个 8 位的字符串。""");
    }
}
