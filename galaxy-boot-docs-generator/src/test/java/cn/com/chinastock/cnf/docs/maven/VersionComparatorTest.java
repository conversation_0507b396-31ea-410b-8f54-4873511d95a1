package cn.com.chinastock.cnf.docs.maven;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * VersionComparator 测试类
 */
public class VersionComparatorTest {

    @Test
    public void testIsPreRelease() {
        // 测试预发布版本识别
        assertTrue(VersionComparator.isPreRelease("0.0.23-alpha2"));
        assertTrue(VersionComparator.isPreRelease("1.0.0-beta"));
        assertTrue(VersionComparator.isPreRelease("2.0.0-rc1"));
        assertTrue(VersionComparator.isPreRelease("1.0.0-SNAPSHOT"));
        assertTrue(VersionComparator.isPreRelease("1.0.0-M1"));
        assertTrue(VersionComparator.isPreRelease("1.0.0.alpha"));
        assertTrue(VersionComparator.isPreRelease("1.0.0.ALPHA"));
        
        // 测试稳定版本
        assertFalse(VersionComparator.isPreRelease("0.0.23"));
        assertFalse(VersionComparator.isPreRelease("1.0.0"));
        assertFalse(VersionComparator.isPreRelease("2.1.3"));
        
        // 测试边界情况
        assertFalse(VersionComparator.isPreRelease(null));
        assertFalse(VersionComparator.isPreRelease(""));
        assertFalse(VersionComparator.isPreRelease("   "));
    }

    @Test
    public void testCompareVersions() {
        // 测试基本版本比较
        assertTrue(VersionComparator.compare("1.0.0", "0.9.0") > 0);
        assertTrue(VersionComparator.compare("0.9.0", "1.0.0") < 0);
        assertEquals(0, VersionComparator.compare("1.0.0", "1.0.0"));
        
        // 测试不同长度的版本号
        assertTrue(VersionComparator.compare("1.0.1", "1.0") > 0);
        assertTrue(VersionComparator.compare("1.0", "1.0.1") < 0);
        assertEquals(0, VersionComparator.compare("1.0", "1.0.0"));
        
        // 测试预发布版本比较
        assertTrue(VersionComparator.compare("1.0.0", "1.0.0-alpha") > 0);
        assertTrue(VersionComparator.compare("1.0.0-alpha", "1.0.0") < 0);
        assertTrue(VersionComparator.compare("1.0.0-beta", "1.0.0-alpha") > 0);
        
        // 测试实际示例
        assertTrue(VersionComparator.compare("0.0.23", "0.0.23-alpha2") > 0);
        assertTrue(VersionComparator.compare("0.0.22", "0.0.23-alpha2") < 0);
        
        // 测试null值
        assertEquals(0, VersionComparator.compare(null, null));
        assertTrue(VersionComparator.compare("1.0.0", null) > 0);
        assertTrue(VersionComparator.compare(null, "1.0.0") < 0);
    }

    @Test
    public void testVersionSorting() {
        String[] versions = {
            "0.0.1", "0.0.3", "0.0.7", "0.0.10", "0.0.11", "0.0.12", 
            "0.0.13-ALPHA", "0.0.13", "0.0.14", "0.0.16", "0.0.17", 
            "0.0.15", "0.0.18", "0.0.19", "0.0.20", "0.0.21", 
            "0.0.22", "0.0.23-alpha2"
        };
        
        // 找到最新的稳定版本应该是 0.0.22
        String latestStable = null;
        for (String version : versions) {
            if (!VersionComparator.isPreRelease(version)) {
                if (latestStable == null || VersionComparator.compare(version, latestStable) > 0) {
                    latestStable = version;
                }
            }
        }
        
        assertEquals("0.0.22", latestStable);
    }
}
