package cn.com.chinastock.cnf.docs.template;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Properties;

public class TemplateCompilerTest {

    @Test
    void should_loadTemplateContent_withValidFile() throws IOException {
        // Given
        File templateFile = new File("template.txt");
        Files.write(templateFile.toPath(), "This is a template content".getBytes());

        // When
        String templateContent = TemplateCompiler.loadTemplateContent(templateFile);

        // Then
        Assertions.assertThat(templateContent).isEqualTo("This is a template content");

        // Cleanup
        Files.deleteIfExists(templateFile.toPath());
    }

    @Test
    void should_compile_withValidTemplateFile() throws IOException {
        // Given
        File templateFile = new File("template.txt");
        Files.write(templateFile.toPath(), "#if (key) { ${key} }".getBytes());
        File outputFile = new File("output.txt");
        Properties parentProperties = new Properties();
        parentProperties.setProperty("key", "value");

        // When
        TemplateCompiler.compile(templateFile, outputFile, parentProperties);

        // Then
        Assertions.assertThat(outputFile).exists();
        String outputContent = new String(Files.readAllBytes(outputFile.toPath()));
        Assertions.assertThat(outputContent).contains("value");

        // Cleanup
        Files.deleteIfExists(templateFile.toPath());
        Files.deleteIfExists(outputFile.toPath());
    }
}
