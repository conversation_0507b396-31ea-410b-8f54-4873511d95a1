package cn.com.chinastock.cnf.utils.id;

import cn.com.chinastock.cnf.utils.id.snowflake.contract.IIdGenerator;
import cn.com.chinastock.cnf.utils.id.snowflake.contract.IdGeneratorException;
import cn.com.chinastock.cnf.utils.id.snowflake.contract.IdGeneratorOptions;
import cn.com.chinastock.cnf.utils.id.snowflake.generator.DefaultIdGenerator;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 这是一个调用的例子，默认情况下，单机集成者可以直接使用 nextId()。
 */
public class SnowflakeIdUtils {

    private static IIdGenerator idGenInstance = null;

    public static IIdGenerator getIdGenInstance() {
        return idGenInstance;
    }

    /**
     * 设置参数，建议程序初始化时执行一次
     *
     * @param options 雪花算法配置，建议springboot框架下用Configuration配置Bean
     *              <ul>
     *                  <li>@Configuration</li>
     *                       <li>public class SnowflakeConfig {</li>
     *                             <li>@Bean</li>
     *                             <li>public IdGeneratorOptions idGeneratorOptions() {</li>
     *                                 <li>IdGeneratorOptions options = new IdGeneratorOptions(SnowflakeIdUtils.genWorkId());</li>
     *                                 <li>options.SeqBitLength = 21;</li>
     *                                 <li>options.WorkerIdBitLength = 11;</li>
     *                                 <li>SnowflakeIdUtils.setIdGenerator(options);</li>
     *                                 <li>return options;</li>
     *                             <li>}</li>
     *                         <li>}</li>
     *              </ul>
     */
    public static void setIdGenerator(IdGeneratorOptions options) throws IdGeneratorException {
        idGenInstance = new DefaultIdGenerator(options);
    }

    /**
     * 生成新的Id
     * 调用本方法前，请确保调用了 SetIdGenerator 方法做初始化。
     *
     * @return 返回一个long型数字。
     */
    public static long nextId() throws IdGeneratorException {
        if (idGenInstance == null)
            throw new IdGeneratorException("Please initialize IdGeneratorOptions first.");

        return idGenInstance.newLong();
    }
    /**
     * 生成新的Ids
     * 调用本方法前，请确保调用了 SetIdGenerator 方法做初始化。
     * @param num 需要生成得id数量
     *
     * @return 返回一组long型数字
     */
    public static List<Long> nextIds(int num) throws IdGeneratorException {
        List<Long> ids = new ArrayList<>();

        if (idGenInstance == null)
            throw new IdGeneratorException("Please initialize IdGeneratorOptions first.");

        if (num <= 1) {
            throw new IdGeneratorException("Wrong parameter ids num, must bigger than 1.");
        }

        for (int i = 0; i < num; i++) {
            synchronized (ids) {
                long id = idGenInstance.newLong();
                ids.add(id);
            }
        }
        return ids;
    }

    /**
     * 获取本地机器码字符串
     *
     *
     * @return 返回一个字符串得机器码，来自于MAC、IP、主机名
     */
    public static String getUniqueIdentifier() throws Exception {
        InetAddress ip = getLocalIPAddress();
        NetworkInterface network = NetworkInterface.getByInetAddress(ip);

        if (network != null) {
            byte[] mac = network.getHardwareAddress();
            if (mac != null && mac.length > 0) {
                StringBuilder macAddress = new StringBuilder();
                for (byte b : mac) {
                    macAddress.append(String.format("%02X", b));
                }

                String ipAddress = ip.getHostAddress();
                String hostName = ip.getHostName();

                // 组合多个标识符
                return macAddress.toString() + ipAddress + hostName;
            }
        }

        throw new Exception("无法获取唯一标识符");
    }

    /**
     * 获取本机的 InetAddress。
     *
     * @return 本机的 InetAddress
     */
    public static InetAddress getLocalIPAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                // Skip down, loopback, and virtual interfaces (utun, lo, etc.)
                if (!networkInterface.isUp() || networkInterface.isLoopback() || networkInterface.isVirtual()) {
                    continue;
                }
                String name = networkInterface.getName();
                if (name != null && (name.startsWith("utun") || name.startsWith("lo"))) {
                    continue;
                }
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address instanceof Inet4Address) {
                        return address;
                    }
                }
            }
            // Fallback: try InetAddress.getLocalHost() if nothing found
            InetAddress localHost = InetAddress.getLocalHost();
            if (localHost != null && !localHost.isLoopbackAddress()) {
                return localHost;
            }
        } catch (Exception e) {
            // 发生异常时返回null，避免影响应用启动
        }
        return null;
    }

    /**
     * 获取本地机器码数字
     *
     *
     * @return 根据getUniqueIdentifier计算出一个short数字
     */
    public static short genWorkId(){
        try {
            String uniqueId = getUniqueIdentifier();

            // 获取SHA-256的消息摘要实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 将组合后的标识符转换为字节数组并更新摘要
            byte[] hash = digest.digest(uniqueId.getBytes(StandardCharsets.UTF_8));

            // 使用前两个字节创建一个int值
            int result = ((hash[0] & 0xFF) << 8) | (hash[1] & 0xFF);

            // 缩放结果以确保其值在0到63之间
            int scaledResult = Math.abs(result % 64);

            // 返回short结果
            return (short) scaledResult;
        } catch (Exception e) {
            throw new RuntimeException("获取机器标识符失败", e);
        }
    }

    public static void main(String[] args) {
        SnowflakeIdUtils snowflakeIdUtils = new SnowflakeIdUtils();
        short workId = snowflakeIdUtils.genWorkId();
        System.out.println("workId:"+workId);
        IdGeneratorOptions options = new IdGeneratorOptions(workId);
        //options.Method = 1;
        options.SeqBitLength = 21;
        options.WorkerIdBitLength = 11;
        snowflakeIdUtils.setIdGenerator(options);

        long pre = -1;
        for (int i = 0; i < 10; i++) {
            long cur = snowflakeIdUtils.nextId();
            if (cur > pre) {
                //System.out.println("success!!");
            } else {
                System.out.println("failed!!!!!!!!!!!!!!!!!!!!!!!");
            }
            pre = cur;
            System.out.println(cur);
        }


    }
}
