package cn.com.chinastock.cnf.utils.id;

import java.util.UUID;

/**
 * IdUtils 提供用于生成各种长度的 UUID 功能：8 位、16 位、32 位、64 位或 128 位。
 *
 * <p>该类中包含以下静态方法：</p>
 *
 * <ul>
 *     <li>{@link #getId(ID_LEN)}：根据指定的长度生成 UUID。</li>
 * </ul>
 * <p>
 * 使用示例：
 *
 * <pre>
 *     {@code
 *       String id = IdUtils.getId(ID_LEN.LEN32);
 *       // 输出一个32位的 UUID 字符串，没有横杠
 *     }
 * </pre>
 *
 * <AUTHOR>
 */
public class IdUtils {
    private static final String[] CHARS = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
            "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8",
            "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"};

    /**
     * 根据指定的长度生成唯一识别ID。
     *
     * <pre>
     *    {@code
     * 	    String id = IdUtils.getId(ID_LEN.LEN32);
     * 	    // id 是一个32位的UUID字符串，没有横杠
     *    }
     * 	</pre>
     *
     * @param idLen 指定ID的长度，可接受的值为：
     *              <ul>
     *                  <li>{@link ID_LEN#LEN8} 生成8位UUID。</li>
     *                  <li>{@link ID_LEN#LEN16} 生成16位UUID，由两个8位UUID拼接而成。</li>
     *                  <li>{@link ID_LEN#LEN32} 生成32位UUID。</li>
     *                  <li>{@link ID_LEN#LEN64} 生成64位UUID，由两个32位UUID拼接而成。</li>
     *                  <li>{@link ID_LEN#LEN128} 生成128位UUID，由四个32位UUID拼接而成。</li>
     *              </ul>
     * @return 根据指定长度生成的唯一识别ID字符串。
     * @see ID_LEN
     */
    public static String getId(ID_LEN idLen) {
        switch (idLen) {
            case LEN8:
                return getId8();
            case LEN16:
                return getId8() + getId8();
            case LEN32:
                return getId32();
            case LEN64:
                return getId32() + getId32();
            case LEN128:
                return getId32() + getId32() + getId32() + getId32();
            default:
                return null;
        }
    }

    /**
     * 生成一个32位的唯一标识符。
     * <p>
     * 该方法使用UUID（通用唯一识别码）生成一个随机字符串，然后将字符串中的连字符（-）去除，
     * 并将所有字符转换为大写。最终返回一个32位的唯一标识符。
     * </p>
     *
     * @return 一个32位的唯一标识符，由随机生成的 UUID 转换而来
     */
    private static String getId32() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }

    /**
     * 获取一个 8 位不重复的 ID。
     * <p>
     * 该方法通过生成一个随机的 UUID，并从中提取部分字符来生成一个 8 位的字符串。
     * </p>
     *
     * @return 一个 8 位不重复的 ID
     */
    private static String getId8() {
        StringBuilder shortBuffer = new StringBuilder();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(CHARS[x % 0x3E]);
        }
        return shortBuffer.toString();
    }
}
