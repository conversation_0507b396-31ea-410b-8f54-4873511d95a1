package cn.com.chinastock.cnf.utils.id;

/**
 * 表示唯一标识符（ID）的长度类型。
 *
 * <p>此枚举定义了常用的 ID 长度选项，便于系统在生成或校验 ID 时选择合适的长度。
 *
 * <ul>
 *   <li>{@link #LEN8} - 8 位长度</li>
 *   <li>{@link #LEN16} - 16 位长度</li>
 *   <li>{@link #LEN32} - 32 位长度</li>
 *   <li>{@link #LEN64} - 64 位长度</li>
 *   <li>{@link #LEN128} - 128 位长度</li>
 * </ul>
 */
public enum ID_LEN {
    /**
     * 表示 8 位长度的 ID。
     */
    LEN8(8),

    /**
     * 表示 16 位长度的 ID。
     */
    LEN16(16),

    /**
     * 表示 32 位长度的 ID。
     */
    LEN32(32),

    /**
     * 表示 64 位长度的 ID。
     */
    LEN64(64),

    /**
     * 表示 128 位长度的 ID。
     */
    LEN128(128);

    private final int len;

    private ID_LEN(int len) {
        this.len = len;
    }

    /**
     * 返回当前对象的长度属性值。
     *
     * @return 当前对象的长度属性值，类型为int。
     */
    public int getLen() {
        return len;
    }

    /**
     * 根据指定的长度值获取对应的 ID 长度枚举对象。
     *
     * @param len 指定的长度值
     * @return 对应的 ID 长度枚举对象
     */
    public static ID_LEN getLen(int len) {
        for (ID_LEN idLen : ID_LEN.values()) {
            if (idLen.getLen() == len) {
                return idLen;
            }
        }
        return null;
    }
}
