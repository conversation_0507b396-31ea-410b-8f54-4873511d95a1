package cn.com.chinastock.cnf.utils.id;

import cn.com.chinastock.cnf.utils.id.snowflake.contract.IdGeneratorOptions;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class SnowflakeUtilsTest {
    static {
        IdGeneratorOptions options = new IdGeneratorOptions(SnowflakeIdUtils.genWorkId());
        options.SeqBitLength = 17;
        options.WorkerIdBitLength = 6;
        SnowflakeIdUtils.setIdGenerator(options);
    }

    @Test
    public void testGetUniqueIdentifier() throws Exception {
        String uniqueIdentifier = SnowflakeIdUtils.getUniqueIdentifier();
        assertNotNull(uniqueIdentifier);
    }

    @Test
    public void testGenWorkId() {
        short workId = SnowflakeIdUtils.genWorkId();
        assertTrue(workId > 0);
    }

    @Test
    public void testNextIds() {
        List<Long> ids = SnowflakeIdUtils.nextIds(100);
        assertNotNull(ids);
        assertEquals(100, ids.size());
    }

    @Test
    public void testNextId() {
        long id = SnowflakeIdUtils.nextId();
        assertTrue(id > 0);
    }
}
