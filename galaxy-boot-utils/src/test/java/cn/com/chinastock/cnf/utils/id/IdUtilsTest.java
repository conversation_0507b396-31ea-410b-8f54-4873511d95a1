package cn.com.chinastock.cnf.utils.id;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class IdUtilsTest {
    @Test
    public void testGetIdLen8() {
        String id8 = IdUtils.getId(ID_LEN.LEN8);
        assertNotNull(id8);
        assertEquals(8, id8.length());
    }

    @Test
    public void testGetIdLen16() {
        String id16 = IdUtils.getId(ID_LEN.LEN16);
        assertNotNull(id16);
        assertEquals(16, id16.length());
    }

    @Test
    public void testGetIdLen32() {
        String id32 = IdUtils.getId(ID_LEN.LEN32);
        assertNotNull(id32);
        assertEquals(32, id32.length());
    }

    @Test
    public void testGetIdLen64() {
        String id64 = IdUtils.getId(ID_LEN.LEN64);
        assertNotNull(id64);
        assertEquals(64, id64.length());
    }

    @Test
    public void testGetIdLen128() {
        String id128 = IdUtils.getId(ID_LEN.LEN128);
        assertNotNull(id128);
        assertEquals(128, id128.length());
    }
}