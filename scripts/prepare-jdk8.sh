#!/bin/bash

rm -rf *-jdk8
rm -rf galaxy-boot-starters-jdk8/*-jdk8

# 拉取一个新的分支来调整路径
git branch -d adjust-paths-for-jdk8
git checkout -b adjust-paths-for-jdk8

# 自动调整路径，将主版本的模块目录重命名为 JDK 8 版本格式
modules=(
  "galaxy-boot-core"
  "galaxy-boot-dependencies"
  "galaxy-boot-security"
  "galaxy-boot-test"
  "galaxy-boot-utils"
  "galaxy-boot-starters-jdk8"
)

# 遍历模块列表并重命名目录
for module in "${modules[@]}"; do
  if [ -d "$module" ]; then
    mv "$module" "${module}-jdk8"
    echo "[INFO] Renamed $module to ${module}-jdk8"
  else
    echo "[WARNING] Directory $module not found, skipping..."
  fi
done

# 处理 starters 子项目
if [ -d "galaxy-boot-starters-jdk8" ]; then
  for starter in galaxy-boot-starters-jdk8/*; do
    if [ -d "$starter" ]; then
      mv "$starter" "${starter}-jdk8"
      echo "[INFO] Renamed $starter to ${starter}-jdk8"
    fi
  done
fi

# 完成
echo "[DONE] Path adjustment completed."
