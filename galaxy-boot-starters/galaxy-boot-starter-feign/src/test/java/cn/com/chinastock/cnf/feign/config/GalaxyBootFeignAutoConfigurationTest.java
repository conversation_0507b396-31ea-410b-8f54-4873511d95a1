package cn.com.chinastock.cnf.feign.config;

import cn.com.chinastock.cnf.feign.okhttp.OkHttpClientProperties;
import feign.Logger;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GalaxyBootFeignAutoConfigurationTest {

    private GalaxyBootFeignAutoConfiguration configuration;

    private GalaxyBootFeignLoadBalancerAutoConfiguration loadBalancerConfiguration;

    @Mock
    private OkHttpClientProperties okHttpClientProperties;

    @BeforeEach
    void setUp() {
        configuration = new GalaxyBootFeignAutoConfiguration();
        loadBalancerConfiguration = new GalaxyBootFeignLoadBalancerAutoConfiguration();
    }

    @Test
    void shouldCreateOkHttpClientWithConfiguredTimeoutValues() {
        // Setup
        when(okHttpClientProperties.getConnectTimeout()).thenReturn(1000);
        when(okHttpClientProperties.getReadTimeout()).thenReturn(2000);
        ConnectionPool connectionPool = new ConnectionPool(200, 600, TimeUnit.SECONDS);

        // Execute
        OkHttpClient client = loadBalancerConfiguration.okHttpClient(connectionPool, okHttpClientProperties);

        // Verify
        assertNotNull(client);
        assertEquals(1000, client.connectTimeoutMillis());
        assertEquals(2000, client.readTimeoutMillis()); // Note: There might be a bug in the original code where readTimeout uses connectTimeout value
        assertSame(connectionPool, client.connectionPool());
    }

    @ParameterizedTest
    @EnumSource(Logger.Level.class)
    void shouldCreateCorrectLoggerLevel(Logger.Level configuredLevel) {
        // Setup
        when(okHttpClientProperties.getLoggerLevel()).thenReturn(configuredLevel);

        // Execute
        Logger.Level level = configuration.feignLoggerLevel(okHttpClientProperties);

        // Verify
        assertEquals(configuredLevel, level);
        verify(okHttpClientProperties).getLoggerLevel();
    }

    @Test
    void shouldReturnNoneWhenConfiguredLoggerLevelIsNull() {
        // Setup
        when(okHttpClientProperties.getLoggerLevel()).thenReturn(null);

        // Execute
        Logger.Level level = configuration.feignLoggerLevel(okHttpClientProperties);

        // Verify
        assertEquals(Logger.Level.NONE, level);
        verify(okHttpClientProperties).getLoggerLevel();
    }
}