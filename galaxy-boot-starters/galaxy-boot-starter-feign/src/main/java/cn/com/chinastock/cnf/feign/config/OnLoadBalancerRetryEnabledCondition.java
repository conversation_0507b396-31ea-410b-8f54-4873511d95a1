package cn.com.chinastock.cnf.feign.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.loadbalancer.LoadBalancedRetryFactory;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.retry.support.RetryTemplate;

public class OnLoadBalancerRetryEnabledCondition extends AllNestedConditions {

    public OnLoadBalancerRetryEnabledCondition() {
        super(ConfigurationPhase.REGISTER_BEAN);
    }

    @ConditionalOnClass(RetryTemplate.class)
    static class OnRetryTemplateCondition {

    }

    @ConditionalOnClass({LoadBalancerClient.class, LoadBalancerClientFactory.class, LoadBalancedRetryFactory.class})
    @ConditionalOnProperty(value = "spring.cloud.loadbalancer.enabled", havingValue = "true")
    static class OnLoadBalancerEnabledWithRetryFactoryCondition {

    }

    @ConditionalOnProperty(value = "spring.cloud.loadbalancer.retry.enabled", havingValue = "true", matchIfMissing = true)
    static class OnRetryEnabledCondition {

    }
}
