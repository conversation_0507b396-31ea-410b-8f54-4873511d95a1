package cn.com.chinastock.cnf.feign.okhttp;

import feign.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.concurrent.TimeUnit;

import static org.springframework.cloud.openfeign.support.FeignHttpClientProperties.*;

@ConfigurationProperties(prefix = "spring.cloud.openfeign")
public class OkHttpClientProperties {

    @Value("${spring.cloud.openfeign.httpclient.maxConnections:200}")
    private int maxConnections = DEFAULT_MAX_CONNECTIONS;

    @Value("${spring.cloud.openfeign.httpclient.timeToLive:900}")
    private long timeToLive = DEFAULT_TIME_TO_LIVE;

    @Value("${spring.cloud.openfeign.httpclient.timeToLiveUnit:SECONDS}")
    private TimeUnit timeToLiveUnit = DEFAULT_TIME_TO_LIVE_UNIT;

    @Value("${spring.cloud.openfeign.client.config.default.readTimeout:10000}")
    private int readTimeout = 10000;

    @Value("${spring.cloud.openfeign.client.config.default.writeTimeout:10000}")
    private int writeTimeout = 10000;

    @Value("${spring.cloud.openfeign.client.config.default.connectTimeout:1000}")
    private int connectTimeout = 1000;

    @Value("${spring.cloud.openfeign.client.config.default.loggerLevel:NONE}")
    private Logger.Level loggerLevel = Logger.Level.NONE;

    public int getMaxConnections() {
        return maxConnections;
    }

    public long getTimeToLive() {
        return timeToLive;
    }

    public TimeUnit getTimeToLiveUnit() {
        return timeToLiveUnit;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public Logger.Level getLoggerLevel() {
        return loggerLevel;
    }

    public int getWriteTimeout() {
        return writeTimeout;
    }

    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }
}