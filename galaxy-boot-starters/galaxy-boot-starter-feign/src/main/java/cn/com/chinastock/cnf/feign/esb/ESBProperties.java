package cn.com.chinastock.cnf.feign.esb;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "galaxy.feign.esb")
public class ESBProperties {

    @Value("${galaxy.system.code:}")
    private String systemCode = "";

    @Value("${galaxy.feign.esb.user:}")
    private String user;

    @Value("${galaxy.feign.esb.password:}")
    private String password;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSystemCode() {
        return systemCode;
    }
}
