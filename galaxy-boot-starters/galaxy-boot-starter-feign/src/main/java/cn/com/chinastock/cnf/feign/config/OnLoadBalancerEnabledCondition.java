package cn.com.chinastock.cnf.feign.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;

public class OnLoadBalancerEnabledCondition extends AllNestedConditions {

    public OnLoadBalancerEnabledCondition() {
        super(ConfigurationPhase.REGISTER_BEAN);
    }

    @ConditionalOnClass({LoadBalancerClient.class, LoadBalancerClientFactory.class})
    @ConditionalOnProperty(value = "spring.cloud.loadbalancer.enabled", havingValue = "true")
    static class OnLoadBalancerEnabledWithClientCondition {

    }

    @ConditionalOnProperty(value = "spring.cloud.loadbalancer.retry.enabled", havingValue = "false")
    static class OnRetryNotEnabledCondition {

    }
}
