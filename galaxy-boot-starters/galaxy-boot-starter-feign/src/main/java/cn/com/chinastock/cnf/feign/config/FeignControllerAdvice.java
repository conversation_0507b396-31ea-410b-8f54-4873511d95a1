package cn.com.chinastock.cnf.feign.config;

import cn.com.chinastock.cnf.core.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

/**
 * ControllerAdvice 类用于全局处理控制器中的异常。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #handleFeignException(GalaxyFeignException)}：处理Feign客户端异常，返回HttpStatusCode=500。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@EnableConfigurationProperties(LogProperties.class)
public class FeignControllerAdvice {

    private final String systemCode;

    public FeignControllerAdvice(LogProperties logProperties) {
        this.systemCode = logProperties.getSystemCode();
    }

    private String generateDefaultCode(Integer code) {
        return systemCode + "TCNF" + code.toString();
    }

    @ExceptionHandler(GalaxyFeignException.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public BaseResponse<Object> handleFeignException(GalaxyFeignException e) {
        String code = generateDefaultCode(INTERNAL_SERVER_ERROR.value());
        String errorMessage = e.getCode().isEmpty() ? "调用三方服务异常" : "调用三方服务异常[" + e.getCode() + "]";
        String logMessage = "调用三方服务异常[code: " + e.getCode() + ", message: " + e.getMessage() + "]";
        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, logMessage, e);
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }
}
