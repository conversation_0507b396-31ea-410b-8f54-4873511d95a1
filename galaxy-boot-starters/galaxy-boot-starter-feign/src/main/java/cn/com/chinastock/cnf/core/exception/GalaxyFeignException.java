package cn.com.chinastock.cnf.core.exception;

import cn.com.chinastock.cnf.core.http.Meta;
import feign.FeignException;
import feign.Request;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * GalaxyFeignException 类是一个自定义的 Feign 异常类，用于处理 Feign 客户端调用时发生的异常。
 * 该类扩展了 FeignException，并添加了自定义的 Meta 信息和错误码。
 * 在 Feign 调用异常时，如果响应的 Body 中带有 Meta 信息，就会抛出该异常，并将 Meta 信息传递给前端。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #GalaxyFeignException(int, String, Request, byte[], Map, Meta)}：构造函数，用于初始化异常信息。</li>
 *     <li>{@link #getMeta()}：获取自定义的 Meta 信息。</li>
 *     <li>{@link #setMeta(Meta)}：设置自定义的 Meta 信息。</li>
 *     <li>{@link #getCode()}：获取错误码。</li>
 *     <li>{@link #setCode(String)}：设置错误码。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class GalaxyFeignException extends FeignException {

    private Meta meta;

    private String code;

    public GalaxyFeignException(
            int status,                     // HTTP 状态码
            String message,                 // 异常消息
            Request request,                // 原始请求信息
            byte[] responseBody,            // 响应的 Body 内容
            Map<String, Collection<String>> headers, // 响应 Headers
            Meta meta                       // 自定义的 Meta 信息
    ) {
        super(status, message, request, responseBody, headers);
        this.meta = meta;
        this.code = Optional.of(meta).map(Meta::getCode).orElse("");
    }

    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
