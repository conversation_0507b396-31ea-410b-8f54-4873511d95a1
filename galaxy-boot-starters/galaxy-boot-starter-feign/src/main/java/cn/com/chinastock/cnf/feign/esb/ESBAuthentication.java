package cn.com.chinastock.cnf.feign.esb;

import com.chinastock.esb.auth.PasswordDigest;
import com.chinastock.esb.auth.WsseAuth;

import java.util.HashMap;
import java.util.Map;

/**
 * ESB认证处理类，负责ESB认证信息的生成和请求头的处理
 */
public class ESBAuthentication {
    public static final String FUNCTION_NO_HEADER = "Function-No";
    public static final String CONTENT_TYPE_HEADER = "Content-Type";
    public static final String USER_HEADER = "User";
    public static final String CREATED_HEADER = "Created";
    public static final String NONCE_HEADER = "Nonce";
    // 安全扫描会把 PASSWORD 的变量名当作密码的 key，值为密码的值，导致误报
    public static final String PWD_DIGEST_HEADER = "Password-Digest";
    public static final String CALLER_SYSTEM_CODE_HEADER = "Caller-System-Code";
    public static final String DEFAULT_CONTENT_TYPE = "application/json";

    private final String nonce;
    private final String created;
    private final String passwordDigest;

    private ESBAuthentication(String nonce, String created, String passwordDigest) {
        this.nonce = nonce;
        this.created = created;
        this.passwordDigest = passwordDigest;
    }

    /**
     * 判断是否是ESB请求
     *
     * @param headers 请求头
     * @return 是否是ESB请求
     */
    public static boolean isESBRequest(Map<String, ?> headers) {
        return headers.containsKey(FUNCTION_NO_HEADER);
    }

    /**
     * 生成ESB认证信息
     *
     * @param password ESB密码
     * @return ESB认证信息
     */
    private static ESBAuthentication generate(String password) {
         WsseAuth obj = PasswordDigest.generate(password);
        return new ESBAuthentication(
                obj.getNonce(),
                obj.getCreated(),
                obj.getPswDigest()
        );
    }

    /**
     * 生成ESB请求头
     *
     * @param esbProperties ESB配置属性
     * @return ESB请求头
     */
    public static Map<String, String> generateHeaders(ESBProperties esbProperties) {
        ESBAuthentication auth = generate(esbProperties.getPassword());

        Map<String, String> esbHeaders = new HashMap<>();
        esbHeaders.put(CONTENT_TYPE_HEADER, DEFAULT_CONTENT_TYPE);
        esbHeaders.put(USER_HEADER, esbProperties.getUser());
        esbHeaders.put(CREATED_HEADER, auth.created);
        esbHeaders.put(NONCE_HEADER, auth.nonce);
        esbHeaders.put(PWD_DIGEST_HEADER, auth.passwordDigest);
        esbHeaders.put(CALLER_SYSTEM_CODE_HEADER, esbProperties.getSystemCode());

        return esbHeaders;
    }
} 