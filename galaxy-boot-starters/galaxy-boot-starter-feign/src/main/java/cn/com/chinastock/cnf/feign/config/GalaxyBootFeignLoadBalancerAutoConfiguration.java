package cn.com.chinastock.cnf.feign.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.feign.okhttp.OkHttpClientProperties;
import feign.Client;
import feign.Request;
import feign.okhttp.OkHttpClient;
import okhttp3.ConnectionPool;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalancedRetryFactory;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient;
import org.springframework.cloud.openfeign.loadbalancer.LoadBalancerFeignRequestTransformer;
import org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;

import java.util.List;
import java.util.concurrent.TimeUnit;

@AutoConfigureBefore(GalaxyBootFeignAutoConfiguration.class)
@EnableConfigurationProperties({OkHttpClientProperties.class, LoadBalancerClientsProperties.class})
public class GalaxyBootFeignLoadBalancerAutoConfiguration {

    static {
        System.setProperty("spring.cloud.openfeign.okhttp.enabled", "true");
    }

    @Bean
    @ConditionalOnMissingBean
    public ConnectionPool httpClientConnectionPool(OkHttpClientProperties okHttpClientProperties) {
        int maxTotalConnections = okHttpClientProperties.getMaxConnections();
        long timeToLive = okHttpClientProperties.getTimeToLive();
        TimeUnit ttlUnit = okHttpClientProperties.getTimeToLiveUnit();

        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                "GalaxyBoot: Initializing ConnectionPool, maxConnections={}, timeToLive={}, timeToLiveUnit={}",
                maxTotalConnections, timeToLive, ttlUnit);

        return new ConnectionPool(maxTotalConnections, timeToLive, ttlUnit);
    }

    /**
     * Feign Request Options
     * <p>OkHttpClient底层使用的是Request.Option，使用默认配置初始化Request.Option</p>
     *
     * @param properties OkHttpClientProperties，OkHttp配置信息
     * @return Request.Options Feign Request Options
     */
    @Bean
    @ConditionalOnMissingBean
    public Request.Options feignRequestOptions(OkHttpClientProperties properties) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                "GalaxyBoot: Initializing Feign Request Options, connectTimeout={}, readTimeout={}",
                properties.getConnectTimeout(),
                properties.getReadTimeout());

        return new Request.Options(
                properties.getConnectTimeout(), TimeUnit.MILLISECONDS,
                properties.getReadTimeout(), TimeUnit.MILLISECONDS,
                true);
    }

    @Bean
    @ConditionalOnMissingBean
    public okhttp3.OkHttpClient okHttpClient(ConnectionPool connectionPool, OkHttpClientProperties okHttpClientProperties) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                "GalaxyBoot: Initializing OkHttpClient, connectTimeout={}, readTimeout={}, writeTimeout={}",
                okHttpClientProperties.getConnectTimeout(),
                okHttpClientProperties.getReadTimeout(),
                okHttpClientProperties.getWriteTimeout());

        return new okhttp3.OkHttpClient.Builder()
                .connectionPool(connectionPool)
                .connectTimeout(okHttpClientProperties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(okHttpClientProperties.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(okHttpClientProperties.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }

    @Bean
    @ConditionalOnMissingBean
    @Conditional(OnLoadBalancerRetryEnabledCondition.class)
    public Client feignRetryClient(okhttp3.OkHttpClient okHttpClient,
                                   LoadBalancerClient loadBalancerClient,
                                   LoadBalancerClientFactory loadBalancerClientFactory,
                                   LoadBalancedRetryFactory loadBalancedRetryFactory,
                                   List<LoadBalancerFeignRequestTransformer> transformers) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing RetryableFeignBlockingLoadBalancerClient");

        OkHttpClient delegate = new OkHttpClient(okHttpClient);
        return new RetryableFeignBlockingLoadBalancerClient(delegate, loadBalancerClient, loadBalancedRetryFactory,
                loadBalancerClientFactory, transformers);
    }

    @Bean
    @ConditionalOnMissingBean
    @Conditional(OnLoadBalancerEnabledCondition.class)
    public Client feignBlockingLoadBalancerClient(okhttp3.OkHttpClient okHttpClient,
                                                  LoadBalancerClient loadBalancerClient,
                                                  LoadBalancerClientFactory loadBalancerClientFactory,
                                                  List<LoadBalancerFeignRequestTransformer> transformers) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing FeignBlockingLoadBalancerClient");

        OkHttpClient delegate = new OkHttpClient(okHttpClient);
        return new FeignBlockingLoadBalancerClient(delegate, loadBalancerClient, loadBalancerClientFactory, transformers);
    }
}
