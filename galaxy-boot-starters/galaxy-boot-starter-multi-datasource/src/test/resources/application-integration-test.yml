spring:
  galaxy-datasource:
    # 主数据源 - JPA
    primary:
      primary: true
      type: jpa
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.integration.entity.primary
        repository: cn.com.chinastock.cnf.mdatasource.integration.repository.primary
      datasource:
        url: jdbc:h2:mem:primary;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        username: sa
        password: 
        driver-class-name: org.h2.Driver
      jpa:
        hibernate:
          ddl-auto: create-drop
        show-sql: true
        properties:
          hibernate:
            dialect: org.hibernate.dialect.H2Dialect
      hikari:
        maximum-pool-size: 5
        minimum-idle: 1
        connection-timeout: 30000

    # JPA数据源
    jpa:
      primary: false
      type: jpa
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.fixtures.entity.jpa
        repository: cn.com.chinastock.cnf.mdatasource.fixtures.repository.jpa
      datasource:
        url: jdbc:h2:mem:jpa;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        username: sa
        password: 
        driver-class-name: org.h2.Driver
      jpa:
        hibernate:
          ddl-auto: create-drop
        show-sql: true
        properties:
          hibernate:
            dialect: org.hibernate.dialect.H2Dialect
      hikari:
        maximum-pool-size: 5
        minimum-idle: 1

    # MyBatis数据源
    mybatis:
      primary: false
      type: mybatis
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatis
        mapper: cn.com.chinastock.cnf.mdatasource.fixtures.mapper.mybatis
      datasource:
        url: jdbc:h2:mem:mybatis;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        username: sa
        password: 
        driver-class-name: org.h2.Driver
      mybatis:
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatis
      hikari:
        maximum-pool-size: 5
        minimum-idle: 1

    # MyBatisPlus数据源
    mybatis-plus:
      primary: false
      type: mybatis-plus
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatisplus
        mapper: cn.com.chinastock.cnf.mdatasource.fixtures.mapper.mybatisplus
      datasource:
        url: jdbc:h2:mem:mybatisplus;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        username: sa
        password: 
        driver-class-name: org.h2.Driver
      mybatis-plus:
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatisplus
        configuration:
          map-underscore-to-camel-case: true
        global-config:
          db-config:
            id-type: ASSIGN_ID
            logic-delete-field: deleted
            logic-delete-value: 1
            logic-not-delete-value: 0
      hikari:
        maximum-pool-size: 5
        minimum-idle: 1

logging:
  level:
    cn.com.chinastock.cnf.mdatasource: DEBUG
    org.springframework.boot.autoconfigure: DEBUG 