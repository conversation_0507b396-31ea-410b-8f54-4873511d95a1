package cn.com.chinastock.cnf.mdatasource.fixtures.repository.jpa;

import cn.com.chinastock.cnf.mdatasource.fixtures.entity.jpa.JpaUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * JPA测试用户Repository
 *
 * <AUTHOR>
 */
@Repository
public interface JpaUserRepository extends JpaRepository<JpaUser, Long> {
    
    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户
     */
    JpaUser findByEmail(String email);
} 