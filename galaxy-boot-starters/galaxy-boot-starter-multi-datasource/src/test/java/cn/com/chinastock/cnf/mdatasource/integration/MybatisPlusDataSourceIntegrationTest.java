package cn.com.chinastock.cnf.mdatasource.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatisPlus数据源集成测试
 * 测试MyBatisPlus相关的Bean生成和功能
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = cn.com.chinastock.cnf.mdatasource.fixtures.config.TestApplication.class)
@ActiveProfiles("integration-test")
class MybatisPlusDataSourceIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldCreateMybatisPlusDataSourceBeans() {
        // 验证数据源
        DataSource mybatisPlusDataSource = applicationContext.getBean("mybatis-plusDataSource", DataSource.class);
        assertNotNull(mybatisPlusDataSource);
    }

    @Test
    void shouldHaveCorrectMybatisPlusBeanNames() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        
        // 验证MyBatisPlus相关Bean名称
        assertTrue(containsBean(beanNames, "mybatis-plusDataSource"));
        
        // 如果MyBatisPlus依赖可用，验证其他Bean
        if (isMybatisPlusAvailable()) {
            assertTrue(containsBean(beanNames, "mybatis-plusSqlSessionFactory"));
            assertTrue(containsBean(beanNames, "mybatis-plusSqlSessionTemplate"));
            assertTrue(containsBean(beanNames, "mybatis-plusTransactionManager"));
        }
    }

    @Test
    void shouldLoadMybatisPlusConfigurationCorrectly() {
        // 验证MyBatisPlus配置属性
        String url = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis-plus.datasource.url");
        assertNotNull(url);
        assertTrue(url.contains("jdbc:h2:mem:mybatisplus"));

        String typeAliasesPackage = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.type-aliases-package");
        assertNotNull(typeAliasesPackage);
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatisplus", typeAliasesPackage);

        String idType = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.global-config.db-config.id-type");
        assertNotNull(idType);
        assertEquals("ASSIGN_ID", idType);
    }
    
    @Test
    void shouldValidateMybatisPlusDataSourceConfigurationConsistency() {
        // 验证数据源配置与配置文件一致
        assertEquals("jdbc:h2:mem:mybatisplus;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.datasource.url"));
        assertEquals("sa", getProperty("spring.galaxy-datasource.mybatis-plus.datasource.username"));
        assertEquals("", getProperty("spring.galaxy-datasource.mybatis-plus.datasource.password"));
        assertEquals("org.h2.Driver", getProperty("spring.galaxy-datasource.mybatis-plus.datasource.driver-class-name"));
        
        // 验证MyBatisPlus特定配置
        assertEquals("false", getProperty("spring.galaxy-datasource.mybatis-plus.primary"));
        assertEquals("mybatis-plus", getProperty("spring.galaxy-datasource.mybatis-plus.type"));
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatisplus",
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.type-aliases-package"));
        assertEquals("true", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.configuration.map-underscore-to-camel-case"));
        assertEquals("ASSIGN_ID", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.global-config.db-config.id-type"));
        assertEquals("deleted", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.global-config.db-config.logic-delete-field"));
        assertEquals("1", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.global-config.db-config.logic-delete-value"));
        assertEquals("0", 
                    getProperty("spring.galaxy-datasource.mybatis-plus.mybatis-plus.global-config.db-config.logic-not-delete-value"));
        
        // 验证包路径配置
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatisplus",
                    getProperty("spring.galaxy-datasource.mybatis-plus.packages.entity"));
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.mapper.mybatisplus",
                    getProperty("spring.galaxy-datasource.mybatis-plus.packages.mapper"));
        
        // 验证Hikari连接池配置
        assertEquals("5", getProperty("spring.galaxy-datasource.mybatis-plus.hikari.maximum-pool-size"));
        assertEquals("1", getProperty("spring.galaxy-datasource.mybatis-plus.hikari.minimum-idle"));
    }
    
    @Test
    void shouldValidateMybatisPlusDataSourceBeanProperties() {
        if (!isMybatisPlusAvailable()) {
            return; // 如果MyBatisPlus不可用，跳过此测试
        }
        
        // 验证实际创建的DataSource Bean的属性
        DataSource mybatisPlusDataSource = applicationContext.getBean("mybatis-plusDataSource", DataSource.class);
        assertNotNull(mybatisPlusDataSource);
        
        // 验证数据源类型
        assertTrue(mybatisPlusDataSource instanceof com.zaxxer.hikari.HikariDataSource);
        
        // 验证HikariDataSource的具体配置
        com.zaxxer.hikari.HikariDataSource hikariDataSource = (com.zaxxer.hikari.HikariDataSource) mybatisPlusDataSource;
        assertEquals("jdbc:h2:mem:mybatisplus;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE", hikariDataSource.getJdbcUrl());
        assertEquals("sa", hikariDataSource.getUsername());
        assertNull(hikariDataSource.getPassword());
        assertEquals("org.h2.Driver", hikariDataSource.getDriverClassName());
        
        // 验证连接池配置
        assertEquals(5, hikariDataSource.getMaximumPoolSize());
        assertEquals(1, hikariDataSource.getMinimumIdle());
    }

    private boolean isMybatisPlusAvailable() {
        try {
            Class.forName("com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    private boolean containsBean(String[] beanNames, String beanName) {
        for (String name : beanNames) {
            if (name.equals(beanName)) {
                return true;
            }
        }
        return false;
    }
    
    private String getProperty(String key) {
        return applicationContext.getEnvironment().getProperty(key);
    }
} 