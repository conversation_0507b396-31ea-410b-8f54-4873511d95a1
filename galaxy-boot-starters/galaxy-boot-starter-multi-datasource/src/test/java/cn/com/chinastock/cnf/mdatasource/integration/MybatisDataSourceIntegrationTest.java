package cn.com.chinastock.cnf.mdatasource.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis数据源集成测试
 * 测试MyBatis相关的Bean生成和功能
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = cn.com.chinastock.cnf.mdatasource.fixtures.config.TestApplication.class)
@ActiveProfiles("integration-test")
class MybatisDataSourceIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldCreateMybatisDataSourceBeans() {
        // 验证数据源
        DataSource mybatisDataSource = applicationContext.getBean("mybatisDataSource", DataSource.class);
        assertNotNull(mybatisDataSource);
    }

    @Test
    void shouldHaveCorrectMybatisBeanNames() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();

        // 验证MyBatis相关Bean名称
        assertTrue(containsBean(beanNames, "mybatisDataSource"));

        // 如果MyBatis依赖可用，验证其他Bean
        if (isMybatisAvailable()) {
            assertTrue(containsBean(beanNames, "mybatisSqlSessionFactory"));
            assertTrue(containsBean(beanNames, "mybatisSqlSessionTemplate"));
            assertTrue(containsBean(beanNames, "mybatisTransactionManager"));
        }
    }

    @Test
    void shouldLoadMybatisConfigurationCorrectly() {
        // 验证MyBatis配置属性
        String url = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis.datasource.url");
        assertNotNull(url);
        assertTrue(url.contains("jdbc:h2:mem:mybatis"));

        String typeAliasesPackage = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis.mybatis.type-aliases-package");
        assertNotNull(typeAliasesPackage);
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatis", typeAliasesPackage);
    }

    @Test
    void shouldValidateMybatisDataSourceConfigurationConsistency() {
        // 验证数据源配置与配置文件一致
        assertEquals("jdbc:h2:mem:mybatis;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
                getProperty("spring.galaxy-datasource.mybatis.datasource.url"));
        assertEquals("sa", getProperty("spring.galaxy-datasource.mybatis.datasource.username"));
        assertEquals("", getProperty("spring.galaxy-datasource.mybatis.datasource.password"));
        assertEquals("org.h2.Driver", getProperty("spring.galaxy-datasource.mybatis.datasource.driver-class-name"));

        // 验证MyBatis特定配置
        assertEquals("false", getProperty("spring.galaxy-datasource.mybatis.primary"));
        assertEquals("mybatis", getProperty("spring.galaxy-datasource.mybatis.type"));
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatis",
                getProperty("spring.galaxy-datasource.mybatis.mybatis.type-aliases-package"));

        // 验证包路径配置
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.mybatis",
                getProperty("spring.galaxy-datasource.mybatis.packages.entity"));
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.mapper.mybatis",
                getProperty("spring.galaxy-datasource.mybatis.packages.mapper"));

        // 验证Hikari连接池配置
        assertEquals("5", getProperty("spring.galaxy-datasource.mybatis.hikari.maximum-pool-size"));
        assertEquals("1", getProperty("spring.galaxy-datasource.mybatis.hikari.minimum-idle"));
    }

    @Test
    void shouldValidateMybatisDataSourceBeanProperties() {
        if (!isMybatisAvailable()) {
            return; // 如果MyBatis不可用，跳过此测试
        }

        // 验证实际创建的DataSource Bean的属性
        DataSource mybatisDataSource = applicationContext.getBean("mybatisDataSource", DataSource.class);
        assertNotNull(mybatisDataSource);

        // 验证数据源类型
        assertTrue(mybatisDataSource instanceof com.zaxxer.hikari.HikariDataSource);

        // 验证HikariDataSource的具体配置
        com.zaxxer.hikari.HikariDataSource hikariDataSource = (com.zaxxer.hikari.HikariDataSource) mybatisDataSource;
        assertEquals("jdbc:h2:mem:mybatis;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE", hikariDataSource.getJdbcUrl());
        assertEquals("sa", hikariDataSource.getUsername());
        assertNull(hikariDataSource.getPassword());
        assertEquals("org.h2.Driver", hikariDataSource.getDriverClassName());

        // 验证连接池配置
        assertEquals(5, hikariDataSource.getMaximumPoolSize());
        assertEquals(1, hikariDataSource.getMinimumIdle());
    }

    private boolean isMybatisAvailable() {
        try {
            Class.forName("org.apache.ibatis.session.SqlSessionFactory");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    private boolean containsBean(String[] beanNames, String beanName) {
        for (String name : beanNames) {
            if (name.equals(beanName)) {
                return true;
            }
        }
        return false;
    }

    private String getProperty(String key) {
        return applicationContext.getEnvironment().getProperty(key);
    }
} 