package cn.com.chinastock.cnf.mdatasource.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据源Bean验证测试
 * 验证所有数据源Bean的创建和基本属性
 * 各个数据源类型的详细配置验证已移至对应的集成测试类中
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = cn.com.chinastock.cnf.mdatasource.fixtures.config.TestApplication.class)
@ActiveProfiles("integration-test")
class DataSourceBeanValidationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldCreateAllRequiredDataSources() {
        // 验证所有数据源Bean都存在
        List<String> expectedDataSources = Arrays.asList(
            "primaryDataSource",
            "jpaDataSource", 
            "mybatisDataSource",
            "mybatis-plusDataSource"
        );

        for (String dataSourceName : expectedDataSources) {
            DataSource dataSource = applicationContext.getBean(dataSourceName, DataSource.class);
            assertNotNull(dataSource, "DataSource " + dataSourceName + " should be created");
        }
    }

    @Test
    void shouldHaveCorrectBeanCount() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        
        // 验证数据源相关Bean的数量
        long dataSourceCount = Arrays.stream(beanNames)
            .filter(name -> name.contains("DataSource"))
            .count();
        assertTrue(dataSourceCount >= 4, "Should have at least 4 DataSource beans");

        // 验证事务管理器Bean的数量
        long transactionManagerCount = Arrays.stream(beanNames)
            .filter(name -> name.contains("TransactionManager"))
            .count();
        assertTrue(transactionManagerCount >= 4, "Should have at least 4 TransactionManager beans");
    }

    @Test
    void shouldValidatePrimaryDataSource() {
        // 验证主数据源配置
        String primaryUrl = applicationContext.getEnvironment()
            .getProperty("spring.galaxy-datasource.primary.datasource.url");
        assertNotNull(primaryUrl);
        assertTrue(primaryUrl.contains("jdbc:h2:mem:primary"));

        // 验证主数据源Bean
        DataSource primaryDataSource = applicationContext.getBean("primaryDataSource", DataSource.class);
        assertNotNull(primaryDataSource);
    }

    @Test
    void shouldValidateHikariConfiguration() {
        // 验证Hikari连接池配置
        String maxPoolSize = applicationContext.getEnvironment()
            .getProperty("spring.galaxy-datasource.primary.hikari.maximum-pool-size");
        assertNotNull(maxPoolSize);
        assertEquals("5", maxPoolSize);

        String minIdle = applicationContext.getEnvironment()
            .getProperty("spring.galaxy-datasource.primary.hikari.minimum-idle");
        assertNotNull(minIdle);
        assertEquals("1", minIdle);
    }



    private boolean containsBean(String beanName) {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        return Arrays.stream(beanNames).anyMatch(name -> name.equals(beanName));
    }
} 