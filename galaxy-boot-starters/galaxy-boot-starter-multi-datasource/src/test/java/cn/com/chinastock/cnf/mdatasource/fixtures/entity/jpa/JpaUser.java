package cn.com.chinastock.cnf.mdatasource.fixtures.entity.jpa;

import jakarta.persistence.*;

/**
 * JPA测试用户实体
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "jpa_users")
public class JpaUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email", unique = true)
    private String email;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
} 