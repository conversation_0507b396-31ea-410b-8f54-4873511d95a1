package cn.com.chinastock.cnf.mdatasource.unit;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import cn.com.chinastock.cnf.mdatasource.registrar.DataSourceRegistrar;
import cn.com.chinastock.cnf.mdatasource.registrar.DataSourceRegistrarFactory;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataSourceRegistrarFactory 测试类
 *
 * <AUTHOR>
 */
class DataSourceRegistrarFactoryTest {

    @Test
    void shouldGetJPARegistrarSuccessfully() {
        DataSourceDefinition definition = new DataSourceDefinition();
        definition.setType(DataSourceType.JPA);
        
        // 只有在JPA依赖可用时才测试
        if (DataSourceRegistrarFactory.isTypeSupported(DataSourceType.JPA)) {
            DataSourceRegistrar registrar = DataSourceRegistrarFactory.getRegistrar(definition);
            assertNotNull(registrar);
            assertTrue(registrar.supports(definition));
        }
    }

    @Test
    void shouldGetMyBatisRegistrarSuccessfully() {
        DataSourceDefinition definition = new DataSourceDefinition();
        definition.setType(DataSourceType.MYBATIS);
        
        // 只有在MyBatis依赖可用时才测试
        if (DataSourceRegistrarFactory.isTypeSupported(DataSourceType.MYBATIS)) {
            DataSourceRegistrar registrar = DataSourceRegistrarFactory.getRegistrar(definition);
            assertNotNull(registrar);
            assertTrue(registrar.supports(definition));
        }
    }

    @Test
    void shouldGetAvailableRegistrars() {
        assertNotNull(DataSourceRegistrarFactory.getAvailableRegistrars());
        // 至少应该有一个注册器可用（JPA通常是可用的）
        assertFalse(DataSourceRegistrarFactory.getAvailableRegistrars().isEmpty());
    }

}
