<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.chinastock.cnf.mdatasource.integration.mapper.mybatisplus.MybatisPlusUserMapper">

    <resultMap id="BaseResultMap" type="cn.com.chinastock.cnf.mdatasource.integration.entity.mybatisplus.MybatisPlusUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, email, deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mybatisplus_users
        where id = #{id,jdbcType=BIGINT} and deleted = 0
    </select>

    <insert id="insert" parameterType="cn.com.chinastock.cnf.mdatasource.integration.entity.mybatisplus.MybatisPlusUser">
        insert into mybatisplus_users (name, email, deleted)
        values (#{name,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 0)
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mybatisplus_users
        where deleted = 0
    </select>

</mapper> 