<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.chinastock.cnf.mdatasource.integration.mapper.mybatis.MybatisUserMapper">

    <resultMap id="BaseResultMap" type="cn.com.chinastock.cnf.mdatasource.integration.entity.mybatis.MybatisUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, email
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mybatis_users
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="cn.com.chinastock.cnf.mdatasource.integration.entity.mybatis.MybatisUser">
        insert into mybatis_users (name, email)
        values (#{name,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR})
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mybatis_users
    </select>

</mapper> 