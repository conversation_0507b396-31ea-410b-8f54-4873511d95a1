package cn.com.chinastock.cnf.mdatasource.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 多数据源集成测试
 * 测试ApplicationContext启动和Bean生成
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = cn.com.chinastock.cnf.mdatasource.fixtures.config.TestApplication.class)
@ActiveProfiles("integration-test")
class MultiDataSourceIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldStartApplicationContextSuccessfully() {
        assertNotNull(applicationContext);
    }

    @Test
    void shouldCreateAllDataSources() {
        // 验证主数据源
        DataSource primaryDataSource = applicationContext.getBean("primaryDataSource", DataSource.class);
        assertNotNull(primaryDataSource);

        // 验证JPA数据源
        DataSource jpaDataSource = applicationContext.getBean("jpaDataSource", DataSource.class);
        assertNotNull(jpaDataSource);

        // 验证MyBatis数据源
        DataSource mybatisDataSource = applicationContext.getBean("mybatisDataSource", DataSource.class);
        assertNotNull(mybatisDataSource);

        // 验证MyBatisPlus数据源
        DataSource mybatisPlusDataSource = applicationContext.getBean("mybatis-plusDataSource", DataSource.class);
        assertNotNull(mybatisPlusDataSource);
    }

    @Test
    void shouldHaveCorrectBeanNames() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        
        // 验证数据源Bean名称
        assertTrue(containsBean(beanNames, "primaryDataSource"));
        assertTrue(containsBean(beanNames, "jpaDataSource"));
        assertTrue(containsBean(beanNames, "mybatisDataSource"));
        assertTrue(containsBean(beanNames, "mybatis-plusDataSource"));
    }

    @Test
    void shouldLoadConfigurationPropertiesCorrectly() {
        // 验证配置属性是否正确加载
        assertNotNull(applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.primary.datasource.url"));
        assertNotNull(applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.jpa.datasource.url"));
        assertNotNull(applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis.datasource.url"));
        assertNotNull(applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.mybatis-plus.datasource.url"));
    }

    private boolean containsBean(String[] beanNames, String beanName) {
        for (String name : beanNames) {
            if (name.equals(beanName)) {
                return true;
            }
        }
        return false;
    }
} 