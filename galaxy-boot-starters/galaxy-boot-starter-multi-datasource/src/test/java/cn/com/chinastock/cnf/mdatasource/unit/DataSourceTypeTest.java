package cn.com.chinastock.cnf.mdatasource.unit;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataSourceType 枚举测试类
 *
 * <AUTHOR>
 */
class DataSourceTypeTest {

    @Test
    void shouldReturnJAPDatasourceWithConfigurationCaseInsensitively() {
        assertEquals(DataSourceType.JPA, DataSourceType.fromValue("jpa"));
        assertEquals(DataSourceType.JPA, DataSourceType.fromValue("JPA"));
        assertEquals(DataSourceType.JPA, DataSourceType.fromValue("Jpa"));
    }

    @Test
    void shouldReturnMyBatisDatasourceWithConfigurationCaseInsensitively() {
        assertEquals(DataSourceType.MYBATIS, DataSourceType.fromValue("mybatis"));
        assertEquals(DataSourceType.MYBATIS, DataSourceType.fromValue("MYBATIS"));
        assertEquals(DataSourceType.MYBATIS, DataSourceType.fromValue("MyBatis"));
    }

    @Test
    void shouldReturnMyBatisPlusDatasourceWithConfigurationCaseInsensitively() {
        assertEquals(DataSourceType.MYBATIS_PLUS, DataSourceType.fromValue("mybatis-plus"));
        assertEquals(DataSourceType.MYBATIS_PLUS, DataSourceType.fromValue("MYBATIS-PLUS"));
        assertEquals(DataSourceType.MYBATIS_PLUS, DataSourceType.fromValue("MyBatis-Plus"));
    }

    @Test
    void shouldReturnJPADatasourceWithNullConfiguration() {
        assertEquals(DataSourceType.JPA, DataSourceType.fromValue(null));
    }

    @Test
    void shouldThrowExceptionWithInvalidConfiguration() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> DataSourceType.fromValue("invalid")
        );
        assertTrue(exception.getMessage().contains("Unknown DataSourceType: invalid"));
        assertTrue(exception.getMessage().contains("Supported types are: jpa, mybatis"));
    }
}
