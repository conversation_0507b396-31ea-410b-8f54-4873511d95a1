package cn.com.chinastock.cnf.mdatasource.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JPA数据源集成测试
 * 测试JPA相关的Bean生成和功能
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = cn.com.chinastock.cnf.mdatasource.fixtures.config.TestApplication.class)
@ActiveProfiles("integration-test")
class JpaDataSourceIntegrationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldCreateJpaDataSourceBeans() {
        // 验证数据源
        DataSource jpaDataSource = applicationContext.getBean("jpaDataSource", DataSource.class);
        assertNotNull(jpaDataSource);
    }

    @Test
    void shouldHaveCorrectJpaBeanNames() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        
        // 验证JPA相关Bean名称
        assertTrue(containsBean(beanNames, "jpaDataSource"));
        
        // 如果JPA依赖可用，验证其他Bean
        if (isJpaAvailable()) {
            assertTrue(containsBean(beanNames, "jpaEntityManagerFactory"));
            assertTrue(containsBean(beanNames, "jpaTransactionManager"));
            assertTrue(containsBean(beanNames, "jpaSharedEntityManager"));
        }
    }

    @Test
    void shouldLoadJpaConfigurationCorrectly() {
        // 验证JPA配置属性
        String url = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.jpa.datasource.url");
        assertNotNull(url);
        assertTrue(url.contains("jdbc:h2:mem:jpa"));

        String showSql = applicationContext.getEnvironment().getProperty("spring.galaxy-datasource.jpa.jpa.show-sql");
        assertNotNull(showSql);
        assertEquals("true", showSql);
    }
    
    @Test
    void shouldValidateJpaDataSourceConfigurationConsistency() {
        // 验证数据源配置与配置文件一致
        assertEquals("jdbc:h2:mem:jpa;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE", 
                    getProperty("spring.galaxy-datasource.jpa.datasource.url"));
        assertEquals("sa", getProperty("spring.galaxy-datasource.jpa.datasource.username"));
        assertEquals("", getProperty("spring.galaxy-datasource.jpa.datasource.password"));
        assertEquals("org.h2.Driver", getProperty("spring.galaxy-datasource.jpa.datasource.driver-class-name"));
        
        // 验证JPA特定配置
        assertEquals("false", getProperty("spring.galaxy-datasource.jpa.primary"));
        assertEquals("jpa", getProperty("spring.galaxy-datasource.jpa.type"));
        assertEquals("true", getProperty("spring.galaxy-datasource.jpa.jpa.show-sql"));
        assertEquals("create-drop", getProperty("spring.galaxy-datasource.jpa.jpa.hibernate.ddl-auto"));
        assertEquals("org.hibernate.dialect.H2Dialect", 
                    getProperty("spring.galaxy-datasource.jpa.jpa.properties.hibernate.dialect"));
        
        // 验证包路径配置
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.entity.jpa", 
                    getProperty("spring.galaxy-datasource.jpa.packages.entity"));
        assertEquals("cn.com.chinastock.cnf.mdatasource.fixtures.repository.jpa", 
                    getProperty("spring.galaxy-datasource.jpa.packages.repository"));
        
        // 验证Hikari连接池配置
        assertEquals("5", getProperty("spring.galaxy-datasource.jpa.hikari.maximum-pool-size"));
        assertEquals("1", getProperty("spring.galaxy-datasource.jpa.hikari.minimum-idle"));
    }
    
    @Test
    void shouldValidateJpaDataSourceBeanProperties() {
        if (!isJpaAvailable()) {
            return; // 如果JPA不可用，跳过此测试
        }
        
        // 验证实际创建的DataSource Bean的属性
        DataSource jpaDataSource = applicationContext.getBean("jpaDataSource", DataSource.class);
        assertNotNull(jpaDataSource);
        
        // 验证数据源类型
        assertTrue(jpaDataSource instanceof com.zaxxer.hikari.HikariDataSource);
        
        // 验证HikariDataSource的具体配置
        com.zaxxer.hikari.HikariDataSource hikariDataSource = (com.zaxxer.hikari.HikariDataSource) jpaDataSource;
        assertEquals("jdbc:h2:mem:jpa;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE", hikariDataSource.getJdbcUrl());
        assertEquals("sa", hikariDataSource.getUsername());
        assertEquals("", hikariDataSource.getPassword());
        assertEquals("org.h2.Driver", hikariDataSource.getDriverClassName());
        
        // 验证连接池配置
        assertEquals(5, hikariDataSource.getMaximumPoolSize());
        assertEquals(1, hikariDataSource.getMinimumIdle());
    }

    private boolean isJpaAvailable() {
        try {
            Class.forName("org.springframework.orm.jpa.EntityManagerFactory");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    private boolean containsBean(String[] beanNames, String beanName) {
        for (String name : beanNames) {
            if (name.equals(beanName)) {
                return true;
            }
        }
        return false;
    }
    
    private String getProperty(String key) {
        return applicationContext.getEnvironment().getProperty(key);
    }
} 