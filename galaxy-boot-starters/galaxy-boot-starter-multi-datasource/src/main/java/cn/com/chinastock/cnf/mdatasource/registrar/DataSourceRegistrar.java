package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.core.io.ResourceLoader;

/**
 * 数据源注册器接口
 * 定义了注册数据源相关Bean的通用方法
 *
 * <AUTHOR>
 */
public interface DataSourceRegistrar {
    
    /**
     * 注册数据源相关的Bean定义
     * 
     * @param registry Bean定义注册器
     * @param datasourceName 数据源名称
     * @param properties 数据源配置属性
     * @param resourceLoader 资源加载器
     */
    void registerDataSource(BeanDefinitionRegistry registry, 
                           String datasourceName, 
                           DataSourceDefinition properties, 
                           ResourceLoader resourceLoader);
    
    /**
     * 检查是否支持指定的数据源类型
     * 
     * @param properties 数据源配置属性
     * @return true表示支持，false表示不支持
     */
    boolean supports(DataSourceDefinition properties);
}
