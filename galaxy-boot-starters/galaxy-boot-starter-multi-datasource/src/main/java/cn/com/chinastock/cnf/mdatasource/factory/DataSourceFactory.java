package cn.com.chinastock.cnf.mdatasource.factory;

import cn.com.chinastock.cnf.mdatasource.config.HikariDataSourceConfigurer;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.HikariProperties;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;

import javax.sql.DataSource;

/**
 * 数据源工厂类
 * 负责根据配置创建不同类型的数据源
 *
 * <AUTHOR>
 */
public class DataSourceFactory {

    /**
     * 创建数据源
     * 优先使用 Hikari 连接池配置，如果没有配置则使用默认的数据源构建器
     *
     * @param properties 数据源定义
     * @param dataSourceName 数据源名称
     * @return 配置好的数据源
     */
    public static DataSource createDataSource(DataSourceDefinition properties, String dataSourceName) {
        DataSourceProperties dataSourceProperties = properties.getDatasource();
        HikariProperties hikariProperties = properties.getHikari();

        // 检查是否有 Hikari 特定配置
        if (hasHikariConfiguration(hikariProperties)) {
            // 使用 Hikari 配置创建数据源
            return HikariDataSourceConfigurer.createHikariDataSource(
                dataSourceProperties, hikariProperties, dataSourceName);
        } else {
            // 使用默认的数据源构建器（通常也是 Hikari，但使用默认配置）
            return dataSourceProperties.initializeDataSourceBuilder().build();
        }
    }

    /**
     * 检查是否有 Hikari 特定配置
     * 如果用户配置了任何 Hikari 特定属性，则认为需要使用自定义 Hikari 配置
     *
     * @param hikariProperties Hikari属性配置
     * @return 是否有自定义Hikari配置
     */
    private static boolean hasHikariConfiguration(HikariProperties hikariProperties) {
        if (hikariProperties == null) {
            return false;
        }

        // 检查是否有任何非默认的 Hikari 配置
        return hikariProperties.getPoolName() != null ||
               // 对于有默认值的属性，检查是否与默认值不同
               (hikariProperties.getMinimumIdle() != null && !hikariProperties.getMinimumIdle().equals(10)) ||
               (hikariProperties.getMaximumPoolSize() != null && !hikariProperties.getMaximumPoolSize().equals(10)) ||
               (hikariProperties.getConnectionTimeout() != null && hikariProperties.getConnectionTimeout().toSeconds() != 30) ||
               (hikariProperties.getIdleTimeout() != null && hikariProperties.getIdleTimeout().toMinutes() != 10) ||
               (hikariProperties.getMaxLifetime() != null && hikariProperties.getMaxLifetime().toMinutes() != 30) ||
               (hikariProperties.getLeakDetectionThreshold() != null && !hikariProperties.getLeakDetectionThreshold().isZero()) ||
               (hikariProperties.getValidationTimeout() != null && hikariProperties.getValidationTimeout().toSeconds() != 5) ||
               (hikariProperties.getAutoCommit() != null && !hikariProperties.getAutoCommit()) ||
               (hikariProperties.getReadOnly() != null && hikariProperties.getReadOnly()) ||
               hikariProperties.getTransactionIsolation() != null ||
               hikariProperties.getCatalog() != null ||
               hikariProperties.getSchema() != null ||
               hikariProperties.getConnectionTestQuery() != null ||
               hikariProperties.getConnectionInitSql() != null ||
               (hikariProperties.getDataSourceProperties() != null && !hikariProperties.getDataSourceProperties().isEmpty()) ||
               hikariProperties.getMetricRegistry() != null ||
               hikariProperties.getHealthCheckRegistry() != null ||
               (hikariProperties.getAllowPoolSuspension() != null && hikariProperties.getAllowPoolSuspension());
    }

    /**
     * 获取数据源统计信息
     *
     * @param dataSource 数据源
     * @return 连接池统计信息，如果不是 Hikari 数据源则返回 null
     */
    public static HikariDataSourceConfigurer.PoolStats getPoolStats(DataSource dataSource) {
        return HikariDataSourceConfigurer.getPoolStats(dataSource);
    }
}
