package cn.com.chinastock.cnf.mdatasource;

import cn.com.chinastock.cnf.mdatasource.registrar.DynamicDataSourceModuleRegistrar;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * EnableMultiDatasource 注解用于启用多数据源功能。
 * 该注解会导入 DynamicDataSourceModuleRegistrar 类，自动扫描配置文件中的多数据源配置，
 * 支持JPA、MyBatis和MyBatisPlus三种ORM框架。
 *
 * <p>使用示例：</p>
 * <pre>
 * &#64;SpringBootApplication
 * &#64;EnableMultiDatasource
 * public class Application {
 *     public static void main(String[] args) {
 *         SpringApplication.run(Application.class, args);
 *     }
 * }
 * </pre>
 *
 * <p>配置示例：</p>
 * <pre>
 * spring:
 *   galaxy-datasource:
 *     user:
 *       primary: true
 *       type: jpa  # 或 mybatis 或 mybatis-plus
 *       packages:
 *         entity: com.example.user.entity
 *         repository: com.example.user.repository
 *       datasource:
 *         url: ***********************************
 *         username: root
 *         password: password
 * </pre>
 *
 * <AUTHOR>
 * @see DynamicDataSourceModuleRegistrar
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(DynamicDataSourceModuleRegistrar.class)
public @interface EnableMultiDatasource {
}
