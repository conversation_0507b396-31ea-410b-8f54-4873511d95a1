package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据源注册器工厂
 * 负责根据数据源类型创建相应的注册器
 *
 * <AUTHOR>
 */
public class DataSourceRegistrarFactory {
    
    private static final List<DataSourceRegistrar> REGISTRARS = new ArrayList<>();
    
    static {
        // 检查JPA是否可用
        if (isJpaAvailable()) {
            REGISTRARS.add(new JpaDataSourceRegistrar());
        }
        
        // 检查MyBatis是否可用
        if (isMybatisAvailable()) {
            REGISTRARS.add(new MybatisDataSourceRegistrar());
        }

        // 检查MyBatisPlus是否可用
        if (isMybatisPlusAvailable()) {
            REGISTRARS.add(new MybatisPlusDataSourceRegistrar());
        }
    }
    
    /**
     * 根据数据源配置获取相应的注册器
     * 
     * @param properties 数据源配置属性
     * @return 对应的数据源注册器
     * @throws IllegalArgumentException 如果找不到支持的注册器
     */
    public static DataSourceRegistrar getRegistrar(DataSourceDefinition properties) {
        for (DataSourceRegistrar registrar : REGISTRARS) {
            if (registrar.supports(properties)) {
                return registrar;
            }
        }
        
        throw new IllegalArgumentException(
            "No suitable DataSourceRegistrar found for type: " + properties.getType() +
            ". Please ensure the corresponding dependency is included in your project. " +
            "For JPA: spring-boot-starter-data-jpa, For MyBatis: mybatis-spring-boot-starter, " +
            "For MyBatisPlus: mybatis-plus-spring-boot3-starter");
    }
    
    /**
     * 检查JPA是否可用
     *
     * @return true表示JPA可用，false表示不可用
     */
    private static boolean isJpaAvailable() {
        try {
            Class.forName("org.springframework.data.jpa.repository.JpaRepository");
            // 使用Jakarta EE的EntityManager而不是javax
            Class.forName("jakarta.persistence.EntityManager");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 检查MyBatis是否可用
     *
     * @return true表示MyBatis可用，false表示不可用
     */
    private static boolean isMybatisAvailable() {
        try {
            Class.forName("org.apache.ibatis.session.SqlSessionFactory");
            Class.forName("org.mybatis.spring.SqlSessionFactoryBean");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 检查MyBatisPlus是否可用
     *
     * @return true表示MyBatisPlus可用，false表示不可用
     */
    private static boolean isMybatisPlusAvailable() {
        try {
            Class.forName("com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean");
            Class.forName("com.baomidou.mybatisplus.core.mapper.BaseMapper");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 获取所有可用的注册器
     * 
     * @return 可用的注册器列表
     */
    public static List<DataSourceRegistrar> getAvailableRegistrars() {
        return new ArrayList<>(REGISTRARS);
    }
    
    /**
     * 检查指定类型的数据源是否支持
     * 
     * @param type 数据源类型
     * @return true表示支持，false表示不支持
     */
    public static boolean isTypeSupported(DataSourceType type) {
        return REGISTRARS.stream().anyMatch(registrar -> {
            DataSourceDefinition testProps = new DataSourceDefinition();
            testProps.setType(type);
            return registrar.supports(testProps);
        });
    }
}
