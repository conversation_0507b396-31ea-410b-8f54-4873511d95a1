package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.factory.DataSourceFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import cn.com.chinastock.cnf.mdatasource.properties.MybatisProperties;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

/**
 * MyBatis数据源注册器
 * 负责注册MyBatis相关的Bean定义
 *
 * <AUTHOR>
 */
public class MybatisDataSourceRegistrar implements DataSourceRegistrar {
    
    @Override
    public boolean supports(DataSourceDefinition properties) {
        return properties.getType() == DataSourceType.MYBATIS;
    }
    
    @Override
    public void registerDataSource(BeanDefinitionRegistry registry, 
                                 String datasourceName, 
                                 DataSourceDefinition properties, 
                                 ResourceLoader resourceLoader) {
        
        String dataSourceBeanName = datasourceName + "DataSource";
        String sqlSessionFactoryBeanName = datasourceName + "SqlSessionFactory";
        String sqlSessionTemplateBeanName = datasourceName + "SqlSessionTemplate";
        String transactionManagerBeanName = datasourceName + "TransactionManager";

        // 注册DataSource
        registerDataSourceBean(registry, dataSourceBeanName, properties);
        
        // 注册SqlSessionFactory
        registerSqlSessionFactoryBean(registry, sqlSessionFactoryBeanName, 
                                    dataSourceBeanName, properties, resourceLoader);
        
        // 注册SqlSessionTemplate
        registerSqlSessionTemplateBean(registry, sqlSessionTemplateBeanName, 
                                     sqlSessionFactoryBeanName, properties);
        
        // 注册TransactionManager
        registerTransactionManagerBean(registry, transactionManagerBeanName, 
                                     dataSourceBeanName, properties);
        
        // 注册MyBatis Mappers
        registerMybatisMappers(registry, properties, sqlSessionFactoryBeanName, resourceLoader);
    }
    
    private void registerDataSourceBean(BeanDefinitionRegistry registry,
                                      String beanName,
                                      DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                () -> DataSourceFactory.createDataSource(properties, beanName));
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionFactoryBean(BeanDefinitionRegistry registry,
                                             String beanName,
                                             String dataSourceBeanName,
                                             DataSourceDefinition properties,
                                             ResourceLoader resourceLoader) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionFactoryBean.class);
        
        builder.addPropertyReference("dataSource", dataSourceBeanName);
        
        // 设置MyBatis配置
        MybatisProperties mybatisProps = properties.getMybatis();
        
        // 设置配置文件位置
        if (StringUtils.hasText(mybatisProps.getConfigLocation())) {
            Resource configResource = resourceLoader.getResource(mybatisProps.getConfigLocation());
            builder.addPropertyValue("configLocation", configResource);
        }
        
        // 设置Mapper XML文件位置
        if (!mybatisProps.getMapperLocations().isEmpty()) {
            List<Resource> mapperResources = new ArrayList<>();
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(resourceLoader);
            
            for (String location : mybatisProps.getMapperLocations()) {
                try {
                    Resource[] resources = resolver.getResources(location);
                    for (Resource resource : resources) {
                        mapperResources.add(resource);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to load mapper resources from: " + location, e);
                }
            }
            
            if (!mapperResources.isEmpty()) {
                builder.addPropertyValue("mapperLocations", mapperResources.toArray(new Resource[0]));
            }
        }
        
        // 设置配置属性
        if (!mybatisProps.getConfiguration().isEmpty()) {
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            mybatisProps.getConfiguration().forEach((key, value) -> {
                String keyStr = key.toString();
                if ("mapUnderscoreToCamelCase".equals(keyStr)) {
                    configuration.setMapUnderscoreToCamelCase(Boolean.parseBoolean(value.toString()));
                }
                // 可以根据需要添加更多配置项
            });
            builder.addPropertyValue("configuration", configuration);
        }
        
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionTemplateBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String sqlSessionFactoryBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionTemplate.class);
        builder.addConstructorArgReference(sqlSessionFactoryBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerTransactionManagerBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String dataSourceBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSourceTransactionManager.class);
        builder.addConstructorArgReference(dataSourceBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerMybatisMappers(BeanDefinitionRegistry registry, 
                                      DataSourceDefinition properties, 
                                      String sqlSessionFactoryBeanName,
                                      ResourceLoader resourceLoader) {
        ClassPathScanningCandidateComponentProvider scanner = new MapperScanner(resourceLoader);

        properties.getPackages().getMapper().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> mapperClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(mapperClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MapperFactoryBean.class);
                builder.addConstructorArgValue(mapperClass);
                builder.addPropertyReference("sqlSessionFactory", sqlSessionFactoryBeanName);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }
    
    private void registerBean(String beanName, 
                            BeanDefinitionRegistry registry, 
                            DataSourceDefinition properties, 
                            BeanDefinitionBuilder builder) {
        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    private Class<?> classForName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Cannot load class: " + className, e);
        }
    }
    
    /**
     * Mapper扫描器内部类
     * 用于扫描指定包路径下的Mapper接口
     */
    private static class MapperScanner extends ClassPathScanningCandidateComponentProvider {
        
        /**
         * 构造函数
         * 
         * @param resourceLoader 资源加载器
         */
        public MapperScanner(ResourceLoader resourceLoader) {
            super(false);
            super.setResourceLoader(resourceLoader);
            super.addIncludeFilter(new AnnotationTypeFilter(Mapper.class));
        }

        /**
         * 判断是否为候选组件
         * 只有接口类型才被认为是候选组件
         * 
         * @param beanDefinition Bean定义
         * @return true表示是候选组件，false表示不是
         */
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface();
        }
    }
}
