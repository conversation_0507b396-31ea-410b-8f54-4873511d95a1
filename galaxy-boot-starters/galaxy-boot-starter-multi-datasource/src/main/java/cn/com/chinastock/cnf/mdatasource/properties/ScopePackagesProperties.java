package cn.com.chinastock.cnf.mdatasource.properties;

import java.util.ArrayList;
import java.util.List;

/**
 * ScopePackagesProperties 类用于配置实体、仓库和Mapper的包路径。
 * 该类定义了多数据源环境中每个数据源对应的实体类、Repository接口和Mapper接口的包路径，
 * 确保不同数据源的实体、Repository和Mapper能够正确分离和管理。
 *
 * <AUTHOR>
 */
public class ScopePackagesProperties {

    /**
     * 实体类包路径列表
     */
    private List<String> entity = new ArrayList<>();

    /**
     * Repository接口包路径列表
     */
    private List<String> repository = new ArrayList<>();

    /**
     * MyBatis Mapper接口包路径列表
     */
    private List<String> mapper = new ArrayList<>();

    /**
     * 获取实体类包路径列表
     * 
     * @return 实体类包路径列表
     */
    public List<String> getEntity() {
        return entity;
    }

    /**
     * 设置实体类包路径列表
     * 
     * @param entity 实体类包路径列表
     */
    public void setEntity(List<String> entity) {
        this.entity = entity;
    }

    /**
     * 获取Repository接口包路径列表
     * 
     * @return Repository接口包路径列表
     */
    public List<String> getRepository() {
        return repository;
    }

    /**
     * 设置Repository接口包路径列表
     *
     * @param repository Repository接口包路径列表
     */
    public void setRepository(List<String> repository) {
        this.repository = repository;
    }

    /**
     * 获取MyBatis Mapper接口包路径列表
     *
     * @return MyBatis Mapper接口包路径列表
     */
    public List<String> getMapper() {
        return mapper;
    }

    /**
     * 设置MyBatis Mapper接口包路径列表
     *
     * @param mapper MyBatis Mapper接口包路径列表
     */
    public void setMapper(List<String> mapper) {
        this.mapper = mapper;
    }
}