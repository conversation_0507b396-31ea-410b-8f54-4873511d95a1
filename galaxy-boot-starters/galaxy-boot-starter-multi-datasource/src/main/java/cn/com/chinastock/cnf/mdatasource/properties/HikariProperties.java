package cn.com.chinastock.cnf.mdatasource.properties;

import java.time.Duration;
import java.util.Properties;

/**
 * Hikari 连接池配置属性
 * 对应 HikariCP 连接池的所有配置选项
 *
 * <AUTHOR>
 */
public class HikariProperties {

    /**
     * 连接池名称
     */
    private String poolName;

    /**
     * 连接池中维护的最小空闲连接数
     * 默认值：与 maximumPoolSize 相同
     */
    private Integer minimumIdle = 10;

    /**
     * 连接池中允许的最大连接数，包括空闲和使用中的连接
     * 默认值：10
     */
    private Integer maximumPoolSize = 10;

    /**
     * 客户端等待连接池连接的最大毫秒数
     * 默认值：30000 (30秒)
     */
    private Duration connectionTimeout = Duration.ofSeconds(30);

    /**
     * 连接允许在池中闲置的最长时间
     * 默认值：600000 (10分钟)
     */
    private Duration idleTimeout = Duration.ofMinutes(10);

    /**
     * 连接池中连接的最长生命周期
     * 默认值：1800000 (30分钟)
     */
    private Duration maxLifetime = Duration.ofMinutes(30);

    /**
     * 连接泄漏检测阈值，0表示禁用泄漏检测
     * 默认值：0 (禁用)
     */
    private Duration leakDetectionThreshold = Duration.ZERO;

    /**
     * 验证连接有效性的超时时间
     * 默认值：5000 (5秒)
     */
    private Duration validationTimeout = Duration.ofSeconds(5);

    /**
     * 是否自动提交事务
     * 默认值：true
     */
    private Boolean autoCommit = true;

    /**
     * 连接只读模式
     * 默认值：false
     */
    private Boolean readOnly = false;

    /**
     * 连接隔离级别
     * 可选值：TRANSACTION_READ_UNCOMMITTED, TRANSACTION_READ_COMMITTED, 
     *        TRANSACTION_REPEATABLE_READ, TRANSACTION_SERIALIZABLE
     */
    private String transactionIsolation;

    /**
     * 连接的默认catalog
     */
    private String catalog;

    /**
     * 连接的默认schema
     */
    private String schema;

    /**
     * 用于测试连接是否有效的SQL查询
     */
    private String connectionTestQuery;

    /**
     * 连接初始化SQL
     */
    private String connectionInitSql;

    /**
     * 数据源属性
     */
    private Properties dataSourceProperties = new Properties();

    /**
     * 健康检查属性
     */
    private HealthCheckProperties healthCheckProperties = new HealthCheckProperties();

    /**
     * 指标注册表名称
     */
    private String metricRegistry;

    /**
     * 健康检查注册表名称
     */
    private String healthCheckRegistry;

    /**
     * 是否允许池暂停
     * 默认值：false
     */
    private Boolean allowPoolSuspension = false;

    // Getter and Setter methods

    public String getPoolName() {
        return poolName;
    }

    public void setPoolName(String poolName) {
        this.poolName = poolName;
    }

    public Integer getMinimumIdle() {
        return minimumIdle;
    }

    public void setMinimumIdle(Integer minimumIdle) {
        this.minimumIdle = minimumIdle;
    }

    public Integer getMaximumPoolSize() {
        return maximumPoolSize;
    }

    public void setMaximumPoolSize(Integer maximumPoolSize) {
        this.maximumPoolSize = maximumPoolSize;
    }

    public Duration getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(Duration connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public Duration getIdleTimeout() {
        return idleTimeout;
    }

    public void setIdleTimeout(Duration idleTimeout) {
        this.idleTimeout = idleTimeout;
    }

    public Duration getMaxLifetime() {
        return maxLifetime;
    }

    public void setMaxLifetime(Duration maxLifetime) {
        this.maxLifetime = maxLifetime;
    }

    public Duration getLeakDetectionThreshold() {
        return leakDetectionThreshold;
    }

    public void setLeakDetectionThreshold(Duration leakDetectionThreshold) {
        this.leakDetectionThreshold = leakDetectionThreshold;
    }

    public Duration getValidationTimeout() {
        return validationTimeout;
    }

    public void setValidationTimeout(Duration validationTimeout) {
        this.validationTimeout = validationTimeout;
    }

    public Boolean getAutoCommit() {
        return autoCommit;
    }

    public void setAutoCommit(Boolean autoCommit) {
        this.autoCommit = autoCommit;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getTransactionIsolation() {
        return transactionIsolation;
    }

    public void setTransactionIsolation(String transactionIsolation) {
        this.transactionIsolation = transactionIsolation;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public String getConnectionTestQuery() {
        return connectionTestQuery;
    }

    public void setConnectionTestQuery(String connectionTestQuery) {
        this.connectionTestQuery = connectionTestQuery;
    }

    public String getConnectionInitSql() {
        return connectionInitSql;
    }

    public void setConnectionInitSql(String connectionInitSql) {
        this.connectionInitSql = connectionInitSql;
    }

    public Properties getDataSourceProperties() {
        return dataSourceProperties;
    }

    public void setDataSourceProperties(Properties dataSourceProperties) {
        this.dataSourceProperties = dataSourceProperties;
    }

    public HealthCheckProperties getHealthCheckProperties() {
        return healthCheckProperties;
    }

    public void setHealthCheckProperties(HealthCheckProperties healthCheckProperties) {
        this.healthCheckProperties = healthCheckProperties;
    }

    public String getMetricRegistry() {
        return metricRegistry;
    }

    public void setMetricRegistry(String metricRegistry) {
        this.metricRegistry = metricRegistry;
    }

    public String getHealthCheckRegistry() {
        return healthCheckRegistry;
    }

    public void setHealthCheckRegistry(String healthCheckRegistry) {
        this.healthCheckRegistry = healthCheckRegistry;
    }

    public Boolean getAllowPoolSuspension() {
        return allowPoolSuspension;
    }

    public void setAllowPoolSuspension(Boolean allowPoolSuspension) {
        this.allowPoolSuspension = allowPoolSuspension;
    }

    /**
     * 健康检查配置属性
     */
    public static class HealthCheckProperties {
        
        /**
         * 是否启用健康检查
         */
        private Boolean enabled = true;

        /**
         * 健康检查超时时间
         */
        private Duration timeout = Duration.ofSeconds(1);

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Duration getTimeout() {
            return timeout;
        }

        public void setTimeout(Duration timeout) {
            this.timeout = timeout;
        }
    }
}
