package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.factory.DataSourceFactory;
import cn.com.chinastock.cnf.mdatasource.factory.EntityManagerFactoryBuilderFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean;
import org.springframework.data.repository.Repository;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;

/**
 * JPA数据源注册器
 * 负责注册JPA相关的Bean定义
 *
 * <AUTHOR>
 */
public class JpaDataSourceRegistrar implements DataSourceRegistrar {
    
    @Override
    public boolean supports(DataSourceDefinition properties) {
        return properties.getType() == DataSourceType.JPA;
    }
    
    @Override
    public void registerDataSource(BeanDefinitionRegistry registry, 
                                 String datasourceName, 
                                 DataSourceDefinition properties, 
                                 ResourceLoader resourceLoader) {
        
        String dataSourceBeanName = datasourceName + "DataSource";
        String entityManagerFactoryBeanName = datasourceName + "EntityManagerFactory";
        String transactionManagerBeanName = datasourceName + "TransactionManager";
        String sharedEntityManagerBeanName = datasourceName + "SharedEntityManager";

        // 注册DataSource
        registerDataSourceBean(registry, dataSourceBeanName, properties);
        
        // 注册EntityManagerFactory
        registerEntityManagerFactoryBean(registry, entityManagerFactoryBeanName, 
                                       dataSourceBeanName, datasourceName, properties);
        
        // 注册TransactionManager
        registerTransactionManagerBean(registry, transactionManagerBeanName, 
                                     entityManagerFactoryBeanName, properties);
        
        // 注册SharedEntityManager
        registerSharedEntityManagerBean(registry, sharedEntityManagerBeanName, 
                                      entityManagerFactoryBeanName);
        
        // 注册JPA Repositories
        registerJpaRepositories(registry, properties, sharedEntityManagerBeanName, 
                              transactionManagerBeanName, resourceLoader);
    }
    
    private void registerDataSourceBean(BeanDefinitionRegistry registry,
                                      String beanName,
                                      DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                () -> DataSourceFactory.createDataSource(properties, beanName));
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerEntityManagerFactoryBean(BeanDefinitionRegistry registry,
                                                String beanName,
                                                String dataSourceBeanName,
                                                String datasourceName,
                                                DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(EntityManagerFactoryBuilderFactory.class);
        
        builder.addConstructorArgValue(properties.getJpa());
        builder.addConstructorArgReference(dataSourceBeanName);
        String[] entityPackages = properties.getPackages().getEntity().toArray(new String[0]);
        builder.addConstructorArgValue(entityPackages);
        builder.addConstructorArgValue(datasourceName);
        
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerTransactionManagerBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String entityManagerFactoryBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(JpaTransactionManager.class);
        builder.addPropertyReference("entityManagerFactory", entityManagerFactoryBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSharedEntityManagerBean(BeanDefinitionRegistry registry,
                                               String beanName,
                                               String entityManagerFactoryBeanName) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.rootBeanDefinition(SharedEntityManagerCreator.class, "createSharedEntityManager");
        
        builder.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
        builder.addConstructorArgReference(entityManagerFactoryBeanName);
        
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    private void registerJpaRepositories(BeanDefinitionRegistry registry, 
                                       DataSourceDefinition properties, 
                                       String sharedEntityManagerBeanName, 
                                       String transactionManagerBeanName,
                                       ResourceLoader resourceLoader) {
        ClassPathScanningCandidateComponentProvider scanner = new RepositoryScanner(resourceLoader);

        properties.getPackages().getRepository().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> repositoryClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(repositoryClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(JpaRepositoryFactoryBean.class);
                builder.addConstructorArgValue(repositoryClass);
                builder.addPropertyReference("entityManager", sharedEntityManagerBeanName);
                builder.addPropertyValue("transactionManager", transactionManagerBeanName);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }
    
    private void registerBean(String beanName, 
                            BeanDefinitionRegistry registry, 
                            DataSourceDefinition properties, 
                            BeanDefinitionBuilder builder) {
        builder.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
        
        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    private Class<?> classForName(String className) throws IllegalStateException {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new IllegalStateException("Could not find class: " + className, e);
        }
    }

    /**
     * Repository扫描器内部类
     * 用于扫描指定包路径下的Repository接口
     */
    private static class RepositoryScanner extends ClassPathScanningCandidateComponentProvider {

        /**
         * 构造函数
         *
         * @param resourceLoader 资源加载器
         */
        public RepositoryScanner(ResourceLoader resourceLoader) {
            super(false);
            super.setResourceLoader(resourceLoader);
            super.addIncludeFilter(new AssignableTypeFilter(Repository.class));
        }

        /**
         * 判断是否为候选组件
         * 只有接口类型才被认为是候选组件
         *
         * @param beanDefinition Bean定义
         * @return true表示是候选组件，false表示不是
         */
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface();
        }
    }
}
