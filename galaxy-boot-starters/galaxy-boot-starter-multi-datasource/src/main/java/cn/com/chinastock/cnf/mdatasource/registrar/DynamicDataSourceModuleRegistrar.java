package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Collections;
import java.util.Map;

/**
 * DynamicDataSourceModuleRegistrar 类是多数据源模块的动态注册器。
 * 该类实现了 ImportBeanDefinitionRegistrar 接口，负责解析配置文件中的多数据源配置，
 * 并为每个数据源动态创建相应的DataSource、EntityManagerFactory/SqlSessionFactory、TransactionManager等Bean。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>解析 spring.galaxy-datasource 配置</li>
 *     <li>验证主数据源配置的唯一性</li>
 *     <li>根据配置的type字段选择JPA或MyBatis注册器</li>
 *     <li>为每个数据源创建完整的ORM基础设施</li>
 *     <li>扫描并注册Repository/Mapper接口</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class DynamicDataSourceModuleRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware, ResourceLoaderAware {

    private Environment environment;
    private ResourceLoader resourceLoader;

    /**
     * 设置Spring环境对象
     * 
     * @param environment Spring环境对象，用于读取配置属性
     */
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     * 设置资源加载器
     * 
     * @param resourceLoader 资源加载器，用于加载配置文件等资源
     */
    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 注册Bean定义的主要方法
     * 解析多数据源配置并为每个数据源创建相应的Bean定义
     * 
     * @param importingClassMetadata 导入类的注解元数据
     * @param registry Bean定义注册器
     */
    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        Bindable<Map<String, DataSourceDefinition>> mapBindable = Bindable.mapOf(String.class, DataSourceDefinition.class);
        Map<String, DataSourceDefinition> datasourcesMap = Binder.get(environment).bind("spring.galaxy-datasource", mapBindable)
                .orElseGet(Collections::emptyMap);

        if (datasourcesMap.isEmpty()) {
            return;
        }

        // 验证主数据源配置
        verifyPrimaryDatasource(datasourcesMap);
        
        // 验证数据源类型配置
        verifyDataSourceTypes(datasourcesMap);

        // 为每个数据源注册相应的Bean
        datasourcesMap.forEach((name, properties) -> {
            registerDataSource(registry, name, properties);
        });
    }

    /**
     * 验证主数据源配置的唯一性
     * 确保最多只有一个数据源被标记为主数据源
     * 
     * @param datasourcesMap 数据源配置映射
     * @throws IllegalArgumentException 如果有多个主数据源
     */
    private void verifyPrimaryDatasource(Map<String, DataSourceDefinition> datasourcesMap) {
        long primaryCount = datasourcesMap.values().stream()
                .mapToLong(definition -> definition.isPrimary() ? 1 : 0)
                .sum();

        if (primaryCount > 1) {
            throw new IllegalArgumentException("Multiple primary datasources found. Only one datasource can be marked as primary.");
        }
    }
    
    /**
     * 验证数据源类型配置
     * 确保所有配置的数据源类型都有对应的依赖支持
     * 
     * @param datasourcesMap 数据源配置映射
     * @throws IllegalArgumentException 如果有不支持的数据源类型
     */
    private void verifyDataSourceTypes(Map<String, DataSourceDefinition> datasourcesMap) {
        for (Map.Entry<String, DataSourceDefinition> entry : datasourcesMap.entrySet()) {
            String name = entry.getKey();
            DataSourceDefinition definition = entry.getValue();
            DataSourceType type = definition.getType();
            
            if (!DataSourceRegistrarFactory.isTypeSupported(type)) {
                throw new IllegalArgumentException(
                    String.format("DataSource '%s' is configured with type '%s', but the required dependency is not available. " +
                                "For JPA: add spring-boot-starter-data-jpa, For MyBatis: add mybatis-spring-boot-starter, " +
                                "For MyBatisPlus: mybatis-plus-spring-boot3-starter",
                                name, type.getValue()));
            }
        }
    }

    /**
     * 注册单个数据源的所有相关Bean
     * 根据数据源类型选择相应的注册器进行注册
     * 
     * @param registry Bean定义注册器
     * @param datasourceName 数据源名称
     * @param properties 数据源配置属性
     */
    private void registerDataSource(BeanDefinitionRegistry registry, String datasourceName, DataSourceDefinition properties) {
        try {
            DataSourceRegistrar registrar = DataSourceRegistrarFactory.getRegistrar(properties);
            registrar.registerDataSource(registry, datasourceName, properties, resourceLoader);
        } catch (Exception e) {
            throw new RuntimeException("Failed to register datasource: " + datasourceName, e);
        }
    }
}
