package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.factory.DataSourceFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import cn.com.chinastock.cnf.mdatasource.properties.MybatisPlusProperties;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

/**
 * MyBatisPlus数据源注册器
 * 负责注册MyBatisPlus相关的Bean定义
 *
 * <AUTHOR>
 */
public class MybatisPlusDataSourceRegistrar implements DataSourceRegistrar {
    
    @Override
    public boolean supports(DataSourceDefinition properties) {
        return properties.getType() == DataSourceType.MYBATIS_PLUS;
    }
    
    @Override
    public void registerDataSource(BeanDefinitionRegistry registry, 
                                 String datasourceName, 
                                 DataSourceDefinition properties, 
                                 ResourceLoader resourceLoader) {
        
        String dataSourceBeanName = datasourceName + "DataSource";
        String sqlSessionFactoryBeanName = datasourceName + "SqlSessionFactory";
        String sqlSessionTemplateBeanName = datasourceName + "SqlSessionTemplate";
        String transactionManagerBeanName = datasourceName + "TransactionManager";

        // 注册DataSource
        registerDataSourceBean(registry, dataSourceBeanName, properties);
        
        // 注册SqlSessionFactory
        registerSqlSessionFactoryBean(registry, sqlSessionFactoryBeanName, 
                                    dataSourceBeanName, properties, resourceLoader);
        
        // 注册SqlSessionTemplate
        registerSqlSessionTemplateBean(registry, sqlSessionTemplateBeanName, 
                                     sqlSessionFactoryBeanName, properties);
        
        // 注册TransactionManager
        registerTransactionManagerBean(registry, transactionManagerBeanName, 
                                     dataSourceBeanName, properties);
        
        // 注册MyBatisPlus Mappers
        registerMybatisPlusMappers(registry, properties, sqlSessionFactoryBeanName, resourceLoader);
    }
    
    private void registerDataSourceBean(BeanDefinitionRegistry registry,
                                      String beanName,
                                      DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                () -> DataSourceFactory.createDataSource(properties, beanName));
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionFactoryBean(BeanDefinitionRegistry registry,
                                             String beanName,
                                             String dataSourceBeanName,
                                             DataSourceDefinition properties,
                                             ResourceLoader resourceLoader) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MybatisSqlSessionFactoryBean.class);
        
        builder.addPropertyReference("dataSource", dataSourceBeanName);
        
        // 设置MyBatisPlus配置
        MybatisPlusProperties mybatisPlusProps = properties.getMybatisPlus();
        
        // 设置配置文件位置
        if (StringUtils.hasText(mybatisPlusProps.getConfigLocation())) {
            Resource configResource = resourceLoader.getResource(mybatisPlusProps.getConfigLocation());
            builder.addPropertyValue("configLocation", configResource);
        }
        
        // 设置Mapper XML文件位置
        if (!mybatisPlusProps.getMapperLocations().isEmpty()) {
            List<Resource> mapperResources = new ArrayList<>();
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(resourceLoader);
            
            for (String location : mybatisPlusProps.getMapperLocations()) {
                try {
                    Resource[] resources = resolver.getResources(location);
                    for (Resource resource : resources) {
                        mapperResources.add(resource);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to load mapper resources from: " + location, e);
                }
            }
            
            if (!mapperResources.isEmpty()) {
                builder.addPropertyValue("mapperLocations", mapperResources.toArray(new Resource[0]));
            }
        }
        
        // 设置类型别名包路径
        if (StringUtils.hasText(mybatisPlusProps.getTypeAliasesPackage())) {
            builder.addPropertyValue("typeAliasesPackage", mybatisPlusProps.getTypeAliasesPackage());
        }
        
        // 设置类型处理器包路径
        if (StringUtils.hasText(mybatisPlusProps.getTypeHandlersPackage())) {
            builder.addPropertyValue("typeHandlersPackage", mybatisPlusProps.getTypeHandlersPackage());
        }
        
        // 设置MyBatis配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        
        // 应用基础配置属性
        mybatisPlusProps.getConfiguration().forEach((key, value) -> {
            String keyStr = key.toString();
            if ("mapUnderscoreToCamelCase".equals(keyStr)) {
                configuration.setMapUnderscoreToCamelCase(Boolean.parseBoolean(value.toString()));
            }
            // 可以根据需要添加更多配置项
        });
        
        builder.addPropertyValue("configuration", configuration);
        
        // 设置全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        
        MybatisPlusProperties.DbConfig configDbConfig = mybatisPlusProps.getGlobalConfig().getDbConfig();
        
        // 设置主键类型
        try {
            IdType idType = IdType.valueOf(configDbConfig.getIdType());
            dbConfig.setIdType(idType);
        } catch (IllegalArgumentException e) {
            // 使用默认值
            dbConfig.setIdType(IdType.ASSIGN_ID);
        }
        
        // 设置表名前缀
        if (StringUtils.hasText(configDbConfig.getTablePrefix())) {
            dbConfig.setTablePrefix(configDbConfig.getTablePrefix());
        }
        
        // 设置字段策略
        try {
            FieldStrategy fieldStrategy = FieldStrategy.valueOf(configDbConfig.getFieldStrategy());
            dbConfig.setInsertStrategy(fieldStrategy);
            dbConfig.setUpdateStrategy(fieldStrategy);
            dbConfig.setWhereStrategy(fieldStrategy);
        } catch (IllegalArgumentException e) {
            // 使用默认值
            dbConfig.setInsertStrategy(FieldStrategy.NOT_NULL);
            dbConfig.setUpdateStrategy(FieldStrategy.NOT_NULL);
            dbConfig.setWhereStrategy(FieldStrategy.NOT_NULL);
        }
        
        // 设置逻辑删除配置
        if (StringUtils.hasText(configDbConfig.getLogicDeleteField())) {
            dbConfig.setLogicDeleteField(configDbConfig.getLogicDeleteField());
            dbConfig.setLogicDeleteValue(configDbConfig.getLogicDeleteValue());
            dbConfig.setLogicNotDeleteValue(configDbConfig.getLogicNotDeleteValue());
        }
        
        globalConfig.setDbConfig(dbConfig);
        builder.addPropertyValue("globalConfig", globalConfig);
        
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionTemplateBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String sqlSessionFactoryBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionTemplate.class);
        builder.addConstructorArgReference(sqlSessionFactoryBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerTransactionManagerBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String dataSourceBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSourceTransactionManager.class);
        builder.addConstructorArgReference(dataSourceBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerMybatisPlusMappers(BeanDefinitionRegistry registry, 
                                          DataSourceDefinition properties, 
                                          String sqlSessionFactoryBeanName,
                                          ResourceLoader resourceLoader) {
        ClassPathScanningCandidateComponentProvider scanner = new MapperScanner(resourceLoader);

        properties.getPackages().getMapper().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> mapperClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(mapperClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MapperFactoryBean.class);
                builder.addConstructorArgValue(mapperClass);
                builder.addPropertyReference("sqlSessionFactory", sqlSessionFactoryBeanName);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }
    
    private Class<?> classForName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Failed to load class: " + className, e);
        }
    }
    
    private void registerBean(String beanName, BeanDefinitionRegistry registry, 
                            DataSourceDefinition properties, BeanDefinitionBuilder builder) {
        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    /**
     * Mapper扫描器，用于扫描Mapper接口
     */
    private static class MapperScanner extends ClassPathScanningCandidateComponentProvider {
        
        public MapperScanner(ResourceLoader resourceLoader) {
            super(false);
            setResourceLoader(resourceLoader);
            addIncludeFilter(new AnnotationTypeFilter(Mapper.class));
            addIncludeFilter(new AssignableTypeFilter(BaseMapper.class));
        }
        
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
        }
    }
}
