package cn.com.chinastock.cnf.mdatasource.config;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import org.springframework.boot.context.properties.ConfigurationPropertiesBinding;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * DataSourceType配置属性转换器
 * 用于将字符串转换为DataSourceType枚举
 *
 * <AUTHOR>
 */
@Component
@ConfigurationPropertiesBinding
public class DataSourceTypeConverter implements Converter<String, DataSourceType> {

    @Override
    public DataSourceType convert(String source) {
        return DataSourceType.fromValue(source);
    }
}
