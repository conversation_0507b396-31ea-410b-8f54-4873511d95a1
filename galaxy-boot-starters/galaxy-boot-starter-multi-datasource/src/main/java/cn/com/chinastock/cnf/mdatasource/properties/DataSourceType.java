package cn.com.chinastock.cnf.mdatasource.properties;

/**
 * 数据源类型枚举
 * 定义支持的ORM框架类型
 *
 * <AUTHOR>
 */
public enum DataSourceType {
    /**
     * JPA数据源类型
     */
    JPA("jpa"),
    
    /**
     * MyBatis数据源类型
     */
    MYBATIS("mybatis"),

    /**
     * MyBatisPlus数据源类型
     */
    MYBATIS_PLUS("mybatis-plus");
    
    private final String value;
    
    DataSourceType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    /**
     * 根据字符串值获取对应的枚举
     * 
     * @param value 字符串值
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果值不匹配任何枚举
     */
    public static DataSourceType fromValue(String value) {
        if (value == null) {
            return JPA; // 默认值
        }
        
        for (DataSourceType type : values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("Unknown DataSourceType: " + value +
            ". Supported types are: jpa, mybatis, mybatis-plus");
    }
}
