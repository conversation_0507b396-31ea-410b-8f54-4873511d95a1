package cn.com.chinastock.cnf.mdatasource.properties;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * MyBatisPlus配置属性类
 * 用于配置MyBatisPlus相关的属性
 * 与官方 MyBatisPlus 配置保持一致
 *
 * <AUTHOR>
 */
public class MybatisPlusProperties {
    
    /**
     * Mapper XML文件位置
     */
    private List<String> mapperLocations = new ArrayList<>();
    
    /**
     * MyBatis配置文件位置
     */
    private String configLocation;
    
    /**
     * MyBatis配置属性
     */
    private Properties configuration = new Properties();
    
    /**
     * 类型别名包路径
     */
    private String typeAliasesPackage;
    
    /**
     * 类型别名父类型
     */
    private String typeAliasesSuperType;
    
    /**
     * 类型处理器包路径
     */
    private String typeHandlersPackage;
    
    /**
     * 全局配置
     */
    private GlobalConfig globalConfig = new GlobalConfig();
    
    // Getter and Setter methods
    
    public List<String> getMapperLocations() {
        return mapperLocations;
    }
    
    public void setMapperLocations(List<String> mapperLocations) {
        this.mapperLocations = mapperLocations;
    }
    
    public String getConfigLocation() {
        return configLocation;
    }
    
    public void setConfigLocation(String configLocation) {
        this.configLocation = configLocation;
    }
    
    public Properties getConfiguration() {
        return configuration;
    }
    
    public void setConfiguration(Properties configuration) {
        this.configuration = configuration;
    }
    
    public String getTypeAliasesPackage() {
        return typeAliasesPackage;
    }
    
    public void setTypeAliasesPackage(String typeAliasesPackage) {
        this.typeAliasesPackage = typeAliasesPackage;
    }
    
    public String getTypeAliasesSuperType() {
        return typeAliasesSuperType;
    }
    
    public void setTypeAliasesSuperType(String typeAliasesSuperType) {
        this.typeAliasesSuperType = typeAliasesSuperType;
    }
    
    public String getTypeHandlersPackage() {
        return typeHandlersPackage;
    }
    
    public void setTypeHandlersPackage(String typeHandlersPackage) {
        this.typeHandlersPackage = typeHandlersPackage;
    }
    
    public GlobalConfig getGlobalConfig() {
        return globalConfig;
    }
    
    public void setGlobalConfig(GlobalConfig globalConfig) {
        this.globalConfig = globalConfig;
    }
    
    /**
     * MyBatisPlus全局配置
     */
    public static class GlobalConfig {
        
        /**
         * 是否启用SQL运行器
         */
        private Boolean enableSqlRunner = false;
        
        /**
         * 是否显示Banner
         */
        private Boolean banner = true;
        
        /**
         * 数据库相关配置
         */
        private DbConfig dbConfig = new DbConfig();
        
        public Boolean getEnableSqlRunner() {
            return enableSqlRunner;
        }
        
        public void setEnableSqlRunner(Boolean enableSqlRunner) {
            this.enableSqlRunner = enableSqlRunner;
        }
        
        public Boolean getBanner() {
            return banner;
        }
        
        public void setBanner(Boolean banner) {
            this.banner = banner;
        }
        
        public DbConfig getDbConfig() {
            return dbConfig;
        }
        
        public void setDbConfig(DbConfig dbConfig) {
            this.dbConfig = dbConfig;
        }
    }
    
    /**
     * 数据库配置
     */
    public static class DbConfig {
        
        /**
         * 主键类型
         */
        private String idType = "ASSIGN_ID";
        
        /**
         * 表名是否使用下划线
         */
        private Boolean tableUnderline = true;
        
        /**
         * 主键生成器
         */
        private String keyGenerator;
        
        /**
         * 表名前缀
         */
        private String tablePrefix;
        
        /**
         * 数据库schema
         */
        private String schema;
        
        /**
         * 列名格式
         */
        private String columnFormat;
        
        /**
         * 属性名格式
         */
        private String propertyFormat;
        
        /**
         * 表名格式
         */
        private String tableFormat;
        
        /**
         * 数据库类型
         */
        private String dbType;
        
        /**
         * 是否启用删除
         */
        private Boolean isDelete = true;
        
        /**
         * 字段策略
         */
        private String fieldStrategy = "NOT_EMPTY";
        
        /**
         * 插入策略
         */
        private String insertStrategy = "NOT_EMPTY";
        
        /**
         * 更新策略
         */
        private String updateStrategy = "NOT_EMPTY";
        
        /**
         * 查询策略
         */
        private String selectStrategy = "NOT_EMPTY";
        
        /**
         * WHERE策略
         */
        private String whereStrategy = "NOT_EMPTY";
        
        /**
         * 逻辑删除字段名
         */
        private String logicDeleteField;
        
        /**
         * 逻辑删除全局值（已删除值）
         */
        private String logicDeleteValue = "1";
        
        /**
         * 逻辑未删除全局值（未删除值）
         */
        private String logicNotDeleteValue = "0";
        
        // Getter and Setter methods
        
        public String getIdType() {
            return idType;
        }
        
        public void setIdType(String idType) {
            this.idType = idType;
        }
        
        public Boolean getTableUnderline() {
            return tableUnderline;
        }
        
        public void setTableUnderline(Boolean tableUnderline) {
            this.tableUnderline = tableUnderline;
        }
        
        public String getKeyGenerator() {
            return keyGenerator;
        }
        
        public void setKeyGenerator(String keyGenerator) {
            this.keyGenerator = keyGenerator;
        }
        
        public String getTablePrefix() {
            return tablePrefix;
        }
        
        public void setTablePrefix(String tablePrefix) {
            this.tablePrefix = tablePrefix;
        }
        
        public String getSchema() {
            return schema;
        }
        
        public void setSchema(String schema) {
            this.schema = schema;
        }
        
        public String getColumnFormat() {
            return columnFormat;
        }
        
        public void setColumnFormat(String columnFormat) {
            this.columnFormat = columnFormat;
        }
        
        public String getPropertyFormat() {
            return propertyFormat;
        }
        
        public void setPropertyFormat(String propertyFormat) {
            this.propertyFormat = propertyFormat;
        }
        
        public String getTableFormat() {
            return tableFormat;
        }
        
        public void setTableFormat(String tableFormat) {
            this.tableFormat = tableFormat;
        }
        
        public String getDbType() {
            return dbType;
        }
        
        public void setDbType(String dbType) {
            this.dbType = dbType;
        }
        
        public Boolean getIsDelete() {
            return isDelete;
        }
        
        public void setIsDelete(Boolean isDelete) {
            this.isDelete = isDelete;
        }
        
        public String getFieldStrategy() {
            return fieldStrategy;
        }
        
        public void setFieldStrategy(String fieldStrategy) {
            this.fieldStrategy = fieldStrategy;
        }
        
        public String getInsertStrategy() {
            return insertStrategy;
        }
        
        public void setInsertStrategy(String insertStrategy) {
            this.insertStrategy = insertStrategy;
        }
        
        public String getUpdateStrategy() {
            return updateStrategy;
        }
        
        public void setUpdateStrategy(String updateStrategy) {
            this.updateStrategy = updateStrategy;
        }
        
        public String getSelectStrategy() {
            return selectStrategy;
        }
        
        public void setSelectStrategy(String selectStrategy) {
            this.selectStrategy = selectStrategy;
        }
        
        public String getWhereStrategy() {
            return whereStrategy;
        }
        
        public void setWhereStrategy(String whereStrategy) {
            this.whereStrategy = whereStrategy;
        }
        
        public String getLogicDeleteField() {
            return logicDeleteField;
        }
        
        public void setLogicDeleteField(String logicDeleteField) {
            this.logicDeleteField = logicDeleteField;
        }
        
        public String getLogicDeleteValue() {
            return logicDeleteValue;
        }
        
        public void setLogicDeleteValue(String logicDeleteValue) {
            this.logicDeleteValue = logicDeleteValue;
        }
        
        public String getLogicNotDeleteValue() {
            return logicNotDeleteValue;
        }
        
        public void setLogicNotDeleteValue(String logicNotDeleteValue) {
            this.logicNotDeleteValue = logicNotDeleteValue;
        }
    }
}
