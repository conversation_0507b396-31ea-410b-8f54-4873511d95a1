package cn.com.chinastock.cnf.mdatasource.properties;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * MyBatis配置属性类
 * 用于配置MyBatis相关的属性
 * 只包含实际使用的配置项
 *
 * <AUTHOR>
 */
public class MybatisProperties {
    
    /**
     * Mapper XML文件位置
     */
    private List<String> mapperLocations = new ArrayList<>();
    
    /**
     * MyBatis配置文件位置
     */
    private String configLocation;
    
    /**
     * MyBatis配置属性
     */
    private Properties configuration = new Properties();
    
    /**
     * 获取Mapper XML文件位置列表
     * 
     * @return Mapper XML文件位置列表
     */
    public List<String> getMapperLocations() {
        return mapperLocations;
    }
    
    /**
     * 设置Mapper XML文件位置列表
     * 
     * @param mapperLocations Mapper XML文件位置列表
     */
    public void setMapperLocations(List<String> mapperLocations) {
        this.mapperLocations = mapperLocations;
    }
    
    /**
     * 获取MyBatis配置文件位置
     * 
     * @return MyBatis配置文件位置
     */
    public String getConfigLocation() {
        return configLocation;
    }
    
    /**
     * 设置MyBatis配置文件位置
     * 
     * @param configLocation MyBatis配置文件位置
     */
    public void setConfigLocation(String configLocation) {
        this.configLocation = configLocation;
    }
    
    /**
     * 获取MyBatis配置属性
     * 
     * @return MyBatis配置属性
     */
    public Properties getConfiguration() {
        return configuration;
    }
    
    /**
     * 设置MyBatis配置属性
     * 
     * @param configuration MyBatis配置属性
     */
    public void setConfiguration(Properties configuration) {
        this.configuration = configuration;
    }
}
