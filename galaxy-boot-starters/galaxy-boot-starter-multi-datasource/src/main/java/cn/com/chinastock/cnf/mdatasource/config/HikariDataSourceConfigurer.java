package cn.com.chinastock.cnf.mdatasource.config;

import cn.com.chinastock.cnf.mdatasource.properties.HikariProperties;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * Hikari 数据源配置器
 * 负责根据配置属性创建和配置 HikariDataSource
 *
 * <AUTHOR>
 */
public class HikariDataSourceConfigurer {

    /**
     * 创建 Hikari 数据源
     *
     * @param dataSourceProperties 数据源基础属性
     * @param hikariProperties Hikari连接池属性
     * @param dataSourceName 数据源名称
     * @return 配置好的 HikariDataSource
     */
    public static DataSource createHikariDataSource(DataSourceProperties dataSourceProperties,
                                                   HikariProperties hikariProperties,
                                                   String dataSourceName) {
        HikariConfig config = new HikariConfig();

        // 基础数据源配置
        configureBasicProperties(config, dataSourceProperties);

        // Hikari 连接池配置
        configureHikariProperties(config, hikariProperties, dataSourceName);

        return new HikariDataSource(config);
    }

    /**
     * 配置基础数据源属性
     *
     * @param config Hikari配置对象
     * @param properties 数据源属性
     */
    private static void configureBasicProperties(HikariConfig config, DataSourceProperties properties) {
        if (StringUtils.hasText(properties.getUrl())) {
            config.setJdbcUrl(properties.getUrl());
        }
        
        if (StringUtils.hasText(properties.getUsername())) {
            config.setUsername(properties.getUsername());
        }
        
        if (StringUtils.hasText(properties.getPassword())) {
            config.setPassword(properties.getPassword());
        }
        
        if (StringUtils.hasText(properties.getDriverClassName())) {
            config.setDriverClassName(properties.getDriverClassName());
        }
    }

    /**
     * 配置 Hikari 连接池属性
     *
     * @param config Hikari配置对象
     * @param hikariProperties Hikari属性配置
     * @param dataSourceName 数据源名称
     */
    private static void configureHikariProperties(HikariConfig config,
                                                HikariProperties hikariProperties,
                                                String dataSourceName) {
        // 连接池名称 - 没有默认值，需要检查
        if (StringUtils.hasText(hikariProperties.getPoolName())) {
            config.setPoolName(hikariProperties.getPoolName());
        } else {
            config.setPoolName(dataSourceName + "-HikariPool");
        }

        // 连接池大小配置 - 有默认值，直接使用
        config.setMaximumPoolSize(hikariProperties.getMaximumPoolSize());
        config.setMinimumIdle(hikariProperties.getMinimumIdle());

        // 超时配置 - 有默认值，直接使用
        config.setConnectionTimeout(hikariProperties.getConnectionTimeout().toMillis());
        config.setIdleTimeout(hikariProperties.getIdleTimeout().toMillis());
        config.setMaxLifetime(hikariProperties.getMaxLifetime().toMillis());
        config.setValidationTimeout(hikariProperties.getValidationTimeout().toMillis());
        
        // 泄漏检测阈值 - 有默认值，但需要特殊处理（0表示禁用）
        if (!hikariProperties.getLeakDetectionThreshold().isZero()) {
            config.setLeakDetectionThreshold(hikariProperties.getLeakDetectionThreshold().toMillis());
        }

        // 连接属性配置 - 有默认值，直接使用
        config.setAutoCommit(hikariProperties.getAutoCommit());
        config.setReadOnly(hikariProperties.getReadOnly());
        
        // 以下属性没有默认值，需要检查
        if (StringUtils.hasText(hikariProperties.getTransactionIsolation())) {
            config.setTransactionIsolation(hikariProperties.getTransactionIsolation());
        }
        
        if (StringUtils.hasText(hikariProperties.getCatalog())) {
            config.setCatalog(hikariProperties.getCatalog());
        }
        
        if (StringUtils.hasText(hikariProperties.getSchema())) {
            config.setSchema(hikariProperties.getSchema());
        }

        // 连接测试和初始化 - 没有默认值，需要检查
        if (StringUtils.hasText(hikariProperties.getConnectionTestQuery())) {
            config.setConnectionTestQuery(hikariProperties.getConnectionTestQuery());
        }
        
        if (StringUtils.hasText(hikariProperties.getConnectionInitSql())) {
            config.setConnectionInitSql(hikariProperties.getConnectionInitSql());
        }

        // 数据源属性 - 有默认值，但需要检查是否为空
        if (!hikariProperties.getDataSourceProperties().isEmpty()) {
            Properties dsProps = new Properties();
            dsProps.putAll(hikariProperties.getDataSourceProperties());
            config.setDataSourceProperties(dsProps);
        }

        // 其他配置 - 有默认值，直接使用
        config.setAllowPoolSuspension(hikariProperties.getAllowPoolSuspension());

        // 健康检查配置
        configureHealthCheck(config, hikariProperties);

        // 监控配置
        configureMonitoring(config, hikariProperties);
    }

    /**
     * 配置健康检查
     * 
     * 注意：HikariCP 默认启用健康检查，无需额外配置。
     * 健康检查会自动验证连接的有效性，并在需要时创建新连接。
     * 
     * 如果需要自定义健康检查行为，可以通过以下方式：
     * 1. 设置 connectionTestQuery 来指定验证SQL
     * 2. 调整 validationTimeout 来设置验证超时时间
     * 3. 配置 maxLifetime 来控制连接的最大生命周期
     *
     * @param config Hikari配置对象
     * @param hikariProperties Hikari属性配置
     */
    private static void configureHealthCheck(HikariConfig config, HikariProperties hikariProperties) {
        // HikariCP 默认启用健康检查，无需额外配置
        // 健康检查相关的配置已经在 configureHikariProperties 方法中处理：
        // - connectionTestQuery: 连接测试SQL
        // - validationTimeout: 验证超时时间
        // - maxLifetime: 连接最大生命周期
        
        // 如果需要禁用健康检查（不推荐），可以通过设置很长的 maxLifetime 来实现
        // 但通常不建议禁用，因为健康检查有助于提高系统稳定性
    }

    /**
     * 配置监控
     * 
     * 注意：HikariCP 的监控配置通常通过以下方式实现：
     * 1. 使用 Micrometer 集成（推荐）
     * 2. 使用 JMX 监控
     * 3. 通过 getPoolStats 方法获取统计信息
     * 
     * 当前实现中，监控配置由 galaxy-boot-starter-metrics 模块处理，
     * 该模块会自动配置 MetricRegistry 和相关的监控指标。
     *
     * @param config Hikari配置对象
     * @param hikariProperties Hikari属性配置
     */
    private static void configureMonitoring(HikariConfig config, HikariProperties hikariProperties) {
        // 监控配置由 galaxy-boot-starter-metrics 模块自动处理
        // 该模块会：
        // 1. 自动注册 MetricRegistry
        // 2. 配置 HikariCP 的监控指标
        // 3. 支持日志和 Prometheus 两种监控方式
        
        // 如果需要手动配置监控，可以参考 galaxy-boot-starter-metrics 模块的实现
    }

    /**
     * 获取连接池统计信息
     *
     * @param dataSource 数据源
     * @return 连接池统计信息，如果不是Hikari数据源则返回null
     */
    public static PoolStats getPoolStats(DataSource dataSource) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            return new PoolStats(
                hikariDataSource.getHikariPoolMXBean().getTotalConnections(),
                hikariDataSource.getHikariPoolMXBean().getActiveConnections(),
                hikariDataSource.getHikariPoolMXBean().getIdleConnections(),
                hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection()
            );
        }
        return null;
    }

    /**
     * 连接池统计信息
     */
    public static class PoolStats {
        private final int totalConnections;
        private final int activeConnections;
        private final int idleConnections;
        private final int threadsAwaitingConnection;

        public PoolStats(int totalConnections, int activeConnections, 
                        int idleConnections, int threadsAwaitingConnection) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.idleConnections = idleConnections;
            this.threadsAwaitingConnection = threadsAwaitingConnection;
        }

        public int getTotalConnections() {
            return totalConnections;
        }

        public int getActiveConnections() {
            return activeConnections;
        }

        public int getIdleConnections() {
            return idleConnections;
        }

        public int getThreadsAwaitingConnection() {
            return threadsAwaitingConnection;
        }

        @Override
        public String toString() {
            return String.format("PoolStats{total=%d, active=%d, idle=%d, waiting=%d}", 
                totalConnections, activeConnections, idleConnections, threadsAwaitingConnection);
        }
    }
}
