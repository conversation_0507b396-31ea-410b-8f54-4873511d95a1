
### 组件功能介绍

#### 多ORM框架支持
- **JPA支持**：基于Spring Data JPA，支持实体类和Repository接口，提供丰富的查询方法和自动SQL生成
- **MyBatis支持**：基于MyBatis Spring Boot Starter，支持Mapper接口和XML映射文件，提供灵活的SQL控制
- **MyBatisPlus支持**：基于MyBatisPlus Spring Boot Starter，支持增强的Mapper接口、代码生成和丰富的CRUD功能
- **混合使用**：可以在同一个应用中同时使用JPA、MyBatis和MyBatisPlus数据源，根据业务需求选择最适合的ORM框架

#### 多数据源配置
支持通过配置文件声明式地配置多个数据源，每个数据源都是完全独立的，包括：

- 独立的数据库连接配置
- 独立的ORM框架配置（JPA、MyBatis或MyBatisPlus）
- 独立的实体、Repository/Mapper包路径
- 独立的事务管理器

#### 自动化 Bean 注册
组件会根据数据源类型自动为每个数据源创建相应的 Bean：

**JPA数据源Bean：**

- `DataSource` - 数据源
- `EntityManagerFactory` - JPA 实体管理器工厂
- `JpaTransactionManager` - JPA 事务管理器
- `SharedEntityManager` - 共享实体管理器
- `JpaRepositoryFactoryBean` - Repository 工厂 Bean

**MyBatis数据源Bean：**

- `DataSource` - 数据源
- `SqlSessionFactory` - MyBatis SQL会话工厂
- `DataSourceTransactionManager` - 数据源事务管理器
- `SqlSessionTemplate` - SQL会话模板
- `MapperFactoryBean` - Mapper 工厂 Bean

**MyBatisPlus数据源Bean：**

- `DataSource` - 数据源
- `SqlSessionFactory` - MyBatis SQL会话工厂
- `SqlSessionTemplate` - MyBatis SQL会话模板
- `DataSourceTransactionManager` - 数据源事务管理器
- `MapperFactoryBean` - Mapper 工厂 Bean

#### 事务管理
每个数据源都有独立的事务管理器，支持：

- 独立的事务边界
- 事务回滚隔离
- 声明式事务管理
- JPA和MyBatis事务的独立管理

### 依赖配置

#### 基础依赖
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-multi-datasource</artifactId>
</dependency>
```

#### JPA支持
如果需要使用JPA数据源，添加以下依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
```

#### MyBatis支持
如果需要使用MyBatis数据源，添加以下依赖：

```xml
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
</dependency>
```

#### MyBatisPlus支持
如果需要使用MyBatisPlus数据源，添加以下依赖：

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    <version>3.5.9</version>
</dependency>
```

#### 混合使用
如果需要在同一个应用中同时使用JPA、MyBatis和MyBatisPlus，请同时引入相应的依赖。

### 使用方式

#### 1. 启用多数据源
在启动类上添加 `@EnableMultiDatasource` 注解：

```java
@SpringBootApplication
@EnableMultiDatasource
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

#### 2. 配置多数据源
在 `application.yml` 中配置多个数据源。配置路径为 `spring.galaxy-datasource`：

##### JPA数据源配置示例
```yaml
spring:
  galaxy-datasource:
    # 用户数据源配置（JPA）
    user:
      primary: true  # 标记为主数据源
      type: jpa      # 指定使用JPA
      # 包路径配置
      packages:
        entity: com.example.user.entity      # 实体类包路径
        repository: com.example.user.repository  # Repository 包路径
      # 标准的 Spring Boot DataSource 配置
      datasource:
        url: ***********************************
        username: root
        password: password
        driver-class-name: com.mysql.cj.jdbc.Driver
      # 标准的 Spring Boot JPA 配置
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: none
          hibernate.dialect: org.hibernate.dialect.MySQL8Dialect
```

##### MyBatis数据源配置示例
```yaml
spring:
  galaxy-datasource:
    # 产品数据源配置（MyBatis）
    product:
      type: mybatis  # 指定使用MyBatis
      packages:
        entity: com.example.product.entity    # 实体类包路径（可选）
        mapper: com.example.product.mapper    # Mapper接口包路径
      datasource:
        url: **************************************
        username: root
        password: password
        driver-class-name: com.mysql.cj.jdbc.Driver
      mybatis:
        mapper-locations: classpath:mapper/product/*.xml  # Mapper XML文件位置
        configuration:
          map-underscore-to-camel-case: true  # 下划线转驼峰命名
```

##### MyBatisPlus数据源配置示例
```yaml
spring:
  galaxy-datasource:
    # 订单数据源配置（MyBatisPlus）
    order:
      type: mybatis-plus  # 指定使用MyBatisPlus
      packages:
        entity: com.example.order.entity      # 实体类包路径
        mapper: com.example.order.mapper      # Mapper接口包路径
      datasource:
        url: ************************************
        username: root
        password: password
        driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
      mybatis-plus:
        mapper-locations: classpath:mapper/order/*.xml  # Mapper XML文件位置（可选）
        type-aliases-package: com.example.order.entity  # 类型别名包路径
        configuration:
          map-underscore-to-camel-case: true  # 下划线转驼峰命名
          log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出
        global-config:
          enable-sql-runner: false  # 是否启用SQL运行器
          banner: true  # 是否显示Banner
          db-config:
            id-type: ASSIGN_ID          # 主键类型：雪花算法
            table-prefix: t_            # 表名前缀
            logic-delete-field: deleted # 逻辑删除字段名
            logic-delete-value: 1       # 逻辑删除值
            logic-not-delete-value: 0   # 逻辑未删除值
            field-strategy: NOT_EMPTY   # 字段策略
```

##### 混合配置示例
```yaml
spring:
  galaxy-datasource:
    # JPA数据源
    user:
      primary: true
      type: jpa
      packages:
        entity: com.example.user.entity
        repository: com.example.user.repository
      datasource:
        url: ***********************************
        username: root
        password: password
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: none

    # MyBatis数据源
    product:
      type: mybatis
      packages:
        entity: com.example.product.entity
        mapper: com.example.product.mapper
      datasource:
        url: **************************************
        username: root
        password: password
      mybatis:
        mapper-locations: classpath:mapper/product/*.xml
        configuration:
          map-underscore-to-camel-case: true

    # MyBatisPlus数据源
    order:
      type: mybatis-plus
      packages:
        entity: com.example.order.entity
        mapper: com.example.order.mapper
      datasource:
        url: ************************************
        username: root
        password: password
      mybatis-plus:
        type-aliases-package: com.example.order.entity
        global-config:
          db-config:
            id-type: ASSIGN_ID
            table-prefix: t_
            logic-delete-field: deleted
```

#### 3. 定义实体类
根据使用的ORM框架，在指定的包路径下创建实体类：

##### JPA实体类
```java
// 用户实体 - 位于 com.example.user.entity 包
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String username;
    private String email;
    private Integer age;

    // 构造函数、getter 和 setter
}
```

##### MyBatis实体类
```java
// 产品实体 - 位于 com.example.product.entity 包
public class Product {
    private Long id;
    private String name;
    private BigDecimal price;
    private String description;

    // 构造函数、getter 和 setter
}
```

##### MyBatisPlus实体类
```java
// 订单实体 - 位于 com.example.order.entity 包
@TableName("t_order")
public class Order {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("order_no")
    private String orderNo;
    
    private BigDecimal amount;
    private String status;

    @TableLogic
    private Integer deleted;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 构造函数、getter 和 setter
}

// 用户实体 - 使用MyBatisPlus注解
@TableName("t_user")
public class User {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("user_name")
    private String userName;
    
    private String email;
    private Integer age;

    @TableLogic
    private Integer deleted;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 构造函数、getter 和 setter
}
```

#### 4. 定义 Repository/Mapper 接口
根据使用的ORM框架，在指定的包路径下创建相应的接口：

##### JPA Repository接口
```java
// 用户 Repository - 位于 com.example.user.repository 包
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
}
```

##### MyBatis Mapper接口
```java
// 产品 Mapper - 位于 com.example.product.mapper 包
@Mapper
public interface ProductMapper {
    Optional<Product> findById(@Param("id") Long id);
    Optional<Product> findByName(@Param("name") String name);
    List<Product> findAll();
    int save(Product product);
    int update(Product product);
    int deleteById(@Param("id") Long id);
    long count();
}
```

##### MyBatisPlus Mapper接口
```java
// 订单 Mapper - 位于 com.example.order.mapper 包
@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    // 继承BaseMapper后，自动拥有基础的CRUD方法：
    // insert(T entity) - 插入记录
    // deleteById(Serializable id) - 根据ID删除（逻辑删除）
    // updateById(T entity) - 根据ID更新
    // selectById(Serializable id) - 根据ID查询
    // selectList(Wrapper<T> queryWrapper) - 条件查询
    // selectCount(Wrapper<T> queryWrapper) - 统计查询

    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM t_order WHERE order_no = #{orderNo} AND deleted = 0")
    Order findByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询指定状态的订单列表
     */
    List<Order> findByStatus(@Param("status") String status);

    /**
     * 分页查询订单
     */
    IPage<Order> selectOrderPage(IPage<Order> page, @Param("status") String status);
}

// 用户 Mapper - 继承BaseMapper获得基础CRUD功能
@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM t_user WHERE user_name = #{userName} AND deleted = 0")
    User findByUserName(@Param("userName") String userName);

    /**
     * 根据年龄范围查询用户
     */
    List<User> findByAgeRange(@Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge);
}
```

##### MyBatis XML映射文件
在 `src/main/resources/mapper/product/` 目录下创建 `ProductMapper.xml`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.product.mapper.ProductMapper">

    <resultMap id="ProductResultMap" type="com.example.product.entity.Product">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="price" property="price"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="findById" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM product
        WHERE id = #{id}
    </select>

    <select id="findByName" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM product
        WHERE name = #{name}
    </select>

    <select id="findAll" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM product
        ORDER BY id
    </select>

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product (name, price, description)
        VALUES (#{name}, #{price}, #{description})
    </insert>

    <update id="update">
        UPDATE product
        SET name = #{name}, price = #{price}, description = #{description}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM product WHERE id = #{id}
    </delete>

    <select id="count" resultType="long">
        SELECT COUNT(*) FROM product
    </select>

</mapper>
```

#### 5. 使用事务管理
通过指定事务管理器名称来使用特定数据源的事务：

```java
@Service
public class BusinessService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private OrderMapper orderMapper;

    // 使用JPA数据源的事务管理器
    @Transactional("userTransactionManager")
    public void saveUser(User user) {
        userRepository.save(user);
        // 如果发生异常，只回滚用户数据源的操作
    }

    // 使用MyBatis数据源的事务管理器
    @Transactional("productTransactionManager")
    public void saveProduct(Product product) {
        productMapper.save(product);
        // 如果发生异常，只回滚产品数据源的操作
    }

    // 使用MyBatisPlus数据源的事务管理器
    @Transactional("orderTransactionManager")
    public void saveOrder(Order order) {
        orderMapper.insert(order);
        // 如果发生异常，只回滚订单数据源的操作
    }

    // 不使用事务注解时，会使用主数据源的事务管理器
    public void saveUserWithDefaultTransaction(User user) {
        userRepository.save(user);
    }

    // 混合使用示例：在同一个方法中操作不同数据源
    public void businessOperation() {
        // 注意：这里没有事务注解，每个操作使用各自的事务
        User user = new User("john", "<EMAIL>", 25);
        userRepository.save(user);  // 使用JPA

        Product product = new Product("Laptop", new BigDecimal("999.99"), "Gaming laptop");
        productMapper.save(product);  // 使用MyBatis

        Order order = new Order("ORD001", new BigDecimal("999.99"), "PENDING");
        orderMapper.insert(order);  // 使用MyBatisPlus
    }
}
```

### 配置说明

#### 数据源配置结构
每个数据源的配置包含以下部分：

##### 通用配置结构
```yaml
spring:
  galaxy-datasource:
    {datasource-name}:  # 数据源名称，用于生成 Bean 名称
      primary: true|false  # 是否为主数据源，最多只能有一个
      type: jpa|mybatis   # 数据源类型，默认为 jpa
      packages:
        entity: [实体类包路径列表]      # 实体类包路径
        repository: [Repository包路径列表]  # JPA Repository包路径
        mapper: [Mapper包路径列表]     # MyBatis Mapper包路径
      datasource:
        # 标准的 Spring Boot DataSource 配置
        url: 数据库连接URL
        username: 用户名
        password: 密码
        driver-class-name: 驱动类名
```

##### JPA特有配置
```yaml
      jpa:
        # 标准的 Spring Boot JPA 配置（可选）
        show-sql: true|false
        properties:
          hibernate.hbm2ddl.auto: create|update|validate|none
          hibernate.dialect: 数据库方言
```

##### MyBatis特有配置
```yaml
      mybatis:
        # MyBatis配置
        mapper-locations: [Mapper XML文件位置列表]
        config-location: MyBatis配置文件位置（可选）
        type-aliases-package: com.example.entity  # 类型别名包路径
        type-aliases-super-type: java.lang.Object  # 类型别名父类
        type-handlers-package: com.example.handler  # 类型处理器包路径
        configuration:
          # MyBatis配置属性
          map-underscore-to-camel-case: true|false  # 下划线转驼峰命名
          cache-enabled: true|false  # 启用缓存
          lazy-loading-enabled: true|false  # 启用延迟加载
          aggressive-lazy-loading: true|false  # 激进延迟加载
          multiple-result-sets-enabled: true|false  # 多结果集支持
          use-column-label: true|false  # 使用列标签
          use-generated-keys: true|false  # 使用生成的主键
          auto-mapping-behavior: PARTIAL|FULL|NONE  # 自动映射行为
          auto-mapping-unknown-column-behavior: WARNING|FAILING|NONE  # 未知列行为
          default-executor-type: SIMPLE|REUSE|BATCH  # 默认执行器类型
          default-statement-timeout: 30  # 默认语句超时时间
          default-fetch-size: 100  # 默认获取大小
          safe-row-bounds-enabled: true|false  # 安全行边界
          safe-result-handler-enabled: true|false  # 安全结果处理器
          map-underscore-to-camel-case: true|false  # 下划线转驼峰
          local-cache-scope: SESSION|STATEMENT  # 本地缓存作用域
          jdbc-type-for-null: OTHER|VARCHAR|CHAR  # NULL的JDBC类型
          lazy-load-trigger-methods: equals,clone,hashCode,toString  # 延迟加载触发方法
          call-setters-on-nulls: true|false  # NULL时调用setter
          return-instance-for-empty-row: true|false  # 空行返回实例
          log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 日志实现
```

##### MyBatisPlus特有配置
```yaml
      mybatis-plus:
        # MyBatisPlus配置
        mapper-locations: [Mapper XML文件位置列表]  # Mapper XML文件位置（可选）
        type-aliases-package: com.example.entity  # 类型别名包路径
        type-aliases-super-type: java.lang.Object  # 类型别名父类
        type-handlers-package: com.example.handler  # 类型处理器包路径
        configuration:
          # MyBatis配置属性
          map-underscore-to-camel-case: true|false  # 下划线转驼峰命名
          cache-enabled: true|false  # 启用缓存
          lazy-loading-enabled: true|false  # 启用延迟加载
          aggressive-lazy-loading: true|false  # 激进延迟加载
          multiple-result-sets-enabled: true|false  # 多结果集支持
          use-column-label: true|false  # 使用列标签
          use-generated-keys: true|false  # 使用生成的主键
          auto-mapping-behavior: PARTIAL|FULL|NONE  # 自动映射行为
          auto-mapping-unknown-column-behavior: WARNING|FAILING|NONE  # 未知列行为
          default-executor-type: SIMPLE|REUSE|BATCH  # 默认执行器类型
          default-statement-timeout: 30  # 默认语句超时时间
          default-fetch-size: 100  # 默认获取大小
          safe-row-bounds-enabled: true|false  # 安全行边界
          safe-result-handler-enabled: true|false  # 安全结果处理器
          local-cache-scope: SESSION|STATEMENT  # 本地缓存作用域
          jdbc-type-for-null: OTHER|VARCHAR|CHAR  # NULL的JDBC类型
          lazy-load-trigger-methods: equals,clone,hashCode,toString  # 延迟加载触发方法
          call-setters-on-nulls: true|false  # NULL时调用setter
          return-instance-for-empty-row: true|false  # 空行返回实例
          log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 日志实现
        global-config:
          # 全局配置
          enable-sql-runner: false  # 是否启用SQL运行器
          banner: true  # 是否显示Banner
          db-config:
            id-type: ASSIGN_ID|AUTO|INPUT|ASSIGN_UUID  # 主键类型
            table-prefix: t_  # 表名前缀
            table-underline: true|false  # 表名是否使用下划线命名
            key-generator: com.baomidou.mybatisplus.extension.incrementer.H2KeyGenerator  # 主键生成器
            schema:  # 数据库schema
            column-format:  # 列格式
            property-format:  # 属性格式
            table-format:  # 表格式
            db-type: mysql|oracle|postgre|sqlserver|db2|h2|hsql|sqlite|dm|kingbasees|mariadb|clickhouse|gauss|sybase|oceanbase|firebird|hana|kingbase|db2|dm|gbase|oscar|tidb|uxdb|openGauss|informix|greenplum|goldilocks|cosmos|polardb|highgo|vastbase  # 数据库类型
            is-delete: true|false  # 是否启用逻辑删除
            field-strategy: IGNORED|NOT_NULL|NOT_EMPTY|DEFAULT  # 字段策略
            insert-strategy: IGNORED|NOT_NULL|NOT_EMPTY|DEFAULT  # 插入策略
            update-strategy: IGNORED|NOT_NULL|NOT_EMPTY|DEFAULT  # 更新策略
            select-strategy: IGNORED|NOT_NULL|NOT_EMPTY|DEFAULT  # 查询策略
            where-strategy: IGNORED|NOT_NULL|NOT_EMPTY|DEFAULT  # WHERE策略
            logic-delete-field: deleted  # 逻辑删除字段名
            logic-delete-value: 1  # 逻辑删除值
            logic-not-delete-value: 0  # 逻辑未删除值
```

#### Bean 命名规则
组件会根据数据源名称和类型自动生成相应的 Bean：

##### JPA数据源Bean命名
- `{datasource-name}DataSource` - 数据源 Bean
- `{datasource-name}EntityManagerFactory` - 实体管理器工厂 Bean
- `{datasource-name}TransactionManager` - 事务管理器 Bean
- `{datasource-name}SharedEntityManager` - 共享实体管理器 Bean

##### MyBatis数据源Bean命名
- `{datasource-name}DataSource` - 数据源 Bean
- `{datasource-name}SqlSessionFactory` - SQL会话工厂 Bean
- `{datasource-name}SqlSessionTemplate` - SQL会话模板 Bean
- `{datasource-name}TransactionManager` - 事务管理器 Bean

##### MyBatisPlus数据源Bean命名
- `{datasource-name}DataSource` - 数据源 Bean
- `{datasource-name}SqlSessionFactory` - SQL会话工厂 Bean
- `{datasource-name}SqlSessionTemplate` - SQL会话模板 Bean
- `{datasource-name}TransactionManager` - 事务管理器 Bean

##### 示例
数据源名称为 `user`（JPA）时，会生成：

- `userDataSource`
- `userEntityManagerFactory`
- `userTransactionManager`
- `userSharedEntityManager`

数据源名称为 `product`（MyBatis）时，会生成：

- `productDataSource`
- `productSqlSessionFactory`
- `productSqlSessionTemplate`
- `productTransactionManager`

数据源名称为 `order`（MyBatisPlus）时，会生成：

- `orderDataSource`
- `orderSqlSessionFactory`
- `orderSqlSessionTemplate`
- `orderTransactionManager`

#### 包路径配置
每个数据源需要指定独立的包路径，根据ORM框架类型配置相应的包路径：

##### JPA包路径配置
```yaml
packages:
  entity:
    - com.example.user.entity
    - com.example.user.dto.entity  # 支持多个包路径
  repository:
    - com.example.user.repository
    - com.example.user.dao
```

##### MyBatis包路径配置
```yaml
packages:
  entity:  # 可选，用于存放POJO类
    - com.example.product.entity
  mapper:  # 必需，Mapper接口包路径
    - com.example.product.mapper
    - com.example.product.dao
```

##### MyBatisPlus包路径配置
```yaml
packages:
  entity:  # 实体类包路径
    - com.example.order.entity
  mapper:  # Mapper接口包路径
    - com.example.order.mapper
```

##### 混合配置包路径
```yaml
# JPA数据源
user:
  type: jpa
  packages:
    entity: com.example.user.entity
    repository: com.example.user.repository

# MyBatis数据源
product:
  type: mybatis
  packages:
    entity: com.example.product.entity
    mapper: com.example.product.mapper

# MyBatisPlus数据源
order:
  type: mybatis-plus
  packages:
    entity: com.example.order.entity
    mapper: com.example.order.mapper
```
