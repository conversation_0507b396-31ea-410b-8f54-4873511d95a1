## Galaxy Boot Starter Multi Datasource

> `Galaxy Boot Starter Multi Datasource` 是一个多数据源管理组件(关系数据库)，支持 `Spring Data JPA`、`MyBatis` 和 `MyBatisPlus` 三种ORM框架，可以在单个应用中配置和管理多个数据源。每个数据源都有独立的配置和事务管理，支持混合使用不同的ORM框架。

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter Multi Datasource`，只需要引入对应 `starter` 即可：使用 group ID 为 `cn.com.chinastock` 和 `artifact ID` 为
`galaxy-boot-starter-multi-datasource` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-multi-datasource</artifactId>
</dependency>
```
