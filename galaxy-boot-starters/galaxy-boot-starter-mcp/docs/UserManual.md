### 组件介绍

- **Spring AI @Tool 注解**: 使用 Spring AI 原生的 `@Tool` 注解，自动扫描和注册工具
- **自动工具注册**: 自动扫描 Controller 中的 `@Tool` 注解方法
- **SSE 传输支持**: 完整的 SSE (Server-Sent Events) 接口实现
- **Spring AI 集成**: 基于 Spring AI MCP Server WebMVC 实现

### 使用方式

#### 配置 MCP Server
```yaml
# Galaxy MCP 配置
galaxy:
  mcp:
    enabled: true

# Spring AI MCP Server 配置 - 使用 Spring AI 原生配置
spring:
  ai:
    mcp:
      server:
        enabled: true
        name: my-mcp-server
        instructions: "My MCP Server"
        sse-endpoint: /sse                     # 默认值
        sse-message-endpoint: /mcp/message     # 默认值
```

#### 创建 MCP Tool

使用 Spring AI 的 `@Tool` 注解标记 `Controller` 方法：

(注：目前MCP Tool设计目标是将 `API` 以 `MCP Server` 形式提供，因此实现中只主动扫描 `@Controller` 和 `@RestController` 注解的文件)

```java
@RestController
public class PaymentOrderController {

    /**
     * 确认订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单信息
     */
    @PostMapping("/{orderId}/confirm")
    @Tool(
            name = "confirmPaymentOrder",
            description = "确认支付订单，将订单状态从待确认改为待支付"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> confirmOrder(@PathVariable String orderId) {
        PaymentOrder order = paymentOrderService.confirmOrder(orderId);
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单确认成功"), order));
    }

}
```

### 配置说明

#### Galaxy MCP 配置（极简）

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | boolean | true | 是否启用 Galaxy MCP Starter |

#### Spring AI MCP 配置（推荐）

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| spring.ai.mcp.server.enabled | boolean | true | 是否启用 MCP Server |
| spring.ai.mcp.server.name | String | galaxy-mcp-server | MCP Server 名称 |
| spring.ai.mcp.server.version | String | 1.0.0 | MCP Server 版本 |
| spring.ai.mcp.server.instructions | String | Galaxy Boot MCP Server | MCP Server 描述 |
| spring.ai.mcp.server.sse-endpoint | String | /mcp/sse | SSE 端点路径 |
| spring.ai.mcp.server.sse-message-endpoint | String | /mcp/message | SSE 消息端点路径 |
| spring.ai.mcp.server.base-url | String | "" | 基础 URL 前缀 |

#### Spring AI @Tool 注解参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | String | 方法名 | Tool 的名称 |
| description | String | 必填 | Tool 的描述信息 |


#### 客户端 SSE 配置

在Cursor或Cline等MCP Server中的配置（JSON）示例如下：

```json
{
  "mcpServers": {
    "galaxy-mcp-server": {
      "timeout": 60,
      "type": "sse",
      "url": "http://localhost:8080/sse",
      "env": {
        "API_KEY": "${api-key-value}"
      }
    }
  }
}
```

其中 `url` 中 `http://localhost:8080/` 需要替换成实际的服务端地址，PATH `/sse` 跟配置文件中保持一致，不配置取默认值 `/sse`.
