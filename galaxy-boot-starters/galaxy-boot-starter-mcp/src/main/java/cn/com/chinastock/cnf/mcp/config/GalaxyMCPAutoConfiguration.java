package cn.com.chinastock.cnf.mcp.config;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.mcp.tool.GalaxyMCPToolRegistrar;
import io.modelcontextprotocol.server.McpSyncServer;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;

/**
 * Galaxy MCP 自动配置类
 * <p>
 * 负责自动配置 Galaxy MCP Server 相关组件，包括：
 * <ul>
 *   <li>Tool 注册器</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(prefix = "galaxy.mcp", name = "enabled", havingValue = "true", matchIfMissing = true)
public class GalaxyMCPAutoConfiguration {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(GalaxyMCPAutoConfiguration.class);

    public GalaxyMCPAutoConfiguration() {
        logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy MCP: Initializing Galaxy MCP Auto Configuration");
    }

    /**
     * 创建 Galaxy MCP Tool 注册器
     * <p>
     * 自动扫描 Controller 中的 @Tool 注解方法并注册为 MCP Tool
     * </p>
     *
     * @param mcpSyncServer MCP 同步服务器实例，用于在启动过程中动态添加被@Tool注解的工具
     * @return GalaxyMCPToolRegistrar 实例
     */
    @Bean
    public GalaxyMCPToolRegistrar galaxyMCPToolRegistrar(McpSyncServer mcpSyncServer) {
        logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy MCP: Creating Galaxy MCP Tool Registrar");
        mcpSyncServer.removeTool(GalaxyMCPToolPlaceholder.PLACEHOLDER_TOOL_NAME);
        return new GalaxyMCPToolRegistrar(mcpSyncServer);
    }

    /**
     * 创建 Galaxy MCP Tool 占位工具类
     * <p>
     * 在McpSyncServer的addTools方法实现逻辑中，如果初始化没有任何工具(mcpAsyncServer.serverCapabilities.tools() == null)，addTool方法会失败
     * 因此在服务启动时注册个占位工具，在GalaxyMCPToolRegistrar Bean初始化过程中，拿到McpSyncServer实例后，先删除占位工具，再添加真实工具
     * </p>
     *
     * @return GalaxyMCPToolPlaceholder 实例
     */
    @Bean
    public GalaxyMCPToolPlaceholder galaxyMCPToolPlaceholder() {
        return new GalaxyMCPToolPlaceholder();
    }

    @Bean
    public ToolCallbackProvider galaxyMCPTools(GalaxyMCPToolPlaceholder galaxyMCPToolPlaceholder) {
        return MethodToolCallbackProvider.builder().toolObjects(galaxyMCPToolPlaceholder).build();
    }
}
