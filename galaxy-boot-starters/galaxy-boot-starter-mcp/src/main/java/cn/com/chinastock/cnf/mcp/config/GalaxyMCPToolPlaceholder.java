package cn.com.chinastock.cnf.mcp.config;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

@Component
public class GalaxyMCPToolPlaceholder {

    public static final String PLACEHOLDER_TOOL_NAME = "galaxyMCPToolPlaceholder";

    @Tool(
            name = PLACEHOLDER_TOOL_NAME,
            description = "Galaxy MCP Tool placeholder, will be removed during application startup."
    )
    public String galaxyMCPToolPlaceholder() {
        return "Galaxy MCP Tool placeholder, will be removed during application startup.";
    }
}
