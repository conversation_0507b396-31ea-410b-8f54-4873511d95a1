package cn.com.chinastock.cnf.mcp.config;

import cn.com.chinastock.cnf.mcp.tool.GalaxyMCPToolRegistrar;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Galaxy MCP Auto Configuration 条件测试
 * 测试当 galaxy.mcp.enabled=false 时的行为
 */
@SpringBootTest(classes = {
        GalaxyMCPAutoConfiguration.class
})
@EnableAutoConfiguration
@TestPropertySource(properties = {
        "galaxy.mcp.enabled=false"
})
class GalaxyMCPAutoConfigurationConditionalTest {

    @Test
    void shouldNotCreateBeansWhenDisabled(ApplicationContext context) {
        // 验证当 galaxy.mcp.enabled=false 时，不会创建相关 Bean
        
        // 检查 GalaxyMCPToolRegistrar 是否不存在
        assertFalse(context.containsBean("galaxyMCPToolRegistrar"), 
            "GalaxyMCPToolRegistrar should not be created when disabled");
        
        // 检查 ToolCallbackProvider 是否不存在
        assertFalse(context.containsBean("galaxyMCPToolCallbackProvider"), 
            "ToolCallbackProvider should not be created when disabled");
    }

    @Test
    void shouldNotHaveGalaxyMCPToolRegistrarBean(ApplicationContext context) {
        try {
            context.getBean(GalaxyMCPToolRegistrar.class);
            fail("Should not find GalaxyMCPToolRegistrar bean when disabled");
        } catch (Exception e) {
            // 期望抛出异常，因为 Bean 不应该存在
            assertTrue(e.getMessage().contains("No qualifying bean"));
        }
    }

    @Test
    void shouldNotHaveToolCallbackProviderBean(ApplicationContext context) {
        try {
            context.getBean("galaxyMCPToolCallbackProvider", ToolCallbackProvider.class);
            fail("Should not find ToolCallbackProvider bean when disabled");
        } catch (Exception e) {
            // 期望抛出异常，因为 Bean 不应该存在
            // 检查异常消息是否包含常见的 "bean not found" 相关字符串
            String message = e.getMessage().toLowerCase();
            assertTrue(message.contains("no qualifying bean") || 
                      message.contains("no bean named") || 
                      message.contains("no such bean") ||
                      message.contains("bean not found"),
                "Exception message should indicate bean not found, but was: " + e.getMessage());
        }
    }
} 