package cn.com.chinastock.cnf.mcp.config;

import cn.com.chinastock.cnf.mcp.tool.GalaxyMCPToolRegistrar;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;
import org.junit.jupiter.api.Test;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Galaxy MCP Auto Configuration 集成测试
 */
@SpringBootTest(classes = {
        GalaxyMCPAutoConfiguration.class
})
@EnableAutoConfiguration
@ComponentScan(basePackages = "cn.com.chinastock.cnf.mcp")
class GalaxyMCPAutoConfigurationIntegrationTest {

    @Autowired(required = false)
    private GalaxyMCPToolRegistrar toolRegistrar;

    @Autowired(required = false)
    private ToolCallbackProvider toolCallbackProvider;

    @Test
    void shouldCreateGalaxyMCPToolRegistrar() {
        // 验证 GalaxyMCPToolRegistrar Bean 被创建
        assertNotNull(toolRegistrar, "GalaxyMCPToolRegistrar should be created");
        assertInstanceOf(GalaxyMCPToolRegistrar.class, toolRegistrar);
    }

    @Test
    void shouldCreateToolCallbackProvider() {
        // 验证 ToolCallbackProvider Bean 被创建
        assertNotNull(toolCallbackProvider, "ToolCallbackProvider should be created");
        assertInstanceOf(ToolCallbackProvider.class, toolCallbackProvider);
    }

    // 测试用的内部类
    @Controller
    static class TestController {
        @org.springframework.ai.tool.annotation.Tool(name = "testTool")
        public String testMethod() {
            return "test";
        }
    }

    @RestController
    static class TestRestController {
        @org.springframework.ai.tool.annotation.Tool(name = "testTool1")
        public String testMethod1() {
            return "test1";
        }

        @org.springframework.ai.tool.annotation.Tool(name = "testTool2")
        public String testMethod2() {
            return "test2";
        }
    }

    static class PlainClass {
        public String plainMethod() {
            return "plain";
        }
    }
} 