# Galaxy Boot Starter MCP

> Galaxy Boot Starter MCP 是一个基于 Spring AI MCP (Model Context Protocol) 的组件，提供完整的 MCP Server 功能支持，包括 SSE (Server-Sent Events) 服务器传输。通过 Spring AI 的 `@Tool` 注解，可以轻松将 Spring Boot RestController 的接口方法注册为 MCP Tool。

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter MCP`，只需要引入对应 `starter` 即可：
使用 `group ID` 为 `cn.com.chinastock` 和 `artifact ID` 为 `galaxy-boot-starter-mcp` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-mcp</artifactId>
</dependency>
```
