package cn.com.chinastock.cnf.swagger.webflux.filter;

import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * SwaggerPathMatchingWebFilter 的单元测试类
 */
class SwaggerPathMatchingWebFilterTest {

    private SwaggerPathMatchingWebFilter createFilter() {
        SwaggerProperties swaggerProperties = new SwaggerProperties();
        swaggerProperties.getAuth().setUsername("admin");
        swaggerProperties.getAuth().setPassword("secret");
        SwaggerAuthWebFilter authFilter = new SwaggerAuthWebFilter(swaggerProperties);
        
        List<String> pathPatterns = Arrays.asList(
                "/swagger-ui.html", 
                "/v3/api-docs/**", 
                "/swagger-ui/**"
        );
        
        return new SwaggerPathMatchingWebFilter(pathPatterns, authFilter);
    }

    private ServerWebExchange createMockExchange(String path, String authHeader) {
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerHttpResponse response = mock(ServerHttpResponse.class);
        HttpHeaders requestHeaders = new HttpHeaders();
        HttpHeaders responseHeaders = new HttpHeaders();

        if (authHeader != null) {
            requestHeaders.add("Authorization", authHeader);
        }

        when(exchange.getRequest()).thenReturn(request);
        when(exchange.getResponse()).thenReturn(response);
        when(request.getHeaders()).thenReturn(requestHeaders);
        when(request.getURI()).thenReturn(URI.create("http://localhost:8080" + path));
        when(response.setComplete()).thenReturn(Mono.empty());
        when(response.getHeaders()).thenReturn(responseHeaders);

        return exchange;
    }

    @Test
    void should_pass_through_non_swagger_paths() {
        SwaggerPathMatchingWebFilter filter = createFilter();
        ServerWebExchange exchange = createMockExchange("/api/users", null);
        WebFilterChain chain = mock(WebFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void should_apply_auth_filter_to_swagger_paths() {
        SwaggerPathMatchingWebFilter filter = createFilter();
        ServerWebExchange exchange = createMockExchange("/swagger-ui.html", null);
        WebFilterChain chain = mock(WebFilterChain.class);

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        // 验证认证过滤器被调用（返回401）
        verify(exchange.getResponse()).setStatusCode(any());
    }

    @Test
    void should_match_swagger_ui_paths() {
        SwaggerPathMatchingWebFilter filter = createFilter();
        
        // 测试各种Swagger路径
        testSwaggerPath(filter, "/swagger-ui.html");
        testSwaggerPath(filter, "/swagger-ui/index.html");
        testSwaggerPath(filter, "/v3/api-docs");
        testSwaggerPath(filter, "/v3/api-docs/swagger-config");
    }

    private void testSwaggerPath(SwaggerPathMatchingWebFilter filter, String path) {
        ServerWebExchange exchange = createMockExchange(path, null);
        WebFilterChain chain = mock(WebFilterChain.class);

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        // 验证认证过滤器被调用
        verify(exchange.getResponse()).setStatusCode(any());
    }
}
