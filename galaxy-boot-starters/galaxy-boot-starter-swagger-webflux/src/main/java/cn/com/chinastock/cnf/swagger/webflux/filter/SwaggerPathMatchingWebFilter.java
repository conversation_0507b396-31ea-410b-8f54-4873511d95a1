package cn.com.chinastock.cnf.swagger.webflux.filter;

import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * SwaggerPathMatchingWebFilter 类负责路径匹配，类似于WebMVC版本的拦截器路径配置。
 * 只有匹配指定路径的请求才会被传递给认证过滤器。
 */
public class SwaggerPathMatchingWebFilter implements WebFilter, Ordered {
    private final List<String> pathPatterns;
    private final SwaggerAuthWebFilter authFilter;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public SwaggerPathMatchingWebFilter(List<String> pathPatterns, SwaggerAuthWebFilter authFilter) {
        this.pathPatterns = pathPatterns;
        this.authFilter = authFilter;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String path = exchange.getRequest().getURI().getPath();
        
        // 检查是否匹配Swagger路径
        boolean isSwaggerPath = pathPatterns.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
        
        if (isSwaggerPath) {
            // 如果是Swagger路径，使用认证过滤器
            return authFilter.filter(exchange, chain);
        } else {
            // 否则直接通过
            return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
