package cn.com.chinastock.cnf.swagger.webflux.annotations;

import cn.com.chinastock.cnf.core.http.annotations.ApiMetaCodes;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import org.springdoc.core.customizers.OpenApiCustomizer;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * ApiMetaCodesCustomizer 类用于处理 {@link ApiMetaCodes}，生成{@link cn.com.chinastock.cnf.core.http.Meta}的文档描述。
 *
 * <AUTHOR>
 */
public class ApiMetaCodesCustomizer implements OpenApiCustomizer {

    private static final String EXTENSION_X_API_META_CODES = "x-api-meta-codes";

    @Override
    public void customise(OpenAPI openApi) {
        Components components = openApi.getComponents();
        if (components == null) {
            return;
        }

        try {
            openApi.getPaths().forEach((path, pathItem) ->
                    pathItem.readOperations().forEach(operation -> processOperation(openApi, operation, components)));
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, "process ApiMetaCodes error", e);
        }
    }

    private void processOperation(OpenAPI openApi, Operation operation, Components components) {
        if (operation.getExtensions() == null
                || !operation.getExtensions().containsKey(EXTENSION_X_API_META_CODES)
                || !(operation.getExtensions().get(EXTENSION_X_API_META_CODES) instanceof ApiMetaCodes)) {
            return;
        }

        ApiResponse apiResponse = operation.getResponses().get("200");
        if (apiResponse == null) {
            return;
        }

        ApiMetaCodes apiMetaCodes = (ApiMetaCodes) operation.getExtensions().get(EXTENSION_X_API_META_CODES);
        String description = Arrays.stream(apiMetaCodes.value())
                .map(apiMetaCode -> apiMetaCode.code() + ":" + apiMetaCode.description())
                .collect(Collectors.joining("; "));

        apiResponse.getContent().values().forEach(mediaType ->
                        modifyCodeSchema(openApi, mediaType.getSchema(), components, description));
    }

    private void modifyCodeSchema(OpenAPI openApi, Schema<?> schema, Components components, String description) {
        if (schema == null) {
            return;
        }

        if (schema.get$ref() != null) {
            String schemaName = schema.get$ref().replace("#/components/schemas/", "");
            Schema<?> resolvedSchema = components.getSchemas().get(schemaName);
            if (resolvedSchema != null) {
                modifyCodeSchema(openApi, resolvedSchema, components, description);
            }
        }
        // 处理内联 Schema
        else if (schema.getProperties() != null && schema.getProperties().containsKey("meta")) {
            Schema<?> metaSchema = (Schema<?>) schema.getProperties().get("meta");
            if (metaSchema == null) return;

            // 处理引用的 meta
            if (metaSchema.get$ref() != null) {
                String metaSchemaName = metaSchema.get$ref().replace("#/components/schemas/", "");
                Schema<?> resolvedMetaSchema = components.getSchemas().get(metaSchemaName);
                if (resolvedMetaSchema != null && resolvedMetaSchema.getProperties() != null) {
                    Schema<?> codeSchema = (Schema<?>) resolvedMetaSchema.getProperties().get("code");
                    if (codeSchema != null) {
                        codeSchema.setDescription(description);
                    }
                } else {
                    // 克隆并修改引用的 meta schema
                    Schema<?> clonedMeta = new Schema<>();
                    clonedMeta.setType("object");
                    Map<String, Schema> clonedProperties = new HashMap<>();
                    clonedProperties.put("success", new Schema<>().type("boolean"));
                    clonedProperties.put("message", new Schema<>().type("string"));
                    clonedProperties.put("code", new Schema<>()
                            .type("string")
                            .description(description));
                    clonedMeta.setProperties(clonedProperties);
                    schema.getProperties().put("meta", clonedMeta);
                }
            }
            // 直接修改内联 meta
            else if (metaSchema.getProperties() != null) {
                Schema<?> codeSchema = (Schema<?>) metaSchema.getProperties().get("code");
                if (codeSchema != null) {
                    codeSchema.setDescription(description);
                }
            }
        }
    }
}
