package cn.com.chinastock.cnf.swagger.webflux.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

import static java.util.Base64.getDecoder;

/**
 * SwaggerAuthWebFilter 类是WebFlux环境下的Swagger认证过滤器。
 * 该过滤器负责对Swagger UI相关路径进行Basic认证保护。
 *
 * <p>与WebMVC版本的SwaggerAuthInterceptor功能相同，但适配了WebFlux的响应式编程模型。</p>
 */
public class SwaggerAuthWebFilter implements WebFilter {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BASIC_PREFIX = "Basic ";
    private static final String AUTHENTICATION_SCHEME = "Basic realm=\"Galaxy Swagger UI\"";

    private final SwaggerProperties swaggerProperties;

    public SwaggerAuthWebFilter(SwaggerProperties swaggerProperties) {
        this.swaggerProperties = swaggerProperties;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String auth = request.getHeaders().getFirst(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(auth) || !auth.startsWith(BASIC_PREFIX)) {
            sendUnauthorizedResponse(exchange.getResponse());
            GalaxyLogger.warn(LogCategory.APP_LOG, "Missing or invalid Authorization header");
            return exchange.getResponse().setComplete();
        }

        try {
            String base64Credentials = auth.substring(BASIC_PREFIX.length()).trim();
            String credentials = new String(getDecoder().decode(base64Credentials), StandardCharsets.UTF_8);
            String[] values = credentials.split(":", 2);

            if (values.length != 2 || !isValidCredentials(values[0], values[1])) {
                sendUnauthorizedResponse(exchange.getResponse());
                GalaxyLogger.warn(LogCategory.APP_LOG, "Invalid credentials attempt");
                return exchange.getResponse().setComplete();
            }

            return chain.filter(exchange);
        } catch (IllegalArgumentException e) {
            sendUnauthorizedResponse(exchange.getResponse());
            GalaxyLogger.warn(LogCategory.APP_LOG, "Failed to decode basic authentication", e);
            return exchange.getResponse().setComplete();
        }
    }

    private boolean isValidCredentials(String username, String password) {
        return constantTimeEquals(username, swaggerProperties.getAuth().getUsername()) &&
                constantTimeEquals(password, swaggerProperties.getAuth().getPassword());
    }

    private void sendUnauthorizedResponse(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("WWW-Authenticate", AUTHENTICATION_SCHEME);
    }

    private boolean constantTimeEquals(String a, String b) {
        byte[] bytes1 = a.getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = b.getBytes(StandardCharsets.UTF_8);
        return java.security.MessageDigest.isEqual(bytes1, bytes2);
    }
}
