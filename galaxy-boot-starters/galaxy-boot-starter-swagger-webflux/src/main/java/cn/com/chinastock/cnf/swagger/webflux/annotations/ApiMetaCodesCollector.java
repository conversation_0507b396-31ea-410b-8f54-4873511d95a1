package cn.com.chinastock.cnf.swagger.webflux.annotations;

import cn.com.chinastock.cnf.core.http.annotations.ApiMetaCodes;
import io.swagger.v3.oas.models.Operation;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.web.method.HandlerMethod;


/**
 * ApiMetaCodesCollector 类用于预处理 {@link ApiMetaCodes}。
 *
 * <AUTHOR>
 */
public class ApiMetaCodesCollector implements OperationCustomizer {

    private static final String EXTENSION_X_API_META_CODES = "x-api-meta-codes";

    @Override
    public Operation customize(Operation operation, HandlerMethod handlerMethod) {
        ApiMetaCodes annotation = handlerMethod.getMethodAnnotation(ApiMetaCodes.class);
        if (annotation == null) {
            return operation;
        }

        operation.addExtension(EXTENSION_X_API_META_CODES, annotation);
        return operation;
    }
}
