package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;

import java.util.ArrayList;

public class FastJsonFeatureUtil {
    /**
     * 现在采用的是 Fastjson 2 版本的配置方式，即通过 JSONWriter.Feature 集合来配置序列化特性。
     * <p>
     * 需要同步修改：{@link cn.com.chinastock.cnf.security.output.FastJsonFilterUtil#handleFastJsonResponse}
     *
     * @param fastJsonProperties
     * @return 序列化特性集合
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/fastjson_1_upgrade_cn">FASTJSON 1.x升级指南</a>
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/Features_cn>通过Features配置序列化和反序列化的行为</a>
     */
    public static JSONWriter.Feature[] collectWriterFromProperties(FastJsonProperties fastJsonProperties) {
        ArrayList<JSONWriter.Feature> features = new ArrayList<>();
        ///  需要同步修改 cn.com.chinastock.cnf.security.output.FastJsonFilterUtil
        features.add(JSONWriter.Feature.WriteBigDecimalAsPlain);
        features.add(JSONWriter.Feature.IgnoreNonFieldGetter);

        if (fastJsonProperties.isWriteMapNullValue()) {
            features.add(JSONWriter.Feature.WriteMapNullValue);
        }

        if (fastJsonProperties.isWriteNullStringAsEmpty()) {
            features.add(JSONWriter.Feature.WriteNullStringAsEmpty);
        }

        return features.toArray(new JSONWriter.Feature[0]);
    }

    public static JSONReader.Feature[] collectReaderFromProperties(FastJsonProperties fastJsonProperties) {
        ArrayList<JSONReader.Feature> features = new ArrayList<>();
        ///  需要同步修改 cn.com.chinastock.cnf.security.output.FastJsonFilterUtil

        if (fastJsonProperties.isSupportSmartMatch()) {
            features.add(JSONReader.Feature.SupportSmartMatch);
        }


        return features.toArray(new JSONReader.Feature[0]);
    }
}
