package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ReactiveWebApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Galaxy FastJson WebFlux 配置测试类
 * 
 * <AUTHOR> Boot Team
 */
public class GalaxyFastJsonWebFluxConfigTest {

    private final ReactiveWebApplicationContextRunner contextRunner = new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyFastJsonWebFluxConfig.class))
            .withUserConfiguration(TestConfiguration.class);


    @Test
    public void testFastJsonConfigBean() {
        this.contextRunner.run(context -> {
            assertTrue(context.containsBean("webfluxFastJsonConfig"));
            FastJsonConfig config = context.getBean("webfluxFastJsonConfig", FastJsonConfig.class);
            assertNotNull(config);
            assertNotNull(config.getCharset());
        });
    }

    @Test
    public void testFastjson2DecoderBean() {
        this.contextRunner.run(context -> {
            // 验证 Fastjson2Decoder Bean 被创建
            assertTrue(context.containsBean("fastjson2Decoder"));
            Fastjson2Decoder decoder = context.getBean(Fastjson2Decoder.class);
            assertNotNull(decoder);
        });
    }

    @Test
    public void testFastjson2EncoderBean() {
        this.contextRunner.run(context -> {
            // 验证 Fastjson2Encoder Bean 被创建
            assertTrue(context.containsBean("fastjson2Encoder"));
            Fastjson2Encoder encoder = context.getBean(Fastjson2Encoder.class);
            assertNotNull(encoder);
        });
    }

    @Test
    public void testConfigurationWithCustomProperties() {
        this.contextRunner
                .withPropertyValues(
                        "galaxy.fastjson.writeMapNullValue=true",
                        "galaxy.fastjson.writeNullStringAsEmpty=true",
                        "galaxy.fastjson.supportSmartMatch=true"
                )
                .run(context -> {
                    // 验证自定义属性被正确应用
                    FastJsonConfig config = context.getBean("webfluxFastJsonConfig", FastJsonConfig.class);
                    assertNotNull(config);
                    
                    // 验证 Writer 特性
                    assertNotNull(config.getWriterFeatures());
                    assertTrue(config.getWriterFeatures().length > 0);
                    
                    // 验证 Reader 特性
                    assertNotNull(config.getReaderFeatures());
                    assertTrue(config.getReaderFeatures().length > 0);
                });
    }

    @Test
    public void testConditionalOnWebApplicationReactive() {
        // 测试在非 WebFlux 环境下配置不会被加载
        new ReactiveWebApplicationContextRunner()
                .withConfiguration(AutoConfigurations.of(GalaxyFastJsonWebFluxConfig.class))
                .withUserConfiguration(TestConfiguration.class)
                .run(context -> {
                    // 在 ReactiveWebApplicationContextRunner 中应该加载配置
                    assertTrue(context.containsBean("galaxyFastJsonWebFluxConfig"));
                });
    }

    @Test
    public void testConditionalOnMissingBean() {
        this.contextRunner
                .withBean("customFastjson2Decoder", Fastjson2Decoder.class, () -> {
                    // 创建一个自定义的 Decoder，需要提供非空的 ObjectMapper
                    return new Fastjson2Decoder(new com.fasterxml.jackson.databind.ObjectMapper());
                })
                .run(context -> {
                    // 验证当存在自定义 Bean 时，默认的不会被创建
                    assertTrue(context.containsBean("customFastjson2Decoder"));
                    // 但是配置类本身仍然会被加载
                    assertTrue(context.containsBean("galaxyFastJsonWebFluxConfig"));
                });
    }

    /**
     * 测试配置类，提供测试所需的 ObjectMapper Bean
     */
    @Configuration
    static class TestConfiguration {

        @Bean
        public ObjectMapper objectMapper() {
            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());
            mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return mapper;
        }
    }
}
