package cn.com.chinastock.cnf.fastjson;

import java.math.BigDecimal;

public class TestModel {
    private String name;
    private BigDecimal amount;
    private transient String transientField;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTransientField() {
        return transientField;
    }

    public void setTransientField(String transientField) {
        this.transientField = transientField;
    }
}
