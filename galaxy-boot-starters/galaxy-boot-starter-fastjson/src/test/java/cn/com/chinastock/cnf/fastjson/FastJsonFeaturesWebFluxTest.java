package cn.com.chinastock.cnf.fastjson;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import java.math.BigDecimal;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

/**
 * FastJSON WebFlux 特性测试类
 * <p>
 * 专门测试 WriteBigDecimalAsPlain 和 IgnoreNonFieldGetter 两个特性
 * 在 WebFlux 环境中的实际行为
 * </p>
 * 
 * <AUTHOR> Boot Team
 */
@SpringBootTest(
    classes = FastJsonFeaturesWebFluxTest.TestApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = {"spring.main.web-application-type=reactive"}
)
@TestPropertySource(properties = {
    "galaxy.fastjson.writeMapNullValue=false",
    "galaxy.fastjson.writeNullStringAsEmpty=false"
})
public class FastJsonFeaturesWebFluxTest {

    @Autowired
    private WebTestClient webTestClient;

    /**
     * 测试 WriteBigDecimalAsPlain 特性
     * 验证 BigDecimal 以普通数字格式输出，而不是科学计数法
     */
    @Test
    public void testWriteBigDecimalAsPlainFeature() {
        // 测试大数值的 BigDecimal
        webTestClient.get()
                .uri("/api/bigdecimal")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .value(response -> {
                    System.out.println("=== WriteBigDecimalAsPlain Feature Test ===");
                    System.out.println("BigDecimal response: " + response);

                    // 验证响应包含普通数字格式，而不是科学计数法
                    // 大数值应该以普通格式显示，如 "123456789.123456789"
                    // 而不是科学计数法 "1.23456789123456789E8"
                    assert response.contains("123456789.123456789") :
                        "BigDecimal should be serialized as plain number, got: " + response;
                    assert !response.contains("E") :
                        "BigDecimal should not use scientific notation, got: " + response;
                    System.out.println("✓ WriteBigDecimalAsPlain feature is working correctly!");
                });
    }

    /**
     * 测试 IgnoreNonFieldGetter 特性
     * 验证非字段的 getter 方法被忽略，不会被序列化
     */
    @Test
    public void testIgnoreNonFieldGetterFeature() {
        webTestClient.get()
                .uri("/api/nonfieldgetter")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .value(response -> {
                    System.out.println("=== IgnoreNonFieldGetter Feature Test ===");
                    System.out.println("NonFieldGetter response: " + response);

                    // 验证响应包含实际字段
                    assert response.contains("\"name\":\"Test User\"") :
                        "Response should contain actual field, got: " + response;

                    // 验证响应不包含非字段的 getter 方法返回值
                    assert !response.contains("\"computedValue\"") :
                        "Response should not contain non-field getter result, got: " + response;
                    assert !response.contains("\"dynamicProperty\"") :
                        "Response should not contain dynamic property, got: " + response;
                    System.out.println("✓ IgnoreNonFieldGetter feature is working correctly!");
                });
    }

    /**
     * 测试两个特性同时工作
     */
    @Test
    public void testBothFeaturesWorking() {
        webTestClient.get()
                .uri("/api/combined")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .value(response -> {
                    System.out.println("=== Combined Features Test ===");
                    System.out.println("Combined features response: " + response);

                    // 验证 BigDecimal 以普通格式输出
                    assert response.contains("999999999.999999999") :
                        "BigDecimal should be plain format, got: " + response;
                    assert !response.contains("E") :
                        "Should not use scientific notation, got: " + response;

                    // 验证非字段 getter 被忽略
                    assert !response.contains("\"calculatedTotal\"") :
                        "Should not contain calculated field, got: " + response;

                    // 验证实际字段存在
                    assert response.contains("\"name\"") && response.contains("\"amount\"") :
                        "Should contain actual fields, got: " + response;
                    System.out.println("✓ Both WriteBigDecimalAsPlain and IgnoreNonFieldGetter features are working correctly!");
                });
    }

    /**
     * 测试配置类
     */
    @Configuration
    @EnableAutoConfiguration
    static class TestApplication {

        @Bean
        public RouterFunction<ServerResponse> testRoutes() {
            return route(GET("/api/bigdecimal"), request -> {
                TestBigDecimalModel model = new TestBigDecimalModel();
                model.setAmount(new BigDecimal("123456789.123456789"));
                return ServerResponse.ok().bodyValue(model);
            })
            .andRoute(GET("/api/nonfieldgetter"), request -> {
                TestNonFieldGetterModel model = new TestNonFieldGetterModel();
                model.setName("Test User");
                return ServerResponse.ok().bodyValue(model);
            })
            .andRoute(GET("/api/combined"), request -> {
                TestCombinedModel model = new TestCombinedModel();
                model.setName("Combined Test");
                model.setAmount(new BigDecimal("999999999.999999999"));
                return ServerResponse.ok().bodyValue(model);
            });
        }
    }

    /**
     * 测试 BigDecimal 序列化的模型
     */
    public static class TestBigDecimalModel {
        private BigDecimal amount;

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }
    }

    /**
     * 测试非字段 getter 的模型
     */
    public static class TestNonFieldGetterModel {
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        // 这是一个非字段的 getter，应该被 IgnoreNonFieldGetter 特性忽略
        public String getComputedValue() {
            return "This should not appear in JSON";
        }

        // 另一个非字段的 getter
        public String getDynamicProperty() {
            return "Dynamic: " + System.currentTimeMillis();
        }
    }

    /**
     * 测试两个特性组合的模型
     */
    public static class TestCombinedModel {
        private String name;
        private BigDecimal amount;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        // 非字段 getter，应该被忽略
        public BigDecimal getCalculatedTotal() {
            return amount != null ? amount.multiply(new BigDecimal("2")) : BigDecimal.ZERO;
        }
    }
}
