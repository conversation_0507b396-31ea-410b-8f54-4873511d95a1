package cn.com.chinastock.cnf.fastjson;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import static org.springframework.web.reactive.function.server.RouterFunctions.route;
import static org.springframework.web.reactive.function.server.RequestPredicates.POST;
import static org.junit.jupiter.api.Assertions.*;

/**
 * WebFlux FastJSON 堆栈跟踪验证测试
 * <p>
 * 通过故意触发异常来验证 WebFlux 确实在使用 FastJSON 进行 JSON 处理
 * </p>
 * 
 * <AUTHOR> Boot Team
 */
@SpringBootTest(
    classes = WebFluxStackTraceTest.TestApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = {"spring.main.web-application-type=reactive"}
)
@TestPropertySource(properties = {
    "galaxy.fastjson.writeMapNullValue=true",
    "galaxy.fastjson.writeNullStringAsEmpty=true"
})
public class WebFluxStackTraceTest {

    @Autowired
    private WebTestClient webTestClient;

    @Test
    public void testFastJsonExceptionStackTrace() {
        // 发送一个会导致 FastJSON 解析异常的请求
        // 这应该返回 400 错误，证明 FastJSON 在处理 JSON 解析
        webTestClient.post()
                .uri("/api/test")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue("{\"id\":\"not_a_number\",\"invalidField\":}")  // 无效的 JSON
                .exchange()
                .expectStatus().is4xxClientError()
                .expectBody(String.class)
                .consumeWith(response -> {
                    String body = response.getResponseBody();
                    System.err.println("=== FastJSON Error Response ===");
                    System.err.println("Response body: " + body);
                    
                    // 检查响应体是否包含 FastJSON 相关的错误信息
                    assertNotNull(body, "Response body should not be null");
                    assertTrue(body.contains("JSON parsing failed"), 
                        "Response should indicate JSON parsing failure");
                });
    }

    @Test 
    public void testDirectFastJsonError() {
        // 测试无效 JSON 格式
        webTestClient.post()
                .uri("/api/test")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue("{invalid json format}")  // 完全无效的 JSON
                .exchange()
                .expectStatus().is4xxClientError()
                .expectBody(String.class)
                .consumeWith(response -> {
                    String body = response.getResponseBody();
                    System.err.println("=== Invalid JSON Error Response ===");
                    System.err.println("Response body: " + body);
                    
                    // 验证响应包含 JSON 解析失败的信息
                    assertNotNull(body, "Response body should not be null");
                    assertTrue(body.contains("JSON parsing failed"), 
                        "Response should indicate JSON parsing failure");
                });
    }

    /**
     * 测试应用配置
     */
    @SpringBootApplication
    @org.springframework.web.reactive.config.EnableWebFlux
    static class TestApplication {
        
        @Bean
        public RouterFunction<ServerResponse> routes() {
            return route(POST("/api/test"), request -> 
                request.bodyToMono(TestUser.class)
                    .flatMap(user -> ServerResponse.ok().bodyValue(user))
                    .onErrorResume(throwable -> {
                        // 打印完整的异常堆栈跟踪来证明使用了 FastJSON
                        System.err.println("=== FastJSON Exception Stack Trace ===");
                        System.err.println("Exception type: " + throwable.getClass().getName());
                        System.err.println("Exception message: " + throwable.getMessage());
                        throwable.printStackTrace();
                        
                        // 检查异常类型和堆栈跟踪
                        String stackTrace = getStackTraceAsString(throwable);
                        boolean containsFastJson = stackTrace.contains("fastjson") || 
                                                 stackTrace.contains("alibaba") ||
                                                 throwable.getClass().getName().contains("fastjson") ||
                                                 throwable.getClass().getName().contains("alibaba");
                        
                        System.err.println("Contains FastJSON in stack trace: " + containsFastJson);
                        
                        return ServerResponse.badRequest()
                            .bodyValue("JSON parsing failed: " + throwable.getClass().getSimpleName() + 
                                     " (FastJSON detected: " + containsFastJson + ")");
                    })
            );
        }
        
        private String getStackTraceAsString(Throwable throwable) {
            java.io.StringWriter sw = new java.io.StringWriter();
            java.io.PrintWriter pw = new java.io.PrintWriter(sw);
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

    /**
     * 测试用户类
     */
    static class TestUser {
        private Long id;
        private String name;

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
}
