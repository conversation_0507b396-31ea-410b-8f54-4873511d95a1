### 组件介绍

Galaxy Boot Fastjson Starter 基于 Fastjson 2.x 版本，与 Fastjson 1.x 版本相比，Fastjson 2.x 在性能、安全性和数据完整性上均有显著提升。
**但在序列化默认行为上存在差异，如字段大小写敏感等，使用需要注意。**

[FastJson 使用场景](https://github.com/alibaba/fastjson2/wiki/design_doc_cn)：

- **服务器应用**：高性能、定制化序列化/反序列化、模块化支持、JDK 8/9 优化、静态化加速、安全性（不能有反序列化安全问题）、JSON/JSONB 两种协议支持。
- **大数据**：部分解析、JSONPath 等特性。
- **Android 客户端**：首次执行性能、静态化加速、JSON/JSONB 两种协议支持。

当前支持配置：

```yaml
galaxy:
  fastjson:
    write-map-null-value: true       # WriteMapNullValue，是否输出 null 字段，默认为 false 
    write-null-string-as-empty: true # 将 String 类型字段的空值序列化输出为空字符串，默认为false
    support-smart-match: true        # 序列化时支持智能匹配，默认为false
```

### JSON 序列化框架对比与分析

**总览对比**

基于性能、安全性、数据完整性及社区活跃度的总体对比如下：

| 框架名称             | 性能表现         | 安全性分析          | 数据完整性     | 社区活跃度            | 适用场景            |
|------------------|--------------|----------------|-----------|------------------|-----------------|
| **Jackson**      | 性能中等，体积适中    | 对反序列化漏洞的防护表现良好 | 支持完整对象图   | 社区活跃，Issues 相对较少 | 适合需要高安全性的通用场景   |
| **Fastjson 1.x** | 性能优异，但漏洞风险较大 | 安全性较弱，易受漏洞威胁   | 数据完整性支持一般 | 官方停止维护，Issue 较多  | 不建议在生产环境使用      |
| **Fastjson 2.x** | 性能显著优化，业界领先  | 安全性显著增强，降低攻击风险 | 数据完整性提升   | 官方维护活跃，Issue 数适中 | 高性能 JSON 解析和序列化 |
| **Gson**         | 性能中等，体积较小    | 安全性一般，无反序列化防护  | 支持基本数据完整性 | 社区稳定，Issues 较少   | 简单轻量的 JSON 处理任务 |
| **org.json**     | 性能较差，功能简单    | 安全性一般，无反序列化防护  | 支持基本数据完整性 | 社区稳定，Issues 较少   | 适合小型应用或旧系统兼容    |

##### 数据来源与对比维度

**社区活跃度：Issue 数对比**

社区维护和开发的活跃度可以通过相关 Issue 数量和修复频率来衡量：

| 框架名称         | GitHub Issue 链接                                                   | Issue 总数 | 社区状态    |
|--------------|-------------------------------------------------------------------|----------|---------|
| Jackson      | [Jackson Databind](https://github.com/FasterXML/jackson-databind) | 447      | 活跃，响应较快 |
| Fastjson 1.x | [Fastjson 1](https://github.com/alibaba/fastjson/issues)          | 1935     | 停止维护    |
| Fastjson 2.x | [Fastjson 2](https://github.com/alibaba/fastjson2)                | 487      | 开发积极维护  |
| Gson         | [Gson](https://github.com/google/gson)                            | 234      | 稳定      |
| org.json     | [org.json](https://github.com/stleary/JSON-java)                  | 20       | 稳定      |

> **结论**：Jackson 和 Gson 社区活跃，Fastjson 2.x 的维护频率较高，而 Fastjson 1.x 已停止更新，生产环境应避免使用。

**流行度：依赖统计（国外三方源统计）**

JSON 框架的流行程度可以通过依赖于该框架的开源库数量体现：

| 框架名称         | 依赖总数     | 数据来源                                                                                               |
|--------------|----------|----------------------------------------------------------------------------------------------------|
| Jackson      | 31,958 个 | [Maven Repository](https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind) |
| Gson         | 25,031 个 | [Maven Repository](https://mvnrepository.com/artifact/com.google.code.gson/gson)                   |
| Fastjson 1.x | 7,065 个  | [Maven Repository](https://mvnrepository.com/artifact/com.alibaba/fastjson)                        |
| org.json     | 6,145 个  | [Maven Repository](https://mvnrepository.com/artifact/org.json/json)                               |
| Fastjson 2.x | 794 个    | [Maven Repository](https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2)             |

> **结论**：Jackson 拥有最高的市场占有率，Gson 紧随其后，而 Fastjson 2.x 虽性能出色但仍偏向起步阶段，依赖数量较少。

**安全性：CVE 漏洞统计**

安全性对比可以通过 CVE 漏洞的曝光及修复数加以体现：

| 框架名称     | CVE 漏洞总数 | 示例                                                                  |
|----------|----------|---------------------------------------------------------------------|
| Jackson  | 87       | [CVE 数据](https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword=jackson)  |
| Gson     | 4        | [CVE 数据](https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword=gson)     |
| Fastjson | 5        | [CVE 数据](https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword=fastjson) |

> **注意**：Fastjson 曾因响应不及时备受质疑，尤其是 Fastjson 1.x
> 漏洞频发，参考 [漏洞分析](https://www.javasec.org/java-vuls/FastJson.html)。

**性能表现：Benchmark**

以下数据摘自 [FastJson Benchmark](https://github.com/alibaba/fastjson2/wiki/fastjson_benchmark)，测试覆盖反序列化与序列化多个场景：

| 测试场景             | FastJSON 2.x | FastJSON 1.x  | Jackson       | Gson          | 备注                                  |
|------------------|--------------|---------------|---------------|---------------|-------------------------------------|
| **反序列化：字符串**     | **100%**     | 54.2%-92.2%   | 25.64%-52.9%  | 25.66%-36.63% | Fastjson 2.x 性能领先，Jackson/Gson 性能较低 |
| **序列化：字符串**      | **100%**     | 35.94%-55.5%  | 46.41%-63.11% | 16.71%-29.33% | Fastjson 2.x 性能最优，Gson 性能明显落后       |
| **解析为树结构对象**     | **100%**     | 44.7%-62.32%  | 43.27%-52.9%  | 34.02%-44.02% | Fastjson 2.x 在复杂场景中继续保持显著性能优势       |
| **解析 UTF-8 字节流** | **100%**     | 30.26%-92.71% | 27.23%-59.03% | 17.2%-48.55%  | Fastjson 2.x 适用于处理大量 JSON 数据的应用     |

> **总结**：
> - **Fastjson 2.x** 的性能在所有框架中表现最优，反序列化和序列化性能普遍为其他框架的 **2~4 倍**。
> - **Jackson** 和 **Gson** 在性能上处于中等水平，适合通用低至中等性能需求的场景。

#### 深度分析与选择建议

**4.1 谁适合生产环境？**

- **优先选择**：Fastjson 2.x（高性能） 或 Jackson（高安全性）
- **权衡考虑**：Gson（轻量级，适合简单任务）
- **不建议选择**：Fastjson 1.x

**4.2 选择依据总结**

1. 若关注 **性能**，优选 Fastjson 2.x。
2. 若关注 **安全性** 和社区支持，优选 Jackson。
3. 若对性能要求较低，优选 Gson 或 org.json（适合小型应用）。
