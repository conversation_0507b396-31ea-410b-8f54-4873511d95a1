package cn.com.chinastock.cnf.webclient;

import java.lang.annotation.*;

/**
 * WebClientExchange 注解用于标记WebClient客户端接口。
 * 该注解标记的接口会被自动扫描并创建相应的WebClient代理实例，
 * 支持声明式的HTTP客户端调用。
 *
 * <AUTHOR>
 * @see EnableWebClients
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface WebClientExchange {

    /**
     * 客户端的名称，用于从Spring容器中查找一个命名的 WebClient.Builder
     * 或者用于负载均衡的服务ID
     * 
     * @return 客户端名称
     */
    String name();

    /**
     * 服务的URL，可以包含占位符
     * 如果未指定，将使用 "http://" + name 作为默认URL
     * 
     * @return 服务URL
     */
    String url() default "";

    /**
     * 用于存放路径前缀
     * 会拼接到URL后面形成完整的基础路径
     * 
     * @return 路径前缀
     */
    String path() default "";
}
