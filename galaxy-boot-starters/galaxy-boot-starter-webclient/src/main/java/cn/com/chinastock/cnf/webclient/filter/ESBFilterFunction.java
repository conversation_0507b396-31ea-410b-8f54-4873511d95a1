package cn.com.chinastock.cnf.webclient.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webclient.esb.ESBAuthentication;
import cn.com.chinastock.cnf.webclient.esb.ESBProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * ESBFilterFunction 类是WebClient的ESB认证过滤器。
 * 该过滤器负责在WebClient发起HTTP请求时，自动检测是否为ESB请求，
 * 并为ESB请求添加必要的认证头信息。
 *
 * <AUTHOR>
 * @see ExchangeFilterFunction
 * @see ESBAuthentication
 * @see ESBProperties
 */
public class ESBFilterFunction implements ExchangeFilterFunction {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private final ESBProperties esbProperties;

    /**
     * 构造函数，初始化ESB认证过滤器
     * 
     * @param esbProperties ESB配置属性，包含用户名、密码等认证信息
     */
    public ESBFilterFunction(ESBProperties esbProperties) {
        this.esbProperties = esbProperties;
    }

    /**
     * 执行ESB认证过滤逻辑
     * 
     * <p>该方法的处理流程：</p>
     * <ol>
     *     <li>检测当前请求是否为ESB请求（通过Function-No请求头判断）</li>
     *     <li>如果不是ESB请求，直接执行下一个过滤器</li>
     *     <li>如果是ESB请求，生成ESB认证头信息</li>
     *     <li>将认证头信息添加到HTTP请求中（避免重复设置）</li>
     *     <li>记录调试日志并执行下一个过滤器</li>
     * </ol>
     * 
     * @param request 客户端请求对象
     * @param next 下一个交换函数
     * @return 包含响应的Mono对象
     */
    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {

        ClientRequest newRequest = ClientRequest.from(request)
                .headers(httpHeaders ->  {
                    if (!ESBAuthentication.isESBRequest(httpHeaders)) {
                        return;
                    }
                    Map<String, String> esbHeaders = ESBAuthentication.generateHeaders(esbProperties);
                    logger.debug(LogCategory.FRAMEWORK_LOG, "ESBFilterFunction::setESBHeaders: {}", esbHeaders.size());

                    esbHeaders.forEach((key, value) -> trySetHeader(httpHeaders, key, value));
                }).build();
        return next.exchange(newRequest);
    }

    /**
     * 尝试设置HTTP头信息
     * 
     * <p>该方法会检查指定的头信息是否已存在，如果不存在或为空，则添加新的头信息。
     * 这样可以避免重复设置已存在的认证头，保持请求的完整性。</p>
     * 
     * @param httpHeaders HTTP头信息对象
     * @param headerName 头信息名称
     * @param value 头信息值
     */
    private void trySetHeader(HttpHeaders httpHeaders, String headerName, String value) {
        if (!httpHeaders.containsKey(headerName) || CollectionUtils.isEmpty(httpHeaders.get(headerName))) {
            httpHeaders.add(headerName, value);
        }
    }
}
