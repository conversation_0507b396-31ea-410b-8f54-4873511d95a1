package cn.com.chinastock.cnf.webclient;

import org.springframework.context.annotation.Import;
import java.lang.annotation.*;

/**
 * EnableWebClients 注解用于启用自定义的WebFlux客户端扫描功能。
 *
 * <AUTHOR>
 * @see WebClientExchange
 * @see WebClientsRegistrar
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(WebClientsRegistrar.class) // 导入核心注册器
public @interface EnableWebClients {

    /**
     * 指定要扫描的包路径
     * 如果为空，则默认扫描此注解所在类的包及其子包
     * 
     * @return 要扫描的包路径数组
     */
    String[] basePackages() default {};
}