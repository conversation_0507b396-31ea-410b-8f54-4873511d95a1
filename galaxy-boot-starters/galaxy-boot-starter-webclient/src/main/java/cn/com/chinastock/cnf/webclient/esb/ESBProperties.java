package cn.com.chinastock.cnf.webclient.esb;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "galaxy.webclient.esb")
public class ESBProperties {

    @Value("${galaxy.system.code:}")
    private String systemCode = "";

    @Value("${galaxy.webclient.esb.user:}")
    private String user;

    @Value("${galaxy.webclient.esb.password:}")
    private String password;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSystemCode() {
        return systemCode;
    }
}
