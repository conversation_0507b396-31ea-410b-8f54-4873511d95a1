package cn.com.chinastock.cnf.webclient.exception;

import cn.com.chinastock.cnf.core.http.Meta;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * GalaxyWebClientException 类是一个自定义的 WebClient 异常类，用于处理 WebClient 调用时发生的异常。
 * 该类扩展了 WebClientResponseException，并添加了自定义的 Meta 信息和错误码。
 * 在 WebClient 调用异常时，如果响应的 Body 中带有 Meta 信息，就会抛出该异常，并将 Meta 信息传递给前端。
 *
 * <AUTHOR>
 */
public class GalaxyWebClientException extends WebClientResponseException {

    private Meta meta;

    private String code;

    /**
     * 构造函数，用于初始化异常信息
     * 
     * @param statusCode HTTP状态码
     * @param reasonPhrase 状态码描述
     * @param headers 响应头信息
     * @param responseBody 响应体内容
     * @param request 原始请求信息
     * @param meta 自定义的Meta信息
     */
    public GalaxyWebClientException(HttpStatusCode statusCode, String reasonPhrase, HttpHeaders headers,
                                    byte[] responseBody, HttpRequest request, Meta meta) {
        super(statusCode, reasonPhrase, headers, responseBody, StandardCharsets.UTF_8, request);
        this.meta = meta;
        this.code = Optional.of(meta).map(Meta::getCode).orElse("");
    }

    /**
     * 获取自定义的Meta信息
     * 
     * @return Meta信息对象
     */
    public Meta getMeta() {
        return meta;
    }

    /**
     * 设置自定义的Meta信息
     * 
     * @param meta Meta信息对象
     */
    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    /**
     * 获取错误码
     * 
     * @return 错误码字符串
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置错误码
     * 
     * @param code 错误码字符串
     */
    public void setCode(String code) {
        this.code = code;
    }
}
