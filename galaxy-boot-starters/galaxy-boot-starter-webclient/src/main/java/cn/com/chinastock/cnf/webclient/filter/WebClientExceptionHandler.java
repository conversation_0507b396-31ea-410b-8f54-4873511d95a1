package cn.com.chinastock.cnf.webclient.filter;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webclient.exception.GalaxyWebClientException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.function.Function;

/**
 * WebClientExceptionHandler 类是WebClient的异常处理器。
 * 该处理器负责处理WebClient调用过程中产生的HTTP错误响应，
 * 智能解析响应体中的业务异常信息，并转换为自定义的异常类型。
 *
 * <p>异常处理流程：</p>
 * <ol>
 *     <li>接收HTTP错误响应</li>
 *     <li>创建基础的WebClientResponseException</li>
 *     <li>尝试解析响应体中的JSON数据</li>
 *     <li>如果包含有效的BaseResponse和Meta信息，创建GalaxyWebClientException</li>
 *     <li>如果解析失败，返回原始异常</li>
 * </ol>
 *
 * <AUTHOR>
 * @see Function
 * @see ClientResponse
 * @see GalaxyWebClientException
 * @see BaseResponse
 */
public class WebClientExceptionHandler implements Function<ClientResponse, Mono<? extends Throwable>> {

    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    /**
     * 处理客户端响应异常
     * 
     * @param response 客户端错误响应对象
     * @return 包含异常信息的Mono对象
     */
    @Override
    public Mono<? extends Throwable> apply(ClientResponse response) {
        return response.createException().flatMap(exception -> {
            String bodyAsString = exception.getResponseBodyAsString();
            if (Objects.isNull(exception)) {
                return Mono.error(exception);
            }
            if (!JSON.isValid(bodyAsString)) {
                return Mono.error(exception);
            }
            try {
                BaseResponse<?> responseDTO = JSON.parseObject(bodyAsString, BaseResponse.class);
                if (responseDTO == null || responseDTO.getMeta() == null) {
                    return Mono.error(exception);
                }

                logger.debug(LogCategory.EXCEPTION_LOG, "WebClient 调用异常: {} {}", responseDTO.getMeta(), response.request().getURI());

                return Mono.error(new GalaxyWebClientException(
                        exception.getStatusCode(),
                        exception.getStatusText(),
                        exception.getHeaders(),
                        exception.getResponseBodyAsByteArray(),
                        exception.getRequest(),
                        responseDTO.getMeta())
                );
            } catch (JSONException ignore) {}

            return Mono.error(exception);
        });
    }
}
