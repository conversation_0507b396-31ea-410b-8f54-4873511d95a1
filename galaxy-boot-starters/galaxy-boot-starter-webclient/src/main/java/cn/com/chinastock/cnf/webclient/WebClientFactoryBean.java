package cn.com.chinastock.cnf.webclient;

import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.util.Optional;

/**
 * WebClientFactoryBean 类负责创建客户端代理实例的工厂Bean。
 *
 * <AUTHOR>
 */
public class WebClientFactoryBean implements FactoryBean<Object>, ApplicationContextAware {

    private Class<?> clientInterface;
    private String name;
    private String url;
    private String path;
    private ApplicationContext applicationContext;

    /**
     * 创建并返回客户端代理实例
     * 
     * @return 基于WebClient的HTTP服务代理对象
     */
    @Override
    public Object getObject() {
        WebClient webClient = buildWebClient();
        // 使用最新的Spring 6.1 API来创建代理工厂
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builder()
                .exchangeAdapter(WebClientAdapter.create(webClient))
                .build();
        return factory.createClient(this.clientInterface);
    }

    /**
     * 构建配置完成的WebClient实例
     * 
     * @return 配置完成的WebClient实例
     */
    private WebClient buildWebClient() {
        // 从Spring容器中获取用户配置的全局WebClient.Builder，而不是自己创建。
        // 这样可以继承所有全局配置，如LoadBalancer, defaultHeaders等。
        WebClient.Builder builder = this.applicationContext.getBean(WebClient.Builder.class);
        Assert.notNull(builder, "A WebClient.Builder bean could not be found. Please define one.");

        WebClient.Builder cloneBuilder = builder.clone();

        Environment environment = this.applicationContext.getEnvironment();

        // 安全地解析URL和Path中的占位符
        String resolvedUrl = Optional.ofNullable(environment.resolvePlaceholders(this.url))
                .filter(StringUtils::hasText)
                .orElse("http://" + this.name);

        String resolvedPath = environment.resolvePlaceholders(this.path);

        // 拼接最终的Base URL
        StringBuilder finalBaseUrl = new StringBuilder(resolvedUrl);
        if (StringUtils.hasText(resolvedPath)) {
            if (finalBaseUrl.toString().endsWith("/")) {
                finalBaseUrl.deleteCharAt(finalBaseUrl.length() - 1);
            }
            if (!resolvedPath.startsWith("/")) {
                finalBaseUrl.append("/");
            }
            finalBaseUrl.append(resolvedPath);
        }

        return cloneBuilder.baseUrl(finalBaseUrl.toString()).build();
    }

    /**
     * 返回工厂Bean创建的对象类型
     * 
     * @return 客户端接口的Class类型
     */
    @Override
    public Class<?> getObjectType() {
        return this.clientInterface;
    }

    /**
     * 设置Spring应用上下文
     * 
     * @param applicationContext Spring应用上下文
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    // --- Setters, used by the BeanDefinitionBuilder ---
    
    /**
     * 设置客户端接口类
     * 
     * @param clientInterface 客户端接口的Class对象
     */
    public void setClientInterface(Class<?> clientInterface) {
        this.clientInterface = clientInterface;
    }

    /**
     * 设置客户端名称
     * 
     * @param name 客户端名称，用于服务发现或负载均衡
     */
    public void setName(String name) { 
        this.name = name; 
    }
    
    /**
     * 设置客户端URL
     * 
     * @param url 客户端的基础URL，可以包含占位符
     */
    public void setUrl(String url) { 
        this.url = url; 
    }
    
    /**
     * 设置客户端路径前缀
     * 
     * @param path 路径前缀，会拼接到URL后面
     */
    public void setPath(String path) { 
        this.path = path; 
    }
}