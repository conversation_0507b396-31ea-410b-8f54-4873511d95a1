package cn.com.chinastock.cnf.webclient.config;

import cn.com.chinastock.cnf.webclient.esb.ESBProperties;
import cn.com.chinastock.cnf.webclient.filter.ESBFilterFunction;
import cn.com.chinastock.cnf.webclient.filter.TraceFilterFunction;
import cn.com.chinastock.cnf.webclient.filter.WebClientExceptionHandler;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
@EnableConfigurationProperties(ESBProperties.class)
public class WebClientFilterConfig {

    @Bean
    @LoadBalanced
    public WebClient.Builder webClientBuilder(ESBProperties esbProperties) {
        WebClientExceptionHandler exceptionHandler = new WebClientExceptionHandler();

        return WebClient.builder()
                .filter(new TraceFilterFunction())
                .filter(new ESBFilterFunction(esbProperties))
                .defaultStatusHandler(httpStatus -> httpStatus.isError(), exceptionHandler);
    }

}