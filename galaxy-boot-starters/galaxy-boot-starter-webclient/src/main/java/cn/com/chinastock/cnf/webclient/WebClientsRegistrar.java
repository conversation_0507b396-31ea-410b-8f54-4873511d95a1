package cn.com.chinastock.cnf.webclient;

import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.Set;

/**
 * WebClientsRegistrar 类是WebClient客户端接口的注册器。
 * 该类实现了ImportBeanDefinitionRegistrar接口，负责扫描和注册
 * 标记了@WebClientExchange注解的客户端接口，并为每个接口创建相应的代理Bean。
 *
 * <p>该注册器的主要功能包括：</p>
 * <ul>
 *     <li>扫描指定包路径下的@WebClientExchange注解接口</li>
 *     <li>解析注解中的配置信息（name、url、path等）</li>
 *     <li>为每个客户端接口创建WebClientFactoryBean</li>
 *     <li>将FactoryBean注册到Spring容器中</li>
 *     <li>支持自定义扫描包路径配置</li>
 * </ul>
 *
 * <AUTHOR>
 * @see ImportBeanDefinitionRegistrar
 * @see EnableWebClients
 * @see WebClientExchange
 * @see WebClientFactoryBean
 */
public class WebClientsRegistrar implements ImportBeanDefinitionRegistrar {

    /**
     * 注册Bean定义到Spring容器
     * 
     * @param metadata 导入类的注解元数据（通常是@EnableWebClients注解所在的类）
     * @param registry Bean定义注册器，用于注册新的Bean定义
     */
    @Override
    public void registerBeanDefinitions(AnnotationMetadata metadata, BeanDefinitionRegistry registry) {
        ClassPathScanningCandidateComponentProvider scanner = createScanner();
        String[] basePackages = getBasePackages(metadata);

        for (String basePackage : basePackages) {
            Set<BeanDefinition> candidateComponents = scanner.findCandidateComponents(basePackage);
            for (BeanDefinition candidate : candidateComponents) {
                if (candidate instanceof AnnotatedBeanDefinition) {
                    AnnotatedBeanDefinition beanDef = (AnnotatedBeanDefinition) candidate;
                    AnnotationMetadata annotationMetadata = beanDef.getMetadata();
                    Map<String, Object> clientAttrs = annotationMetadata.getAnnotationAttributes(WebClientExchange.class.getName());

                    Assert.notNull(clientAttrs, "Attributes for @WebClientExchange must not be null");

                    String className = annotationMetadata.getClassName();
                    Class<?> clientInterface = ClassUtils.resolveClassName(className, null);
                    String clientName = (String) clientAttrs.get("name");

                    // 注册我们的FactoryBean，而不是直接注册代理对象
                    BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(WebClientFactoryBean.class);
                    builder.addPropertyValue("clientInterface", clientInterface);
                    builder.addPropertyValue("url", clientAttrs.get("url"));
                    builder.addPropertyValue("path", clientAttrs.get("path"));
                    builder.addPropertyValue("name", clientName);
                    builder.setAutowireMode(org.springframework.beans.factory.support.AbstractBeanDefinition.AUTOWIRE_BY_TYPE);

                    registry.registerBeanDefinition(clientName, builder.getBeanDefinition());
                }
            }
        }
    }

    /**
     * 创建类路径扫描候选组件提供器
     * 
     * @return 配置完成的类路径扫描候选组件提供器
     */
    private ClassPathScanningCandidateComponentProvider createScanner() {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false) {
            @Override
            protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
                return beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
            }
        };
        scanner.addIncludeFilter(new AnnotationTypeFilter(WebClientExchange.class));
        return scanner;
    }

    /**
     * 获取要扫描的基础包路径
     * 
     * @param metadata 注解元数据，包含@EnableWebClients注解信息
     * @return 要扫描的基础包路径数组
     * @throws IllegalArgumentException 如果@EnableWebClients注解不存在
     */
    private String[] getBasePackages(AnnotationMetadata metadata) {
        Map<String, Object> attrs = metadata.getAnnotationAttributes(EnableWebClients.class.getName());
        Assert.notNull(attrs, "@EnableWebClients is not present.");
        String[] basePackages = (String[]) attrs.get("basePackages");
        if (ObjectUtils.isEmpty(basePackages)) {
            return new String[]{ClassUtils.getPackageName(metadata.getClassName())};
        }
        return basePackages;
    }
}