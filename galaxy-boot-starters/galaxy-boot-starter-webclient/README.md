## Galaxy Boot Starter WebClient（未发布）

> Galaxy Boot Starter WebClient 是一个基于 Spring WebFlux 的响应式 HTTP 客户端，用于调用第三方HTTP服务，支持 Trace Context 传递和 ESB 认证。

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter WebClient`，只需要引入对应 `starter` 即可：使用 group ID 为 `cn.com.chinastock` 和 `artifact ID` 为
`galaxy-boot-starter-webclient` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-webclient</artifactId>
</dependency>
``` 
