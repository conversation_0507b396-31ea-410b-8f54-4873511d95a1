package cn.com.chinastock.cnf.webflux.log.config;

import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;

import java.util.Optional;

/**
 * LogPropertiesWrapper 类是WebFlux环境下的日志配置属性包装器。
 * 该类封装了日志配置属性，提供了安全的配置访问方法，避免空指针异常。
 *
 *  <AUTHOR>
 */
public class LogPropertiesWrapper {
    private LogProperties logProperties;

    /**
     * 设置日志配置属性
     * 
     * @param logProperties 日志配置属性对象
     */
    public void setLogProperties(LogProperties logProperties) {
        this.logProperties = logProperties;
    }

    /**
     * 获取日志最大长度配置
     * 如果配置为空，则返回默认值
     * 
     * @return 日志最大长度
     */
    public int getMaxLogLength() {
        return Optional.ofNullable(logProperties)
                .map(LogProperties::getMaxLength)
                .orElse(LogProperties.DEFAULT_MAX_LENGTH);
    }

    /**
     * 获取默认日志类别配置
     * 如果配置为空，则返回框架日志类别
     * 
     * @return 默认日志类别
     */
    public LogCategory getDefaultCategory() {
        return Optional.ofNullable(logProperties)
                .map(LogProperties::getDefaultCategory)
                .orElse(LogCategory.FRAMEWORK_LOG);
    }
}
