package cn.com.chinastock.cnf.webflux.log;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.IGalaxyLoggerProvider;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.webflux.log.config.LogPropertiesWrapper;
import org.slf4j.LoggerFactory;

/**
 * GalaxyWebfluxLoggerFactory 类是WebFlux环境下的Galaxy日志工厂类。
 * 该类实现了IGalaxyLoggerProvider接口，负责创建适用于WebFlux环境的日志记录器实例。
 *
 * <AUTHOR>
 */
public class GalaxyWebfluxLoggerFactory implements IGalaxyLoggerProvider {

    private static final LogPropertiesWrapper propertiesWrapper = new LogPropertiesWrapper();

    /**
     * 设置日志配置属性
     * 
     * @param logProperties 日志配置属性对象
     */
    public static void setLogProperties(LogProperties logProperties) {
        propertiesWrapper.setLogProperties(logProperties);
    }

    /**
     * 根据名称获取日志记录器
     * 
     * @param name 日志记录器名称
     * @return WebFlux环境下的Galaxy日志记录器实例
     */
    @Override
    public IGalaxyLogger getLogger(String name) {
        return new GalaxyWebfluxLogger(propertiesWrapper, LoggerFactory.getLogger(name));
    }

    /**
     * 根据类获取日志记录器
     * 
     * @param clazz 类对象
     * @return WebFlux环境下的Galaxy日志记录器实例
     */
    @Override
    public IGalaxyLogger getLogger(Class<?> clazz) {
        return new GalaxyWebfluxLogger(propertiesWrapper, LoggerFactory.getLogger(clazz));
    }


}
