package cn.com.chinastock.cnf.webflux.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import reactor.core.publisher.Flux;

import java.util.Objects;

/**
 * FluxRequestCache 类用于在WebFlux环境下缓存HTTP请求数据。
 * 该类通过装饰器模式包装ServerHttpRequest，在请求体数据流过时进行缓存，
 * 以便后续的日志记录操作能够访问到请求体内容。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>根据Content-Type智能判断是否需要缓存请求体</li>
 *     <li>使用装饰器模式非阻塞地缓存请求体数据</li>
 *     <li>支持流式数据处理，避免内存溢出</li>
 *     <li>提供日志记录功能，支持请求头和请求体的记录</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * {@code
 * FluxRequestCache requestCache = new FluxRequestCache(request);
 * ServerHttpRequestDecorator decorator = requestCache.requestDecorator();
 * // 在请求处理完成后记录日志
 * requestCache.log(true);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @see DataBufferCache
 * @see RequestLogger
 * @see ServerHttpRequestDecorator
 */
public class FluxRequestCache {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private final ServerHttpRequest request;
    private final DataBufferCache dataBufferCache;

    /**
     * 构造函数，初始化请求缓存对象
     * 
     * @param request HTTP请求对象，不能为null
     */
    public FluxRequestCache(ServerHttpRequest request) {
        this.request = request;
        this.dataBufferCache = DataBufferCache.createNullableBy(request.getHeaders().getContentType());
    }

    /**
     * 创建请求装饰器，用于拦截和缓存请求体数据
     * 
     * <p>该方法根据Content-Type判断是否需要缓存请求体：</p>
     * <ul>
     *     <li>如果Content-Type不支持缓存，返回原始请求装饰器</li>
     *     <li>如果Content-Type支持缓存，返回能够缓存请求体的装饰器</li>
     * </ul>
     * 
     * @return ServerHttpRequestDecorator 请求装饰器对象
     */
    public ServerHttpRequestDecorator requestDecorator() {
        if (Objects.isNull(dataBufferCache)) {
            return new ServerHttpRequestDecorator(request);
        }

        return new ServerHttpRequestDecorator(request) {
            @Override
            public Flux<DataBuffer> getBody() {
                return super.getBody().doOnNext(buffer -> {
                    dataBufferCache.cache(buffer);
                });
            }
        };
    }

    /**
     * 记录请求日志
     * 
     * <p>该方法根据是否有缓存的请求体数据来决定日志记录方式：</p>
     * <ul>
     *     <li>如果有缓存数据，记录包含请求体的完整日志</li>
     *     <li>如果没有缓存数据，仅记录请求基本信息</li>
     * </ul>
     * 
     * @param requestHeadersEnabled 是否启用请求头记录
     */
    public void log(boolean requestHeadersEnabled) {
        if (Objects.nonNull(dataBufferCache)) {
            RequestLogger.log(request, requestHeadersEnabled, dataBufferCache.outputToString());
        } else {
            RequestLogger.log(request, requestHeadersEnabled);
        }
    }

}
