package cn.com.chinastock.cnf.webflux.log;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webflux.log.config.LogPropertiesWrapper;
import org.slf4j.Logger;

/**
 * GalaxyBoot Webflux日志组件遵循 Galaxy日志规范，提供了统一的、规范化的日志功能。
 *
 * <p>该类中包含以下方法：</p>
 * <ul>
 *     <li>{@link #info(String, Object...)}：记录信息级别的日志。</li>
 *     <li>{@link #error(String, Throwable)}：记录错误日志，包含异常对象。</li>
 *     <li>{@link #error(String, Object...)}：记录错误日志，支持格式化参数。</li>
 *     <li>{@link #warn(String, Object...)}：记录警告日志。</li>
 *     <li>{@link #debug(String, Object...)}：打印调试信息。</li>
 *     <li>{@link #info(LogCategory, String, Object...)}：记录指定类别的INFO级别日志。</li>
 *     <li>{@link #error(LogCategory, String, Throwable)}：记录指定类别的ERROR级别日志，包含异常对象。</li>
 *     <li>{@link #error(LogCategory, String, Object...)}：记录指定类别的ERROR级别日志，支持格式化参数。</li>
 *     <li>{@link #warn(LogCategory, String, Object...)}：记录指定类别的WARN级别日志。</li>
 *     <li>{@link #debug(LogCategory, String, Object...)}：记录指定类别的DEBUG级别日志。</li>
 * </ul>
 *
 * <AUTHOR>
 * @see LogCategory
 */
public class GalaxyWebfluxLogger implements IGalaxyLogger {

    private final LogPropertiesWrapper logProperties;
    private final Logger logger;
    
    public GalaxyWebfluxLogger(LogPropertiesWrapper logProperties, Logger logger) {
        this.logProperties = logProperties;
        this.logger = logger;
    }

    /**
     * 记录信息级别的日志。
     *
     * <pre>
     *    {@code
     *        logger.info("User logged in: {}", user.getName());
     *        // 输出: User logged in: 张三
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数，用于替换占位符
     */
    @Override
    public void info(String message, Object... args) {
        setLogCategory(logProperties.getDefaultCategory());
        logger.info(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
    }

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *      logger.error("An error occurred", new RuntimeException("error"));
     *      // 输出异常堆栈
     *    }
     * </pre>
     *
     * @param message 错误信息
     * @param t       异常对象
     */
    @Override
    public void error(String message, Throwable t) {
        setLogCategory(logProperties.getDefaultCategory());
        logger.error(escapeAndTruncateMessage(message, null, logProperties.getMaxLogLength()), t);
    }

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *        logger.error("An error occurred: %s", "Invalid input");
     *        // 输出: "An error occurred: Invalid input"
     *    }
     * </pre>
     *
     * @param message 错误信息模板，支持格式化
     * @param args    格式化参数
     */
    @Override
    public void error(String message, Object... args) {
        setLogCategory(logProperties.getDefaultCategory());
        logger.error(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
    }

    /**
     * 记录警告日志
     *
     * <pre>
     *    {@code
     *        logger.warn("User login failed: {}", username);
     *        // 输出: User login failed: admin
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数
     */
    @Override
    public void warn(String message, Object... args) {
        setLogCategory(logProperties.getDefaultCategory());
        logger.warn(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
    }

    /**
     * 打印调试信息
     *
     * <pre>
     *    {@code
     *        logger.debug("User logged in: %s", user.getName());
     *        // 输出: "User logged in: 张三"
     *    }
     * </pre>
     *
     * @param message 调试信息模板，支持格式化字符串
     * @param args    格式化字符串的参数
     */
    @Override
    public void debug(String message, Object... args) {
        setLogCategory(logProperties.getDefaultCategory());
        logger.debug(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
    }

    /**
     * 记录INFO级别日志
     * <pre>
     *     {@code
     *     logger.info(LogCategory.USER_ACTION, "User {} logged in at {}", userId, timestamp);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void info(LogCategory logCategory, String message, Object... args) {
        setLogCategory(logCategory);
        logger.info(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
        setLogCategory(logProperties.getDefaultCategory());
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     logger.error(LogCategory.EXCEPTION_LOG, "An error occurred", new RuntimeException("error"));
     *     // 输出异常堆栈
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param t           异常
     */
    @Override
    public void error(LogCategory logCategory, String message, Throwable t) {
        setLogCategory(logCategory);
        logger.error(escapeAndTruncateMessage(message, null, logProperties.getMaxLogLength()), t);
        setLogCategory(logProperties.getDefaultCategory());
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     logger.error(LogCategory.BUSINESS_LOG, "An error occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void error(LogCategory logCategory, String message, Object... args) {
        setLogCategory(logCategory);
        logger.error(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
        setLogCategory(logProperties.getDefaultCategory());
    }

    /**
     * 记录WARN级别日志
     * <pre>
     *     {@code
     *     logger.warn(LogCategory.BUSINESS_LOG, "An warning occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void warn(LogCategory logCategory, String message, Object... args) {
        setLogCategory(logCategory);
        logger.warn(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
        setLogCategory(logProperties.getDefaultCategory());
    }

    /**
     * 记录DEBUG级别日志
     * <pre>
     *     {@code
     *     logger.debug(LogCategory.BUSINESS_LOG, "An debug message, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void debug(LogCategory logCategory, String message, Object... args) {
        setLogCategory(logCategory);
        logger.debug(escapeAndTruncateMessage(message, args, logProperties.getMaxLogLength()));
        setLogCategory(logProperties.getDefaultCategory());
    }

}