package cn.com.chinastock.cnf.webflux.log.logger;

import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.channels.Channels;
import java.nio.channels.WritableByteChannel;
import java.nio.charset.StandardCharsets;

/**
 * DataBufferCache 类用于在WebFlux环境下缓存DataBuffer数据。
 * 该类提供了高效的数据缓存机制，支持流式数据的缓存和字符串输出。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>根据MediaType智能判断是否需要缓存数据</li>
 *     <li>使用NIO通道高效地缓存DataBuffer数据</li>
 *     <li>支持多种Content-Type的缓存（JSON、XML、TEXT等）</li>
 *     <li>提供UTF-8编码的字符串输出功能</li>
 *     <li>内存友好的缓存机制，避免大文件缓存</li>
 * </ul>
 *
 * <AUTHOR>
 * @see DataBuffer
 * @see MediaType
 */
public class DataBufferCache {
    private final ByteArrayOutputStream byteCache = new ByteArrayOutputStream();

    /**
     * 根据Content-Type创建DataBufferCache实例
     * 
     * <p>该方法根据MediaType判断是否需要创建缓存实例：</p>
     * <ul>
     *     <li>如果Content-Type支持缓存，返回DataBufferCache实例</li>
     *     <li>如果Content-Type不支持缓存，返回null</li>
     * </ul>
     * 
     * @param contentType HTTP内容类型
     * @return DataBufferCache实例，如果不支持缓存则返回null
     */
    public static DataBufferCache createNullableBy(MediaType contentType) {
        if (cacheableContentType(contentType)) {
            return new DataBufferCache();
        }
        return null;
    }

    /**
     * 缓存DataBuffer数据
     * 
     * <p>该方法使用NIO通道高效地将DataBuffer中的数据写入到字节数组输出流中。
     * 支持多个ByteBuffer的数据合并缓存。</p>
     * 
     * @param buffer 需要缓存的DataBuffer对象
     * @throws RuntimeException 如果缓存过程中发生IO异常
     */
    public void cache(DataBuffer buffer) {
        try (WritableByteChannel channel = Channels.newChannel(byteCache)) {
            buffer.readableByteBuffers().forEachRemaining(byteBuffer -> {
                try {
                    channel.write(byteBuffer);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将缓存的数据转换为UTF-8编码的字符串
     * 
     * @return 缓存数据的字符串表示，使用UTF-8编码
     */
    public String outputToString() {
        return byteCache.toString(StandardCharsets.UTF_8);
    }

    /**
     * 判断指定的Content-Type是否支持缓存
     * 
     * <p>支持缓存的Content-Type包括：</p>
     * <ul>
     *     <li>application/json</li>
     *     <li>application/xml</li>
     *     <li>text/plain</li>
     *     <li>text/xml</li>
     *     <li>application/*+json 变体（如application/problem+json）</li>
     *     <li>application/*+xml 变体</li>
     *     <li>null值（默认支持缓存）</li>
     * </ul>
     * 
     * @param contentType HTTP内容类型
     * @return 如果支持缓存返回true，否则返回false
     */
    public static boolean cacheableContentType(MediaType contentType) {
        if (contentType == null) {
            return true;
        }
        return contentType.isCompatibleWith(MediaType.APPLICATION_JSON) ||
                contentType.isCompatibleWith(MediaType.APPLICATION_XML) ||
                contentType.isCompatibleWith(MediaType.TEXT_PLAIN) ||
                contentType.isCompatibleWith(MediaType.TEXT_XML) ||
                // 支持 application/problem+json 等变体
                contentType.isCompatibleWith(new MediaType("application", "*+json")) ||
                contentType.isCompatibleWith(new MediaType("application", "*+xml"));
    }
}
