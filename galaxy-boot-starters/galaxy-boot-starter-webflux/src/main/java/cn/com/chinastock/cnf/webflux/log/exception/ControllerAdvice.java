package cn.com.chinastock.cnf.webflux.log.exception;

import cn.com.chinastock.cnf.core.exception.BusinessException;
import cn.com.chinastock.cnf.core.exception.ForbiddenException;
import cn.com.chinastock.cnf.core.exception.ServerErrorException;
import cn.com.chinastock.cnf.core.exception.UnauthorizedException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.net.MalformedURLException;
import java.security.GeneralSecurityException;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.stream.Collectors;

import static org.springframework.http.HttpStatus.*;

/**
 * ControllerAdvice 类用于WebFlux环境下全局处理控制器中的异常。
 * 该类提供了统一的异常处理机制，将各种异常转换为标准的响应格式。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>处理业务异常，返回业务错误信息</li>
 *     <li>处理认证和授权异常</li>
 *     <li>处理服务器内部异常</li>
 *     <li>处理参数验证异常</li>
 *     <li>统一异常日志记录</li>
 *     <li>返回响应式的Mono类型结果</li>
 * </ul>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #ControllerAdvice(LogProperties)}：构造函数，初始化系统代码。</li>
 *     <li>{@link #handleBusinessException(BusinessException)}：处理业务异常，返回HttpStatusCode=200。</li>
 *     <li>{@link #handleUnauthorizedException(UnauthorizedException)}：处理未认证异常，返回HttpStatusCode=401。</li>
 *     <li>{@link #handleForbiddenException(ForbiddenException)}：处理禁止访问异常，返回HttpStatusCode=403。</li>
 *     <li>{@link #handleServerErrorException(ServerErrorException)}：处理服务器异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleRuntimeException(RuntimeException)}：处理运行时异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleException(Exception)}：处理多种常见异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleValidationException(MethodArgumentNotValidException)}：处理参数验证异常，返回HttpStatusCode=400。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class ControllerAdvice {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private final String systemCode;

    /**
     * 构造函数，初始化系统代码
     * 
     * @param logProperties 日志配置属性，用于获取系统代码
     */
    public ControllerAdvice(LogProperties logProperties) {
        this.systemCode = logProperties.getSystemCode();
    }

    /**
     * 生成默认错误代码
     * 
     * @param code HTTP状态码
     * @return 格式化的错误代码
     */
    private String generateDefaultCode(Integer code) {
        return systemCode + "TCNF" + code.toString();
    }

    /**
     * 生成日志消息
     * 
     * @param code 错误代码
     * @param message 错误消息
     * @return 格式化的日志消息
     */
    private String generateLogMessage(String code, String message) {
        return "[" + code + "]" + message;
    }

    /**
     * 记录异常日志
     * 
     * @param code 错误代码
     * @param message 错误消息
     * @param exception 异常对象，可为null
     */
    private void logException(String code, String message, Exception exception) {
        if (exception == null) {
            logger.error(LogCategory.EXCEPTION_LOG, generateLogMessage(code, message));
        } else {
            logger.error(LogCategory.EXCEPTION_LOG, generateLogMessage(code, message), exception);
        }
    }

    /**
     * 处理业务异常，返回HttpStatusCode=200
     * 
     * @param e 业务异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(OK)
    public Mono<BaseResponse<Object>> handleBusinessException(BusinessException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(OK.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "业务异常" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理未认证异常，返回HttpStatusCode=401
     * 
     * @param e 未认证异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(UNAUTHORIZED)
    public Mono<BaseResponse<Object>> handleUnauthorizedException(UnauthorizedException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(UNAUTHORIZED.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "认证失败" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理禁止访问异常，返回HttpStatusCode=403
     * 
     * @param e 禁止访问异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(FORBIDDEN)
    public Mono<BaseResponse<Object>> handleForbiddenException(ForbiddenException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(FORBIDDEN.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "权限不足" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理服务器异常，返回HttpStatusCode=500
     * 
     * @param e 服务器异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(ServerErrorException.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public Mono<BaseResponse<Object>> handleServerErrorException(ServerErrorException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(INTERNAL_SERVER_ERROR.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "服务器异常" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理运行时异常，返回HttpStatusCode=500
     * 
     * @param e 运行时异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public Mono<BaseResponse<Object>> handleRuntimeException(RuntimeException e) {
        String code = generateDefaultCode(INTERNAL_SERVER_ERROR.value());
        String errorMessage = "未知运行时异常";
        logger.error(LogCategory.EXCEPTION_LOG, errorMessage + ": " + e.getMessage(), e);
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理多种常见异常，返回HttpStatusCode=500
     * 
     * @param e 异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler({
            IOException.class,
            SQLException.class,
            ClassNotFoundException.class,
            NoSuchMethodException.class,
            NoSuchFieldException.class,
            InstantiationException.class,
            InterruptedException.class,
            ParseException.class,
            MalformedURLException.class,
            GeneralSecurityException.class,
            CloneNotSupportedException.class
    })
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public Mono<BaseResponse<Object>> handleException(Exception e) {
        String code = generateDefaultCode(INTERNAL_SERVER_ERROR.value());
        String errorMessage = "未知应用服务端异常";
        logger.error(LogCategory.EXCEPTION_LOG, errorMessage + ": " + e.getMessage(), e);
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }

    /**
     * 处理参数验证异常，返回HttpStatusCode=400
     * 
     * @param e 参数验证异常对象
     * @return 包含错误信息的响应对象
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(BAD_REQUEST)
    public Mono<BaseResponse<Object>> handleValidationException(MethodArgumentNotValidException e) {
        String code = generateDefaultCode(BAD_REQUEST.value());
        // 收集所有字段的验证错误信息
        String errorMessage = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining("; "));

        logger.error(LogCategory.EXCEPTION_LOG, "参数校验失败: " + errorMessage, e);
        return Mono.just(new BaseResponse<>(new Meta(false, code, errorMessage), null));
    }
}
