package cn.com.chinastock.cnf.webflux.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * JoinRequestCache 类用于在WebFlux环境下聚合和缓存HTTP请求数据。
 * 该类专门用于时序日志场景，先聚合完整的请求体数据，记录请求日志，
 * 然后提供装饰器供后续处理使用。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>聚合流式的请求体数据为完整的DataBuffer</li>
 *     <li>支持时序日志记录（先记录请求，再处理业务逻辑）</li>
 *     <li>根据Content-Type智能判断是否需要缓存请求体</li>
 *     <li>使用装饰器模式提供缓存后的请求体数据</li>
 *     <li>内存友好的数据处理，支持空请求体场景</li>
 * </ul>
 *
 * <p>与FluxRequestCache的区别：</p>
 * <ul>
 *     <li>JoinRequestCache：先聚合数据，立即记录日志，用于时序日志</li>
 *     <li>FluxRequestCache：流式缓存数据，延迟记录日志，用于非时序日志</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * {@code
 * JoinRequestCache requestCache = new JoinRequestCache(request, dataBufferFactory);
 * // 先记录请求日志
 * return requestCache.log(true)
 *     .then(chain.filter(exchange.mutate()
 *         .request(requestCache.requestDecorator())
 *         .build()));
 * }
 * </pre>
 *
 * <AUTHOR>
 * @see DataBufferCache
 * @see RequestLogger
 * @see ServerHttpRequestDecorator
 * @see DataBufferUtils
 */
public class JoinRequestCache {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private final ServerHttpRequest request;
    private final DataBufferFactory dataBufferFactory;
    private DataBuffer dataBuffer;

    /**
     * 构造函数，初始化聚合请求缓存对象
     * 
     * @param request HTTP请求对象，不能为null
     * @param dataBufferFactory 数据缓冲区工厂，用于创建DataBuffer实例
     */
    public JoinRequestCache(ServerHttpRequest request, DataBufferFactory dataBufferFactory) {
        this.request = request;
        this.dataBufferFactory = dataBufferFactory;
    }

    /**
     * 创建请求装饰器，提供缓存后的请求体数据
     * 
     * <p>该方法根据Content-Type判断是否需要提供缓存的请求体：</p>
     * <ul>
     *     <li>如果Content-Type支持缓存，返回包含缓存数据的装饰器</li>
     *     <li>如果Content-Type不支持缓存，返回原始请求装饰器</li>
     * </ul>
     * 
     * <p>注意：该方法必须在log()方法执行完成后调用，否则dataBuffer可能为null</p>
     * 
     * @return ServerHttpRequestDecorator 请求装饰器对象
     */
    public ServerHttpRequestDecorator requestDecorator() {
        if (DataBufferCache.cacheableContentType(request.getHeaders().getContentType())) {
            return new ServerHttpRequestDecorator(request) {
                @Override
                public Flux<DataBuffer> getBody() {
                    return Flux.just(dataBuffer);
                }
            };
        }

        return new ServerHttpRequestDecorator(request);
    }

    /**
     * 聚合请求体数据并记录请求日志
     * 
     * <p>该方法的处理流程：</p>
     * <ol>
     *     <li>检查Content-Type是否支持缓存</li>
     *     <li>如果不支持缓存，直接记录请求基本信息并返回</li>
     *     <li>如果支持缓存，使用DataBufferUtils.join()聚合所有请求体数据</li>
     *     <li>将聚合后的数据转换为字符串并记录日志</li>
     *     <li>保存聚合后的DataBuffer供后续使用</li>
     *     <li>正确释放临时的聚合缓冲区</li>
     * </ol>
     * 
     * @param requestHeadersEnabled 是否启用请求头记录
     * @return Mono&lt;Void&gt; 表示异步操作完成的Mono对象
     */
    public Mono<Void> log(boolean requestHeadersEnabled) {
        if (!DataBufferCache.cacheableContentType(request.getHeaders().getContentType())) {
            RequestLogger.log(request, requestHeadersEnabled);
            return Mono.empty();
        }

        return DataBufferUtils.join(request.getBody()).defaultIfEmpty(dataBufferFactory.wrap(new byte[0]))
                .flatMap(aggregatedBuffer -> {
                    byte[] bytes = new byte[aggregatedBuffer.readableByteCount()];
                    aggregatedBuffer.read(bytes);
                    DataBufferUtils.release(aggregatedBuffer);
                    RequestLogger.log(request, requestHeadersEnabled, new String(bytes, StandardCharsets.UTF_8));

                    dataBuffer = dataBufferFactory.wrap(bytes);
                    return Mono.empty();
                });

    }
}
