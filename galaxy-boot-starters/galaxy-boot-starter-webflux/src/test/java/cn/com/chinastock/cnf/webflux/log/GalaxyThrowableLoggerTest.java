package cn.com.chinastock.cnf.webflux.log;

import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.PropertyConstants;
import cn.com.chinastock.cnf.core.log.filter.ThrowableFilter;
import cn.com.chinastock.cnf.core.log.filter.ThrowablePrettyPrintFilter;
import cn.com.chinastock.cnf.webflux.log.config.LogPropertiesWrapper;
import cn.com.chinastock.cnf.webflux.log.helper.AsyncTestLogAppender;
import org.apache.logging.log4j.core.Filter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

import static org.assertj.core.api.Assertions.assertThat;

public class GalaxyThrowableLoggerTest {

    private LogPropertiesWrapper logPropertiesWrapper;
    @BeforeEach
    void setUp() {
        // 当前测试文件主要测试日志的输出功能，设置日志输出为同步输出
        System.setProperty("log4j2.contextSelector",
                "org.apache.logging.log4j.core.selector.ClassLoaderContextSelector");
        logPropertiesWrapper = new LogPropertiesWrapper();
    }

    @Test
    void shouldPrintExceptionWhenCaptureException() {
        AsyncTestLogAppender throwableAppender = AsyncTestLogAppender.createThrowableAsyncAppender(GalaxyWebfluxLoggerTest.class, ThrowableFilter.createFilter(Filter.Result.ACCEPT, Filter.Result.DENY));
        GalaxyWebfluxLogger throwableLogger = new GalaxyWebfluxLogger(logPropertiesWrapper, LoggerFactory.getLogger(GalaxyWebfluxLoggerTest.class));

        // 创建并记录一个异常日志
        throwableLogger.error(LogCategory.APP_LOG, "Test exception", new RuntimeException("Test exception"));

        assertThat(throwableAppender.getEventsSnapshot()).hasSize(1);
    }

    @Test
    void shouldPrintPrettyExceptionWithCategoryWhenEnabled() {
        // 设置配置为 true
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "true");

        AsyncTestLogAppender throwableAppender = AsyncTestLogAppender.createThrowableAsyncAppender(GalaxyWebfluxLoggerTest.class, ThrowablePrettyPrintFilter.createFilter(Filter.Result.ACCEPT, Filter.Result.DENY));
        GalaxyWebfluxLogger throwableLogger = new GalaxyWebfluxLogger(logPropertiesWrapper, LoggerFactory.getLogger(GalaxyWebfluxLoggerTest.class));

        // 创建并记录一个异常日志
        throwableLogger.error(LogCategory.APP_LOG, "Test error", new RuntimeException("Test exception"));

        assertThat(throwableAppender.getEventsSnapshot())
                .hasSize(1)
                .allSatisfy(event -> {
                    String message = event.getMessage().getFormattedMessage();
                    assertThat(message).contains("Test error");
                    assertThat(event.getThrown()).isNotNull();
                    assertThat(event.getThrown().getMessage()).isEqualTo("Test exception");
                });
    }

}
