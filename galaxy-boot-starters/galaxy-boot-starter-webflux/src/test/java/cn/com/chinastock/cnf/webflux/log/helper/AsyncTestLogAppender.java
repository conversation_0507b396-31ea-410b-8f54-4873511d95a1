package cn.com.chinastock.cnf.webflux.log.helper;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.*;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.filter.AbstractFilter;
import org.apache.logging.log4j.core.filter.ThresholdFilter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 一个专为异步日志测试设计的、线程安全的Log Appender。
 * 它使用BlockingQueue来解决竞争条件问题。
 */
public class AsyncTestLogAppender extends AbstractAppender {

    // 1. 使用线程安全的BlockingQueue替代ArrayList
    private final BlockingQueue<LogEvent> events = new LinkedBlockingQueue<>();

    public AsyncTestLogAppender(AbstractFilter filter) {
        super("AsyncTestLogAppender", filter, null, true, Property.EMPTY_ARRAY);
    }

    @Override
    public void append(LogEvent event) {
        // toImmutable() 很重要，可以防止Log4j2后续复用或修改LogEvent对象
        // add()方法在队列满时会抛异常，对于无界队列LinkedBlockingQueue来说是安全的
        events.add(event.toImmutable());
    }

    /**
     * 清空所有已捕获的日志事件。
     */
    public void clear() {
        events.clear();
    }

    /**
     * 2. 提供一个阻塞的获取方法，用于在测试中等待日志事件到达。
     *
     * @param timeout  等待的超时时间
     * @param timeUnit 时间单位
     * @return 队列中的第一个日志事件，如果在超时前没有事件则返回null
     * @throws InterruptedException 如果线程在等待时被中断
     */
    public LogEvent awaitAndGetEvent(long timeout, TimeUnit timeUnit) throws InterruptedException {
        return events.poll(timeout, timeUnit);
    }

    /**
     * 获取当前已捕获的所有事件的快照。
     * 注意：在异步测试中，直接调用此方法可能无法获取到最新的事件。
     * 推荐使用 awaitAndGetEvent 来确保时序正确。
     *
     * @return 日志事件列表的快照
     */
    public List<LogEvent> getEventsSnapshot() {
        return new ArrayList<>(events);
    }

    public static AsyncTestLogAppender createThresholdAsyncAppender(Class<?> clazz) {
        return createAsyncAppender(clazz, ThresholdFilter.createFilter(Level.ALL, Filter.Result.ACCEPT, Filter.Result.DENY));
    }

    public static  AsyncTestLogAppender createThrowableAsyncAppender(Class<?> clazz, AbstractFilter filter) {
        return createAsyncAppender(clazz, filter);
    }

    private static AsyncTestLogAppender createAsyncAppender(Class<?> clazz, AbstractFilter appenderFilter) {
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        Logger logger = (Logger) context.getLogger(clazz);

        // 移除已有的Appender
        for (Appender appender : logger.getAppenders().values()) {
            logger.removeAppender(appender);
        }

        // 添加测试Appender到所有已配置的Logger
        AsyncTestLogAppender appender = new AsyncTestLogAppender(appenderFilter);
        appender.start();

        logger.addAppender(appender);
        logger.setLevel(Level.ALL);
        logger.setAdditive(false);

        return appender;
    }
}