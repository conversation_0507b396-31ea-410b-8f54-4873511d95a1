package cn.com.chinastock.cnf.webflux.log;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.webflux.log.config.LogPropertiesWrapper;
import cn.com.chinastock.cnf.webflux.log.helper.AsyncTestLogAppender;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.core.LogEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.com.chinastock.cnf.core.log.context.LogMDCKeys.LOG_CATEGORY;
import static org.assertj.core.api.Assertions.assertThat;

class GalaxyWebfluxLoggerTest {

    private AsyncTestLogAppender appender;
    private IGalaxyLogger logger;
    private LogPropertiesWrapper logPropertiesWrapper;

    @BeforeEach
    void setUp() {
        appender = AsyncTestLogAppender.createThresholdAsyncAppender(GalaxyWebfluxLoggerTest.class);

        logPropertiesWrapper = new LogPropertiesWrapper();
        logger = new GalaxyWebfluxLogger(logPropertiesWrapper, LoggerFactory.getLogger(GalaxyWebfluxLoggerTest.class));
    }

    @Test
    void ShouldCapturedEventWhenAsyncLoggingInWebFlux() throws InterruptedException {
        // 模拟一个WebFlux场景，在另一个线程中延迟打印日志
        Mono.delay(Duration.ofMillis(10))
                .doOnSuccess(i -> {
                    logger.info("This is an async log message.");
                })
                .subscribeOn(Schedulers.parallel()) // 确保在不同线程执行
                .subscribe();

        // 关键步骤：使用阻塞方法等待日志事件
        LogEvent logEvent = appender.awaitAndGetEvent(300, TimeUnit.MILLISECONDS);
        assertThat(logEvent).isNotNull();
        assertThat(logEvent.getMessage().getFormattedMessage()).isEqualTo("This is an async log message.");
        assertThat(logEvent.getLevel()).isEqualTo(Level.INFO);

        Map<String, String> contextMap = logEvent.getContextData().toMap();
        assertThat(contextMap).containsEntry(LOG_CATEGORY, LogCategory.FRAMEWORK_LOG.name());
    }

    @Test
    void shouldCapturedAllEventWhenLoggingMultiLevel() throws InterruptedException {
        LogProperties properties = new LogProperties();
        properties.setDefaultCategory(LogCategory.BUSINESS_LOG);
        logPropertiesWrapper.setLogProperties(properties);

        {
            logger.debug("This is an async log message.");
            expect("This is an async log message.", Level.DEBUG, LogCategory.BUSINESS_LOG);
        }
        {
            logger.warn("This is an async log message.");
            expect("This is an async log message.", Level.WARN, LogCategory.BUSINESS_LOG);
        }
        {
            logger.error("This is an async log message.");
            expect("This is an async log message.", Level.ERROR, LogCategory.BUSINESS_LOG);
        }
        {
            logger.debug(LogCategory.FRAMEWORK_LOG, "This is an async log message.");
            expect("This is an async log message.", Level.DEBUG, LogCategory.FRAMEWORK_LOG);
        }
        {
            logger.info(LogCategory.APP_LOG, "This is an async log message.");
            expect("This is an async log message.", Level.INFO, LogCategory.APP_LOG);
        }
        {
            logger.warn(LogCategory.FRAMEWORK_LOG, "This is an async log message.");
            expect("This is an async log message.", Level.WARN, LogCategory.FRAMEWORK_LOG);
        }
        {
            logger.error(LogCategory.FRAMEWORK_LOG, "This is an async log message.");
            expect("This is an async log message.", Level.ERROR, LogCategory.FRAMEWORK_LOG);
        }
    }

    private void expect(String message, Level level, LogCategory category) throws InterruptedException {
        LogEvent logEvent = appender.awaitAndGetEvent(100, TimeUnit.MILLISECONDS);
        assertThat(logEvent).isNotNull();
        assertThat(logEvent.getMessage().getFormattedMessage()).isEqualTo(message);

        if (Objects.nonNull(level)) {
            assertThat(logEvent.getLevel()).isEqualTo(level);
        }

        if (Objects.nonNull(category)) {
            Map<String, String> contextMap = logEvent.getContextData().toMap();
            assertThat(contextMap).containsEntry(LOG_CATEGORY, category.name());
        }
    }

}
