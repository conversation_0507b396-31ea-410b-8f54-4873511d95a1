package cn.com.chinastock.cnf.webflux.log;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.config.TraceContextAutoConfiguration;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.webflux.log.config.LoggingAutoConfiguration;
import cn.com.chinastock.cnf.webflux.log.filter.LogWebfluxFilter;
import cn.com.chinastock.cnf.webflux.log.helper.AsyncTestLogAppender;
import org.apache.logging.log4j.core.LogEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;

import static cn.com.chinastock.cnf.core.log.context.LogMDCKeys.REQUEST_URI;
import static cn.com.chinastock.cnf.core.log.context.TraceConstants.SPAN_ID;
import static cn.com.chinastock.cnf.core.log.context.TraceConstants.TRACE_ID;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {LoggingAutoConfiguration.class, TraceContextAutoConfiguration.class})
class ControllerTraceContextTest {

    private AsyncTestLogAppender appender;
    private WebTestClient webTestClient;

    @Autowired
    private ITraceContext traceContext;

    @BeforeEach
    void setUp() {
        appender = AsyncTestLogAppender.createThresholdAsyncAppender(ControllerTraceContextTest.class);

        webTestClient = WebTestClient
                .bindToController(new TestController())
                .webFilter(new LogWebfluxFilter(new LogProperties(), traceContext))
                .build();
    }

    @Test
    void shouldReturn200WhenPrintLog() throws InterruptedException {
        webTestClient.get().uri("/test/logging")
                .exchange()
                .expectStatus().isOk();

        List<LogEvent> loggedEvents = appender.getEventsSnapshot();
        assertThat(loggedEvents).hasSize(3);
        
        String expectedTraceId = loggedEvents.get(0).getContextData().getValue(TRACE_ID);
        String expectedSpanId = loggedEvents.get(0).getContextData().getValue(SPAN_ID);
        String expectedRequestUri = loggedEvents.get(0).getContextData().getValue("request_uri");

        assertContextData(loggedEvents, TRACE_ID, expectedTraceId);
        assertContextData(loggedEvents, SPAN_ID, expectedSpanId);
        assertContextData(loggedEvents, REQUEST_URI, expectedRequestUri);
    }

    private void assertContextData(List<LogEvent> loggedEvents, String key, String expectedValue) {
        assertThat(expectedValue).isNotNull().isNotEmpty();
        assertThat(loggedEvents)
                .allSatisfy(event -> {
                    String actualValue = event.getContextData().getValue(key);
                    assertThat(actualValue).isEqualTo(expectedValue);
                    assertThat(event.getMessage().getFormattedMessage()).isNotEmpty();
                });
    }

    // 内部测试控制器（必须适配WebFlux，返回Mono或Flux）
    @RestController
    static class TestController {

        IGalaxyLogger log = GalaxyLoggerFactory.getLogger(ControllerTraceContextTest.class);

        @GetMapping("/test/logging")
        public Mono<String> hello() {
            return Mono.defer(() -> {
                log.info("开始处理请求");
                return Mono.just("Hello")
                        .delayElement(Duration.ofMillis(100), Schedulers.parallel()) // 模拟异步IO调用，切换线程
                        .map(s -> {
                            log.info("在 map 操作中");
                            return s + " World!";
                        })
                        .doOnSuccess(s -> log.info("成功处理请求"));
            });
        }

    }
}






