package cn.com.chinastock.cnf.oceanbase;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.io.ClassPathResource;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Galaxy Boot OceanBase 自动配置类
 * <p>
 * 该配置类在应用启动时会自动加载，并打印当前使用的 OceanBase 客户端版本信息。
 * 版本信息从 galaxy-boot-oceanbase.properties 文件中读取，该文件在构建时会被 Maven 处理，
 * 将 ${oceanbase-client.version} 占位符替换为实际的版本号。
 * 
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.oceanbase.jdbc.Driver")
public class GalaxyBootOceanbaseAutoConfiguration {

    private static final String PROPERTIES_FILE = "galaxy-boot-oceanbase.properties";
    private static final String VERSION_KEY = "oceanbase-client-version";
    private static final String UNKNOWN_VERSION = "unknown";

    /**
     * 在 Bean 初始化后打印 OceanBase 客户端版本信息
     */
    @PostConstruct
    public void printOceanBaseClientVersion() {
        String version = loadOceanBaseClientVersion();
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, 
            "Galaxy Boot OceanBase Starter initialized with oceanbase-client version: " + version);
    }

    /**
     * 从属性文件中加载 OceanBase 客户端版本
     * 
     * @return OceanBase 客户端版本号
     */
    private String loadOceanBaseClientVersion() {
        try {
            ClassPathResource resource = new ClassPathResource(PROPERTIES_FILE);
            if (!resource.exists()) {
                GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG, 
                    "Properties file not found: " + PROPERTIES_FILE);
                return UNKNOWN_VERSION;
            }

            Properties properties = new Properties();
            try (InputStream inputStream = resource.getInputStream()) {
                properties.load(inputStream);
                String version = properties.getProperty(VERSION_KEY);
                return version != null ? version : UNKNOWN_VERSION;
            }
        } catch (IOException e) {
            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, 
                "Failed to load OceanBase client version from properties file", e);
            return UNKNOWN_VERSION;
        }
    }
}
