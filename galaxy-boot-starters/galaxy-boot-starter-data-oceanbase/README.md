## Galaxy Boot OceanBase Starter

> Galaxy Boot OceanBase 数据库 Starter 是一个为 OceanBase 数据库提供的 Spring Boot Starter，用于简化应用的开发。

### 特性

- 🚀 **灵活的 ORM 支持**：支持 JPA，MyBatis和MyBatisPlus，使用者可自由选择
- 💾 **高性能连接池**：默认使用 HikariCP 连接池
- 🔄 **动态配置**：支持通过 Apollo 动态调整连接池大小和密码
- 📊 **监控支持**：集成 Spring Boot Actuator 监控
- 🎯 **简单易用**：依赖 Spring Boot 自动配置，无需额外配置
- 🔌 **OceanBase 专用**：内置 OceanBase JDBC 驱动，开箱即用

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter Oceanbase`，只需要引入对应 `starter` 即可：使用 group ID 为 `cn.com.chinastock` 和 `artifact ID` 为
`galaxy-boot-starter-data-oceanbase` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
</dependency>
```

> **注意**：此 Starter 内部依赖 `galaxy-boot-starter-datasource`，提供通用的数据源管理功能，并额外包含 OceanBase JDBC 驱动。

