### 组件介绍

GalaxyBoot Oceanbase Starter 基于 `galaxy-boot-starter-datasource` 构建，提供通用的数据源管理功能，并内置 OceanBase JDBC 驱动。

### 使用方式

#### 使用 JPA

**1. 添加依赖**
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
```

**2. 配置数据源和JPA**
```yaml
spring:
  datasource:
    url: ************************************
    username: root
    password: password
    driver-class-name: com.oceanbase.jdbc.Driver
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username")
    private String username;

    // getters and setters
}
```

**4. 创建Repository**
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
}
```

#### 使用 MyBatis

**1. 添加依赖**
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
</dependency>
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
</dependency>
```

**2. 配置数据源和MyBatis**
```yaml
spring:
  datasource:
    url: ************************************
    username: root
    password: password
    driver-class-name: com.oceanbase.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
public class User {
    private Long id;
    private String username;

    // getters and setters
}
```

**4. 创建Mapper接口**
```java
@Mapper
public interface UserMapper {
    @Select("SELECT * FROM user WHERE id = #{id}")
    User findById(@Param("id") Long id);

    @Insert("INSERT INTO user(username) VALUES(#{username})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
}
```

**5. 创建XML映射文件（可选）**
在 `src/main/resources/mapper/UserMapper.xml`：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    <select id="findByUsername" resultType="com.example.entity.User">
        SELECT * FROM user WHERE username = #{username}
    </select>
</mapper>
```

#### 使用 MyBatisPlus

**1. 添加依赖**
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
</dependency>
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    <version>3.5.9</version>
</dependency>
```

**2. 配置数据源和MyBatisPlus**
```yaml
spring:
  datasource:
    url: ************************************
    username: root
    password: password
    driver-class-name: com.oceanbase.jdbc.Driver

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
@TableName("user")
public class User {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("username")
    private String username;

    // getters and setters
}
```

**4. 创建Mapper接口**
```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
} 
```

### 配置中心集成 - 动态数据库连接池和密码配置

参见 [galaxy-boot-starter-datasource组件介绍](http://pmc.chinastock.com.cn/confluence/display/JSJGZL/galaxy-boot-starter-datasource) 中章节 `配置中心集成 - 动态数据库连接池和密码配置`

### Oceanbase 最佳实践

Oceanbase
官方文档：《[Java 应用与 OceanBase 数据库连接配置最佳实践](https://www.oceanbase.com/docs/common-best-practices-1000000001489649)》

在进行应用程序与 OceanBase 数据库交互时，合适的驱动配置能极大提高系统的稳定性与性能。以下是一些主要的配置项：

- **连接超时设置**：设置客户端连接数据库的等待时间，例如配置 `connectTimeout` 和 `socketTimeout`
  。同时，要实施快速失败重试机制，确保在连接失败时能迅速重试，并设定连接间隔时间，以减少网络阻塞和系统负载。
- **日志记录**：在程序运行期间，应记录连接数据库的 OceanBase 错误码及连接信息（如 IP、PORT、用户名），以便 DBA 快速诊断和解决问题。
- **版本兼容性**：确保客户端库（如 `.so` 或 `.jar` 包）与数据库服务器版本兼容，以保证各组件间的正确协作。
- **切换数据库方法**：推荐使用 `Connection.setCatalog(dbname)` 接口，而非 SQL 命令 `use <dbname>`，以提高代码的可读性和维护性。
- **Session 状态变量设置**：通过 JDBC 接口（如 `setAutoCommit`、`setReadOnly`、`setTransactionIsolation`）来设置 Session
  状态变量，以减少 SQL 使用频率，降低数据库交互次数并提升性能。
- **事务处理**：在执行单个事务（无论是单条 SQL 还是多条 SQL）之前，重新获取数据库连接（`getConnection`），事务执行完毕后，关闭连接（
  `closeConnection`）。确保每个事务独立处理，以防止连接复用导致的状态污染，确保数据的一致性和隔离性。

通过合理配置以上项，您将能够显著提升 OceanBase 数据库与应用程序之间的交互效率和稳定性。
