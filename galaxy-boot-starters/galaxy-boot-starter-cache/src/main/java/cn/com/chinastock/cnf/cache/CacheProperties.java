package cn.com.chinastock.cnf.cache;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * CacheProperties 类用于映射 application.yml 中 spring.custom-caffeine.specs 配置的属性类。
 * 该类负责绑定自定义的Caffeine缓存配置，支持为不同的缓存名称配置不同的缓存规格。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #getSpecs()}：获取缓存规格配置映射。</li>
 *     <li>{@link #setSpecs(Map)}：设置缓存规格配置映射。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "spring.custom-caffeine")
public class CacheProperties {
    
    private Map<String, CaffeineCacheSpec> specs;

    /**
     * 获取缓存规格配置映射
     * 
     * @return 缓存名称到缓存规格的映射
     */
    public Map<String, CaffeineCacheSpec> getSpecs() {
        return specs;
    }

    /**
     * 设置缓存规格配置映射
     * 
     * @param specs 缓存名称到缓存规格的映射
     */
    public void setSpecs(Map<String, CaffeineCacheSpec> specs) {
        this.specs = specs;
    }

    /**
     * CaffeineCacheSpec 类用于定义单个缓存的规格配置。
     * 该类包含了Caffeine缓存的详细配置字符串。
     *
     * <AUTHOR>
     */
    public static class CaffeineCacheSpec {
        
        private String spec;

        /**
         * 获取缓存规格字符串
         * 
         * @return Caffeine缓存规格字符串
         */
        public String getSpec() {
            return spec;
        }

        /**
         * 设置缓存规格字符串
         * 
         * @param spec Caffeine缓存规格字符串
         */
        public void setSpec(String spec) {
            this.spec = spec;
        }
    }
}