package cn.com.chinastock.cnf.cache;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.cache.interceptor.CacheOperation;
import org.springframework.cache.interceptor.CacheOperationSource;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;

/**
 * 缓存名称校验器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class CacheNameValidator implements SmartInitializingSingleton {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(CacheNameValidator.class);

    private final ApplicationContext applicationContext;
    private final CacheProperties appCacheProperties;

    public CacheNameValidator(ApplicationContext applicationContext, CacheProperties cacheProperties) {
        this.applicationContext = applicationContext;
        this.appCacheProperties = cacheProperties;
    }

    @Override
    public void afterSingletonsInstantiated() {
        logger.info("Starting validation of configured cache names against usage in @Cacheable annotations...");

        // 从自定义的 appCacheProperties 中获取已配置的缓存名称
        final Set<String> configuredCacheNames = appCacheProperties.getSpecs() != null ?
                appCacheProperties.getSpecs().keySet() : Collections.emptySet();

        logger.debug("Configured cache names from yml: {}", configuredCacheNames);

        if (CollectionUtils.isEmpty(configuredCacheNames)) {
            logger.warn("No specific cache specs found in 'spring.custom-caffeine.specs'. Skipping validation.");
            return;
        }

        CacheOperationSource cacheOperationSource = applicationContext.getBean(CacheOperationSource.class);
        String[] beanNames = applicationContext.getBeanNamesForType(Object.class);

        for (String beanName : beanNames) {
            if (isFrameworkBean(beanName)) {
                continue;
            }

            // 使用 AopUtils.getTargetClass 来正确处理AOP代理，获取原始类
            final Class<?> targetClass = AopUtils.getTargetClass(applicationContext.getBean(beanName));

            // isCandidateClass 是一个很好的优化，可以快速跳过没有缓存注解的类
            if (cacheOperationSource.isCandidateClass(targetClass)) {
                // 使用 ReflectionUtils 遍历类中的所有方法
                ReflectionUtils.doWithMethods(targetClass, method -> {
                    // 为每个方法获取其缓存操作
                    Collection<CacheOperation> cacheOperations = cacheOperationSource.getCacheOperations(method, targetClass);
                    if (!CollectionUtils.isEmpty(cacheOperations)) {
                        validateCacheOperations(method, configuredCacheNames, cacheOperations);
                    }
                }, ReflectionUtils.USER_DECLARED_METHODS);
            }
        }

        logger.info("Cache name validation completed successfully.");
    }

    /**
     * 校验缓存操作
     * 
     * @param method 被校验的方法
     * @param configuredCacheNames 已配置的缓存名称集合
     * @param cacheOperations 缓存操作集合
     */
    private void validateCacheOperations(Method method, Set<String> configuredCacheNames, Collection<CacheOperation> cacheOperations) {
        for (CacheOperation operation : cacheOperations) {
            for (String cacheName : operation.getCacheNames()) {
                if (!configuredCacheNames.contains(cacheName)) {
                    // 提供更详细的错误信息，包含类名和方法名
                    String errorMessage = String.format(
                            "Validation failed: Cache name '%s' used on method [%s] in class [%s] is not configured in 'app.caffeine.specs'. " +
                                    "Available caches are: %s",
                            cacheName, method.getName(), method.getDeclaringClass().getName(), configuredCacheNames
                    );
                    logger.error(errorMessage);
                    throw new IllegalStateException(errorMessage);
                }
            }
        }
    }

    private boolean isFrameworkBean(String beanName) {
        return beanName.startsWith("org.springframework") || beanName.contains("CacheConfiguration") || beanName.contains("EfficientCacheNameValidator");
    }
}