package cn.com.chinastock.cnf.cache;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import com.github.benmanes.caffeine.cache.AsyncCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Caffeine缓存自动配置类
 * <p>
 * 该配置类负责自动配置基于Caffeine的缓存管理器，支持全局默认配置和特定缓存的个性化配置。
 * 提供了配置合并、缓存创建等核心功能，支持异步缓存场景。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class CacheAutoConfiguration {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(CacheAutoConfiguration.class);

    /**
     * 创建缓存名称校验器Bean
     *
     * @param applicationContext Spring应用上下文，用于获取Bean和缓存操作源
     * @param cacheProperties 缓存配置属性对象，包含已配置的缓存规格信息
     * @return CacheNameValidator实例，用于执行缓存名称校验
     */
    @Bean
    public CacheNameValidator cacheNameChecker(ApplicationContext applicationContext, CacheProperties cacheProperties) {
        return new CacheNameValidator(applicationContext, cacheProperties);
    }

    /**
     * 创建并配置Caffeine缓存管理器
     * @param defaultSpec 全局默认的Caffeine缓存规格字符串
     * @param appCacheProperties 应用级缓存配置属性
     * @return 配置完成的缓存管理器实例
     */
    @Bean
    @Primary
    public CacheManager cacheManager(@Value("${spring.cache.caffeine.spec:}") String defaultSpec,
                                     CacheProperties appCacheProperties) {

        SimpleCacheManager cacheManager = new SimpleCacheManager();

        logger.info("Loaded default Caffeine spec: '{}'", defaultSpec);

        Map<String, CacheProperties.CaffeineCacheSpec> specificSpecs =
                appCacheProperties.getSpecs() != null ? appCacheProperties.getSpecs() : Collections.emptyMap();

        List<CaffeineCache> caches = specificSpecs.entrySet().stream()
                .map(entry -> {
                    String cacheName = entry.getKey();
                    String specificSpec = entry.getValue().getSpec();

                    // 核心逻辑：合并默认与特定配置
                    String finalSpec = mergeSpecs(defaultSpec, specificSpec);

                    logger.info("Creating cache '{}' with final spec: {}", cacheName, finalSpec);
                    return buildCache(cacheName, finalSpec);
                })
                .collect(Collectors.toList());

        cacheManager.setCaches(caches);
        return cacheManager;
    }

    /**
     * 合并默认配置和特定配置规格字符串
     *
     * @param defaultSpec  全局默认的Caffeine规格字符串
     * @param specificSpec 特定缓存的规格字符串
     * @return 合并后的最终规格字符串
     */
    private String mergeSpecs(String defaultSpec, String specificSpec) {
        if (!StringUtils.hasText(specificSpec)) {
            return defaultSpec;
        }
        if (!StringUtils.hasText(defaultSpec)) {
            return specificSpec;
        }

        Map<String, String> defaultConfig = parseSpecToMap(defaultSpec);
        Map<String, String> specificConfig = parseSpecToMap(specificSpec);

        // 关键：将特定配置覆盖到默认配置上
        defaultConfig.putAll(specificConfig);

        // 将合并后的 Map 重新组合成 spec 字符串
        return mapToSpecString(defaultConfig);
    }

    /**
     * 将Caffeine规格字符串解析为Map结构
     * @param spec Caffeine规格字符串
     * @return 解析后的键值对映射
     */
    private Map<String, String> parseSpecToMap(String spec) {
        return Arrays.stream(spec.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(part -> part.split("=", 2))
                .collect(Collectors.toMap(
                        parts -> parts[0], // key
                        parts -> parts.length > 1 ? parts[1] : "", // value (处理无值的key，如 recordStats)
                        (v1, v2) -> v2 // 如果有重复的key，以后者为准
                ));
    }

    /**
     * 将Map结构转换回Caffeine规格字符串
     * @param map 键值对映射
     * @return 重新组装的Caffeine规格字符串
     */
    private String mapToSpecString(Map<String, String> map) {
        return map.entrySet().stream()
                .map(entry -> {
                    if (StringUtils.hasText(entry.getValue())) {
                        return entry.getKey() + "=" + entry.getValue();
                    }
                    return entry.getKey(); // 处理无值的key
                })
                .collect(Collectors.joining(","));
    }

    /**
     * 构建Caffeine缓存实例
     *
     * @param name 缓存名称
     * @param spec Caffeine规格字符串
     * @return 完全配置的CaffeineCache实例
     */
    private CaffeineCache buildCache(String name, String spec) {
        Caffeine<Object, Object> caffeineBuilder = StringUtils.hasText(spec) ?
                Caffeine.from(spec) : Caffeine.newBuilder();

        // 构建一个 AsyncCache 以支持 WebFlux 等异步场景
        AsyncCache<Object, Object> asyncCache = caffeineBuilder.buildAsync();

        // 使用支持异步的构造函数。allowNullValues 默认为 true，与 Spring 行为保持一致。
        return new CaffeineCache(name, asyncCache, true);
    }

}