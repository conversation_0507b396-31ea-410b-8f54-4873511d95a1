## Galaxy Boot Starter Cache（未发布）

> Galaxy Boot Starter Cache 是一个基于 Caffeine 的高性能缓存组件，支持全局默认配置和特定缓存的个性化配置，提供缓存名称校验和异步缓存支持。

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter Cache`，只需要引入对应 `starter` 即可：使用 group ID 为 `cn.com.chinastock` 和 `artifact ID` 为
`galaxy-boot-starter-cache` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-cache</artifactId>
</dependency>
``` 
