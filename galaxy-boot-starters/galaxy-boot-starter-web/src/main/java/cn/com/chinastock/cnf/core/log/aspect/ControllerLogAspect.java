package cn.com.chinastock.cnf.core.log.aspect;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.logger.FrameworkLogger;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.annotation.Annotation;

/**
 * ControllerLogAspect 类是一个切面类，用于在控制器方法执行前后记录请求和响应的日志信息。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #ControllerLogAspect(LogProperties)}：构造函数，用于初始化日志配置和日志记录器。</li>
 *     <li>{@link #around(ProceedingJoinPoint)}：用于在控制器方法执行前后记录请求和响应日志，对请求体和响应体中的字段进行掩码处理。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Aspect
@Order(Ordered.LOWEST_PRECEDENCE - 1)
@RestControllerAdvice
public class ControllerLogAspect implements ResponseBodyAdvice<Object> {
    private final LogProperties logProperties;
    private final FrameworkLogger frameworkLogger;

    public ControllerLogAspect(LogProperties logProperties) {
        this.logProperties = logProperties;
        this.frameworkLogger = new FrameworkLogger(logProperties);
    }

    @Around("@within(org.springframework.web.bind.annotation.RestController)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        if (logProperties.isControllerLogEnabled()) {
            // 记录请求信息
            frameworkLogger.logRequestWithFieldsMasked(getRequestObject(point));
        }

        return point.proceed();
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        if (logProperties.isControllerLogEnabled()) {
            // 处理响应体，对于Swagger API docs请求，MediaType为application/json，但实际内容是byte[]，需要特殊处理
            Object processedBody = HttpLogUtils.processResponseBody(body, selectedContentType);
            frameworkLogger.logResponseWithFieldsMasked(selectedContentType,
                    response.getHeaders(),
                    ((ServletServerHttpResponse) response).getServletResponse().getStatus(),
                    processedBody);
        }
        return body;
    }

    private Object getRequestObject(ProceedingJoinPoint point) {
        Object[] args = point.getArgs();
        MethodSignature signature = (MethodSignature) point.getSignature();
        Annotation[][] parameterAnnotations = signature.getMethod().getParameterAnnotations();

        for (int i = 0; i < args.length; i++) {
            if (isAnnotatedWithRequestBody(parameterAnnotations[i])) {
                return args[i];
            }
        }
        return null;
    }

    private boolean isAnnotatedWithRequestBody(Annotation[] annotations) {
        for (Annotation annotation : annotations) {
            if (annotation instanceof RequestBody) {
                return true;
            }
        }
        return false;
    }
}