package cn.com.chinastock.cnf.core.log;

/**
 * <p>该类提供了对于 GalaxyLogger 的封装，用法参见 GalaxyLogger 说明</p>
 *
 * <AUTHOR>
 * @see GalaxyLogger
 */
public class GalaxyLoggerHolder {

    private GalaxyLoggerHolder() {
    }

    private static class SingletonHolder {
        private static final GalaxyLoggerHolder INSTANCE = new GalaxyLoggerHolder();
    }

    public static GalaxyLoggerHolder getInstance() {
        return SingletonHolder.INSTANCE;
    }


    /**
     * 记录信息级别的日志。
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.info("User logged in: {}", user.getName());
     *        // 输出: User logged in: JohnDoe
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数，用于替换占位符
     * @see GalaxyLogger#info(String, Object...)
     */
    public void info(String message, Object... args) {
        GalaxyLogger.info(message, args);
    }


    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *      GalaxyLogger.error("An error occurred", new RuntimeException("error"));
     *      // 输出异常堆栈
     *    }
     * </pre>
     *
     * @param message 错误信息
     * @param t       异常对象
     * @see GalaxyLogger#error(String, Throwable)
     */
    public void error(String message, Throwable t) {
        GalaxyLogger.error(message, t);
    }

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.error("An error occurred: %s", "Invalid input");
     *        // 输出: "An error occurred: Invalid input"
     *    }
     * </pre>
     *
     * @param message 错误信息模板，支持格式化
     * @param args    格式化参数
     * @see GalaxyLogger#error(String, Object...)
     */
    public void error(String message, Object... args) {
        GalaxyLogger.error(message, args);
    }

    /**
     * 记录警告日志
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.warn("User login failed: {}", username);
     *        // 输出: User login failed: admin
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数
     * @see GalaxyLogger#warn(String, Object...)
     */
    public void warn(String message, Object... args) {
        GalaxyLogger.warn(message, args);
    }

    /**
     * 打印调试信息
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.debug("User logged in: %s", user.getName());
     *        // 输出: "User logged in: JohnDoe"
     *    }
     * </pre>
     *
     * @param message 调试信息模板，支持格式化字符串
     * @param args    格式化字符串的参数
     * @see GalaxyLogger#debug(String, Object...)
     */
    public void debug(String message, Object... args) {
        GalaxyLogger.debug(message, args);
    }


    /**
     * 记录INFO级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.info(LogCategory.USER_ACTION, "User {} logged in at {}", userId, timestamp);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     * @see GalaxyLogger#info(LogCategory, String, Object...)
     */
    public void info(LogCategory logCategory, String message, Object... args) {
        GalaxyLogger.info(logCategory, message, args);
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "An error occurred", new RuntimeException("error"));
     *     // 输出异常堆栈
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param t           异常
     * @see GalaxyLogger#error(LogCategory, String, Throwable)
     */
    public void error(LogCategory logCategory, String message, Throwable t) {
        GalaxyLogger.error(logCategory, message, t);
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.error(LogCategory.BUSINESS_LOG, "An error occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     * @see GalaxyLogger#error(LogCategory, String, Object...)
     */
    public void error(LogCategory logCategory, String message, Object... args) {
        GalaxyLogger.error(logCategory, message, args);
    }

    /**
     * 记录WARN级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.warn(LogCategory.BUSINESS_LOG, "An warning occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     * @see GalaxyLogger#warn(LogCategory, String, Object...)
     */
    public void warn(LogCategory logCategory, String message, Object... args) {
        GalaxyLogger.warn(logCategory, message, args);
    }

    /**
     * 记录DEBUG级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.debug(LogCategory.BUSINESS_LOG, "An debug message, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     * @see GalaxyLogger#debug(LogCategory, String, Object...)
     */
    public void debug(LogCategory logCategory, String message, Object... args) {
        GalaxyLogger.debug(logCategory, message, args);
    }
}
