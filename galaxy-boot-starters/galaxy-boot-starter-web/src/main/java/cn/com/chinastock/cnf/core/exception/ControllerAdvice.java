package cn.com.chinastock.cnf.core.exception;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;
import java.net.MalformedURLException;
import java.security.GeneralSecurityException;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.stream.Collectors;

import static org.springframework.http.HttpStatus.*;

/**
 * ControllerAdvice 类用于全局处理控制器中的异常。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #handleBusinessException(BusinessException)}：处理业务异常，返回HttpStatusCode=200。</li>
 *     <li>{@link #handleUnauthorizedException(UnauthorizedException)}：处理未认证异常，返回HttpStatusCode=401。</li>
 *     <li>{@link #handleForbiddenException(ForbiddenException)}：处理禁止访问异常，返回HttpStatusCode=403。</li>
 *     <li>{@link #handleServerErrorException(ServerErrorException)}：处理服务器异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleRuntimeException(RuntimeException)}：处理运行时异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleException(Exception)}：处理多种常见异常，返回HttpStatusCode=500。</li>
 *     <li>{@link #handleValidationException(MethodArgumentNotValidException)}：处理参数验证异常，返回HttpStatusCode=400。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class ControllerAdvice {

    private final String systemCode;

    public ControllerAdvice(LogProperties logProperties) {
        this.systemCode = logProperties.getSystemCode();
    }

    private String generateDefaultCode(Integer code) {
        return systemCode + "TCNF" + code.toString();
    }

    private String generateLogMessage(String code, String message) {
        return "[" + code + "]" + message;
    }

    private void logException(String code, String message, Exception exception) {
        if (exception == null) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, generateLogMessage(code, message));
        } else {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, generateLogMessage(code, message), exception);
        }
    }

    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(OK)
    public BaseResponse<Object> handleBusinessException(BusinessException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(OK.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "业务异常" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(UNAUTHORIZED)
    public BaseResponse<Object> handleUnauthorizedException(UnauthorizedException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(UNAUTHORIZED.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "认证失败" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(FORBIDDEN)
    public BaseResponse<Object> handleForbiddenException(ForbiddenException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(FORBIDDEN.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "无权限访问" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler(ServerErrorException.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public BaseResponse<Object> handleServerErrorException(ServerErrorException e) {
        String code = e.getCode().isEmpty() ? generateDefaultCode(INTERNAL_SERVER_ERROR.value()) : e.getCode();
        String errorMessage = e.getMessage().isEmpty() ? "应用服务端异常" : e.getMessage();
        logException(code, errorMessage, e.getException());
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public BaseResponse<Object> handleRuntimeException(RuntimeException e) {
        String code = generateDefaultCode(INTERNAL_SERVER_ERROR.value());
        String errorMessage = "未知应用服务端异常";
        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, errorMessage + ": " + e.getMessage(), e);
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler({
            IOException.class,
            SQLException.class,
            ClassNotFoundException.class,
            NoSuchMethodException.class,
            NoSuchFieldException.class,
            InstantiationException.class,
            InterruptedException.class,
            ParseException.class,
            MalformedURLException.class,
            GeneralSecurityException.class,
            CloneNotSupportedException.class
    })
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public BaseResponse<Object> handleException(Exception e) {
        String code = generateDefaultCode(INTERNAL_SERVER_ERROR.value());
        String errorMessage = "未知应用服务端异常";
        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, errorMessage + ": " + e.getMessage(), e);
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(BAD_REQUEST)
    public BaseResponse<Object> handleValidationException(MethodArgumentNotValidException e) {
        String code = generateDefaultCode(BAD_REQUEST.value());
        // 收集所有字段的验证错误信息
        String errorMessage = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining("; "));

        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "参数校验失败: " + errorMessage, e);
        return new BaseResponse<>(new Meta(false, code, errorMessage), null);
    }
}
