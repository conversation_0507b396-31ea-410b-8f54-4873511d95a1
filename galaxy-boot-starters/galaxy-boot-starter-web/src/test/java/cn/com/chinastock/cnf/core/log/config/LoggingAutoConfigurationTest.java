package cn.com.chinastock.cnf.core.log.config;

import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.MicrometerIntegratedTraceContext;
import cn.com.chinastock.cnf.core.log.filter.LogFilter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {
        LoggingAutoConfiguration.class,
        LoggingEnvironmentPostProcessor.class
})
@EnableAutoConfiguration
@TestPropertySource(properties = {
        "galaxy.system.code=TEST_SYSTEM",
        "spring.application.name=TEST_SERVICE",
        "galaxy.log.request-response.enabled=true",
        "galaxy.log.request-response.request-headers=true",
        "galaxy.log.request-response.response-headers=true",
        "galaxy.log.request-response.mask-field=true",
        "galaxy.log.performance.enabled=true",
        "galaxy.log.max-length=2000",
        "galaxy.log.default-category=APP_LOG",
        "galaxy.log.exception-pretty-print=false"
})
class LoggingAutoConfigurationTest {

    @Autowired
    private ITraceContext traceContext;

    @Autowired
    private LogFilter logFilter;

    @Autowired
    private LogProperties logProperties;

    @Test
    void shouldAutoConfigureLoggingComponents() {
        // 验证所有必需的组件都被正确注入
        // 由于引入了 Micrometer Tracing，现在应该使用 MicrometerIntegratedTraceContext
        assertThat(traceContext).isNotNull()
                .isInstanceOf(MicrometerIntegratedTraceContext.class);

        assertThat(logFilter).isNotNull();
    }

    @Test
    void shouldSetSystemPropertiesFromApplicationProperties() {
        // 验证系统属性是否被正确设置
        assertThat(System.getProperty("system-code")).isEqualTo("TEST_SYSTEM");
        assertThat(System.getProperty("service-name")).isEqualTo("TEST_SERVICE");
        assertThat(System.getProperty("ip-addr")).isNotNull();
    }

    @Test
    void shouldLoadConfiguredProperties() {
        assertThat(logProperties.isRequestResponseEnabled()).isTrue();
        assertThat(logProperties.isRequestHeadersEnabled()).isTrue();
        assertThat(logProperties.isResponseHeadersEnabled()).isTrue();
        assertThat(logProperties.isRequestResponseMaskFieldEnabled()).isTrue();
        assertThat(logProperties.isPerformanceLogEnabled()).isTrue();
        assertThat(logProperties.getMaxLength()).isEqualTo(2000);
        assertThat(logProperties.getDefaultCategory()).isEqualTo(LogCategory.APP_LOG);
        assertThat(logProperties.isExceptionPrettyPrint()).isFalse();
    }
} 