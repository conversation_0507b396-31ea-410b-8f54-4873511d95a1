package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.aspect.MaskedField;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.mock.web.MockPart;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FrameworkLoggerMaskFieldTest {

    private LogProperties logProperties;

    @Mock
    RequestAttributes requestAttributes;

    @Mock
    HttpServletRequest httpServletRequest;

    private FrameworkLogger frameworkLogger;

    @BeforeEach
    void setUp() {
        logProperties = new LogProperties();
        logProperties.setRequestResponseEnabled(true);
        logProperties.setRequestHeadersEnabled(true);
        logProperties.setResponseHeadersEnabled(true);
        logProperties.setRequestResponseMaskFieldEnabled(true);

        frameworkLogger = new FrameworkLogger(logProperties);
    }

    @Test
    void shouldLogRequestWithFieldsMasked() {
        // Given
        when(httpServletRequest.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);
        when(httpServletRequest.getQueryString()).thenReturn("param1=value1&param2=value2");
        when(httpServletRequest.getHeaderNames()).thenReturn(Collections.enumeration(
                Collections.singletonList("Content-Type")));
        when(httpServletRequest.getHeader("Content-Type")).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        // When
        try (MockedStatic<RequestContextHolder> requestContextHolder = mockStatic(RequestContextHolder.class);
             MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {

            requestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(requestAttributes);
            when(requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST)).thenReturn(httpServletRequest);

            frameworkLogger.logRequestWithFieldsMasked(new LoginRequest("testUser", "password123", "secret-key"));

            // Then
            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.REQUEST_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("query_string=param1=value1&param2=value2")
                    .contains("headers={\"Content-Type\":\"application/json\"}")
                    .contains("body={")
                    .contains("\"username\":\"testUser\"")
                    .contains("\"password\":\"***\"")
                    .contains("\"secretKey\":\"***\"");
        }
    }

    @Test
    void shouldLogRequestForFileUpload() {
        // Given
        MockMultipartHttpServletRequest request = new MockMultipartHttpServletRequest();
        request.addPart(new MockPart("document", "test.pdf", new byte[1024 * 1024]));
        request.addPart(new MockPart("image", "photo.jpg", new byte[2048]));

        // When
        try (MockedStatic<RequestContextHolder> requestContextHolder = mockStatic(RequestContextHolder.class);
             MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {

            requestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(requestAttributes);
            when(requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST)).thenReturn(request);

            frameworkLogger.logRequestWithFieldsMasked(null);

            // Then
            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.REQUEST_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("headers={\"Content-Type\":\"multipart/form-data\"}")
                    .contains("body=[{\"file_name\":\"test.pdf\",\"file_size\":\"1.0MB\"},{\"file_name\":\"photo.jpg\",\"file_size\":\"2.0KB\"}]")
                    .contains("query_string=null");
        }
    }

    @Test
    void shouldLogResponseWithFieldsMasked() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        BaseResponse<UserResponse> response = new BaseResponse<>(new Meta(true, "0", "Success"),
                new UserResponse("John Doe", "<EMAIL>", "1234567890"));

        // When
        try (MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {
            frameworkLogger.logResponseWithFieldsMasked(
                    MediaType.APPLICATION_JSON,
                    headers,
                    200,
                    response
            );

            // Then
            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.RESPONSE_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("headers={\"Content-Type\":\"application/json\"}")
                    .contains("http_status_code=200")
                    .contains("meta_code=0")
                    .contains("\"success\":true")//,\"message\":\"Success\"}")
                    .contains("\"code\":\"0\"")
                    .contains("\"message\":\"Success\"")
                    .contains("\"name\":\"John Doe\"")
                    .contains("\"email\":\"<EMAIL>\"")
                    .contains("\"phone\":\"***\"");
        }
    }

    @Test
    void shouldLogFileDownloadResponse() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.attachment().filename("test.pdf").build());
        headers.setContentLength(1024L);

        // When
        try (MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {
            frameworkLogger.logResponseWithFieldsMasked(
                    MediaType.APPLICATION_OCTET_STREAM,
                    headers,
                    200,
                    null
            );

            // Then
            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.RESPONSE_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("body={\"file_name\":\"test.pdf\",\"file_size\":\"1.0KB\"}")
                    .contains("http_status_code=200")
                    .contains("meta_code=null")
                    .contains("\"Content-Type\":\"application/octet-stream\"")
                    .contains("\"Content-Disposition\":\"attachment; filename=\\\"test.pdf\\\"\"");
        }
    }

    @Test
    void shouldNotLogRequestHeadersWhenDisabled() {
        // Given
        logProperties.setRequestHeadersEnabled(false);

        // When
        try (MockedStatic<RequestContextHolder> requestContextHolder = mockStatic(RequestContextHolder.class);
             MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {

            requestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(requestAttributes);
            when(requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST)).thenReturn(httpServletRequest);

            frameworkLogger.logRequestWithFieldsMasked(new LoginRequest("testUser", "password123", "secret-key"));

            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.REQUEST_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("query_string=null")
                    .contains("headers=null")
                    .contains("body={")
                    .contains("\"username\":\"testUser\"")
                    .contains("\"password\":\"***\"")
                    .contains("\"secretKey\":\"***\"");
        }
    }

    @Test
    void shouldNotLogResponseHeadersWhenDisabled() {
        // Given
        logProperties.setResponseHeadersEnabled(false);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        BaseResponse<UserResponse> response = new BaseResponse<>(new Meta(true, "0", "Success"),
                new UserResponse("John Doe", "<EMAIL>", "1234567890"));

        // When
        try (MockedStatic<GalaxyLogger> galaxyLogger = mockStatic(GalaxyLogger.class)) {
            frameworkLogger.logResponseWithFieldsMasked(
                    MediaType.APPLICATION_JSON,
                    headers,
                    200,
                    response
            );

            // Then
            ArgumentCaptor<String> logMessageCaptor = ArgumentCaptor.forClass(String.class);
            galaxyLogger.verify(() -> GalaxyLogger.info(eq(LogCategory.RESPONSE_LOG), logMessageCaptor.capture()));

            String logMessage = logMessageCaptor.getValue();
            assertThat(logMessage)
                    .contains("headers=null")
                    .contains("http_status_code=200")
                    .contains("meta_code=0")
                    .contains("\"success\":true")//,\"message\":\"Success\"}")
                    .contains("\"code\":\"0\"")
                    .contains("\"message\":\"Success\"")
                    .contains("\"name\":\"John Doe\"")
                    .contains("\"email\":\"<EMAIL>\"")
                    .contains("\"phone\":\"***\"");
        }
    }

    // Test classes
    static class LoginRequest {
        private final String username;
        @MaskedField
        private final String password;
        @MaskedField
        private final String secretKey;

        public LoginRequest(String username, String password, String secretKey) {
            this.username = username;
            this.password = password;
            this.secretKey = secretKey;
        }
    }

    static class UserResponse {
        private final String name;
        private final String email;
        @MaskedField
        private final String phone;

        public UserResponse(String name, String email, String phone) {
            this.name = name;
            this.email = email;
            this.phone = phone;
        }
    }
} 