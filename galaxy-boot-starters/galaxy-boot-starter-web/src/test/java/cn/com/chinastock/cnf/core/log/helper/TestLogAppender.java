package cn.com.chinastock.cnf.core.log.helper;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.filter.AbstractFilter;

import java.util.ArrayList;
import java.util.List;

public class TestLogAppender extends AbstractAppender {
    private final List<LogEvent> events = new ArrayList<>();

    public TestLogAppender(AbstractFilter filter) {
        super("TestLogAppender", 
              filter,
              null, 
              true, 
              Property.EMPTY_ARRAY);
    }

    @Override
    public void append(LogEvent event) {
        events.add(event.toImmutable());
    }

    public List<LogEvent> getEvents() {
        return events;
    }

    public void clear() {
        events.clear();
    }
}