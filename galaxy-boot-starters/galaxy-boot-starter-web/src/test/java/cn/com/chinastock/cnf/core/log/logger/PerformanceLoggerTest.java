package cn.com.chinastock.cnf.core.log.logger;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.util.StopWatch;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PerformanceLogger 测试类
 * 
 * <p>测试性能日志记录器在各种异常场景下的行为，确保：</p>
 * <ul>
 *     <li>正常情况下能够正确启动和停止计时</li>
 *     <li>重复启动时能够正确处理</li>
 *     <li>未启动就停止时能够正确处理</li>
 *     <li>异常情况下能够正确重置状态</li>
 * </ul>
 */
class PerformanceLoggerTest {

    private PerformanceLogger performanceLogger;

    @BeforeEach
    void setUp() {
        performanceLogger = new PerformanceLogger();
    }

    @Test
    @DisplayName("正常启动和停止性能监控")
    void testNormalStartAndStop() {
        assertDoesNotThrow(() -> {
            performanceLogger.start();
            Thread.sleep(10); // 模拟一些处理时间
            performanceLogger.stop();
        });
    }

    @Test
    @DisplayName("重复启动性能监控应该能够正确处理")
    void testMultipleStart() {
        assertDoesNotThrow(() -> {
            performanceLogger.start();
            performanceLogger.start(); // 第二次启动应该不会抛出异常
            performanceLogger.stop();
        });
    }

    @Test
    @DisplayName("未启动就停止应该能够正确处理")
    void testStopWithoutStart() {
        assertDoesNotThrow(() -> {
            performanceLogger.stop(); // 直接停止应该不会抛出异常
        });
    }

    @Test
    @DisplayName("重置StopWatch状态")
    void testResetStopWatch() {
        assertDoesNotThrow(() -> {
            performanceLogger.start();
            performanceLogger.resetStopWatch(); // 重置状态
            performanceLogger.start(); // 重置后应该能够正常启动
            performanceLogger.stop();
        });
    }

    @Test
    @DisplayName("并发场景下的性能监控")
    void testConcurrentPerformanceLogging() throws InterruptedException {
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> assertDoesNotThrow(() -> {
                performanceLogger.start();
                Thread.sleep(5);
                performanceLogger.stop();
            }));
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
    }

    @Test
    @DisplayName("异常中断后的状态恢复")
    void testStateRecoveryAfterException() {
        assertDoesNotThrow(() -> {
            performanceLogger.start();
            
            // 模拟异常情况，直接重置而不停止
            performanceLogger.resetStopWatch();
            
            // 重置后应该能够正常使用
            performanceLogger.start();
            performanceLogger.stop();
        });
    }

    @Test
    @DisplayName("多次启动停止循环")
    void testMultipleStartStopCycles() {
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 5; i++) {
                performanceLogger.start();
                Thread.sleep(1);
                performanceLogger.stop();
            }
        });
    }

    /**
     * 获取ThreadLocal中的StopWatch实例（用于测试）
     *
     * @return ThreadLocal中的StopWatch实例
     * @throws Exception 如果反射访问失败
     */
    private StopWatch getStopWatchFromThreadLocal() throws Exception {
        Field field = PerformanceLogger.class.getDeclaredField("stopWatchThreadLocal");
        field.setAccessible(true);
        ThreadLocal<StopWatch> threadLocal = (ThreadLocal<StopWatch>) field.get(performanceLogger);
        return threadLocal.get();
    }

    @Test
    @DisplayName("验证ThreadLocal清理")
    void testThreadLocalCleanup() throws Exception {
        performanceLogger.start();
        StopWatch stopWatch = getStopWatchFromThreadLocal();
        assertNotNull(stopWatch);
        assertTrue(stopWatch.isRunning());
        
        performanceLogger.stop();
        
        // 停止后ThreadLocal应该被清理
        assertDoesNotThrow(() -> {
            StopWatch newStopWatch = getStopWatchFromThreadLocal();
            assertFalse(newStopWatch.isRunning());
            assertEquals(0, newStopWatch.getTaskCount());
        });
    }
}
