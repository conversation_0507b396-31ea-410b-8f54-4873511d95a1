### 组件介绍

GalaxyBoot DataSource Starter 提供了通用的数据源管理功能，支持任何 JDBC 兼容的数据库驱动。
默认使用 HikariCP 作为连接池，因为 `HikariCP` 是 `Spring Boot 2.x` 和 `Spring Boot 3.x`
的[默认连接池](https://docs.spring.io/spring-boot/reference/data/sql.html#data.sql.datasource.connection-pool)，性能优秀，适合高并发场景。

### 使用方式（示例展示）

#### 使用 MySQL 和 JPA

**1. 添加依赖**
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-datasource</artifactId>
</dependency>
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
```

**2. 配置数据源和JPA**
```yaml
spring:
  datasource:
    url: ********************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "email")
    private String email;

    // getters and setters
}
```

**4. 创建Repository**
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);
}
```

#### 使用 PostgreSQL 和 MyBatis

**1. 添加依赖**
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-datasource</artifactId>
</dependency>
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
</dependency>
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
</dependency>
```

**2. 配置数据源和MyBatis**
```yaml
spring:
  datasource:
    url: *************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
public class User {
    private Long id;
    private String name;
    private String email;

    // getters and setters
}
```

**4. 创建Mapper接口**
```java
@Mapper
public interface UserMapper {
    @Select("SELECT * FROM users WHERE id = #{id}")
    User findById(@Param("id") Long id);

    @Insert("INSERT INTO users(name, email) VALUES(#{name}, #{email})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
}
```

**5. 创建XML映射文件（可选）**
在 `src/main/resources/mapper/UserMapper.xml`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    <select id="findById" resultType="com.example.entity.User">
        SELECT * FROM users WHERE id = #{id}
    </select>
</mapper>
```

#### 使用 Oceanbase 和 MyBatisPlus

**1. 添加依赖**
```
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-datasource</artifactId>
</dependency>
<dependency>
    <groupId>com.oceanbase</groupId>
    <artifactId>oceanbase-client</artifactId>
</dependency>
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
</dependency>
```

**2. 配置数据源和MyBatisPlus**
```yaml
spring:
  datasource:
    url: ************************************
    username: root
    password: password
    driver-class-name: com.oceanbase.jdbc.Driver

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true

galaxy:
  datasource:
    dynamic-maximum-pool-size: true
```

**3. 创建实体类**
```java
@TableName("users")
public class User {
    @TableId
    private Long id;

    @TableField("name")
    private String name;

    @TableField("email")
    private String email;

    // getters and setters
}
```

**4. 创建Mapper接口**

```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
}
```

### 配置中心集成 - 动态数据库连接池和密码配置

支持两种方式动态更新 `HikariCP` 连接池配置：

1. 通过 Apollo 配置中心动态更新连接池配置（当前方式）
2. Nacos 配置中心动态更新连接池配置（未来）
3. ~~通过 AutoConfiguration + Servlet 的方式动态更新连接池配置~~（不合适、不使用）

支持通过 Apollo 配置中心动态修改数据库密码

#### Apollo 配置中心集成

使用前，请确保项目中引入了 apollo-client 依赖以及配置了 Apollo 配置中心的地址。在配置了 `spring-boot-starter-parent`
的情况下，只需要在项目的 pom.xml 文件中添加如下依赖即可：

```xml

<dependency>
    <groupId>com.ctrip.framework.apollo</groupId>
    <artifactId>apollo-client</artifactId>
</dependency>
```

确保在 `application.yaml` 中配置了 Apollo 配置中心的地址，如：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址
```

启动后检查是否成功连接到 Apollo 配置中心，可以在日志中看到类似如下的信息：

```
V1|........|Located meta services from apollo.meta configuration: http://localhost:8080!|-
V1|........|Located meta server address http://localhost:8080 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider|-
```

#### 修改数据库连接池配置

注意：Galaxy Datasource 仅用于提供动态配置等的支持，其它配置项和 Spring Datasource 一致，如：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址

spring:
  datasource:
    # 其它配置项 
    # 请根据实际情况修改以下配置
    hikari:
      maximum-pool-size: 10

galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
```

使用：

1. 在项目中引入 `galaxy-boot-starter-data-oceanbase` 依赖，并按需配置 `application.yaml` 文件即可使用。
2. 通过 Apollo 配置中心动态更新连接池配置，对应的 Key 为 `galaxy.datasource.dynamic-maximum-pool-size`。
3. 查看连接池配置是否生效，可以通过日志或者 `http://localhost:8080/actuator/prometheus` 端点查看 `HikariCP` 的监控数据。

修改完成功的日志示例：

```
V1|...|com.ctrip.framework.apollo.spring.property.AutoUpdateConfigChangeListener|-|Auto update apollo changed value successfully, new value: 50, key: spring.datasource.hikari.maximum-pool-size, beanName: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration, field: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration$$SpringCGLIB$$0.maxActive|-
V1|...|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPoolSizeAutoConfiguration|FRAMEWORK_LOG|Updating maximum pool size from 10 to 50|-
```

#### 动态修改数据库密码

确保在 `application.yaml` 中配置了 Apollo 配置中心的地址，并启用动态密码刷新功能：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址

galaxy:
  datasource:
    dynamic-refresh-username-password: true  # 启用动态密码刷新
```

通过 Apollo 配置中心动态修改数据库密码的使用步骤：

1. 在 Apollo 配置中心修改密码配置项：
    - 配置 Key：`spring.datasource.password`
    - 配置 Value：新的数据库密码
2. 密码更新流程：
    - 系统检测到 Apollo 配置变更时会自动触发密码更新
    - 创建新的数据源连接池，使用新密码进行验证
    - 验证成功后，系统会平滑切换到新的数据源
    - 旧的数据源会等待现有连接自然关闭后再释放资源
3. 可以通过系统日志查看密码更新状态：
    - 更新成功：输出 "Successfully updated datasource with new password"
    - 更新失败：输出具体的错误信息

注意事项：

- 密码更新过程是异步的，不会影响现有业务连接
- 确保新密码正确，错误的密码会导致新连接池创建失败，系统会自动回滚到旧的数据源。
- 建议在业务低峰期进行密码更新操作

修改成功后的日志示例：

```
V1|...|Apollo-Config-5|-|-|-|-|-|-|-|com.ctrip.framework.apollo.spring.property.AutoUpdateConfigChangeListener|-|Auto update apollo changed value successfully, new value: f5GGWuGxxxxxxxx, key: spring.datasource.password, beanName: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration, field: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration$$SpringCGLIB$$0.password|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736324339473 - Starting...|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.pool.HikariPool|-|HikariPool-1736324339473 - Added connection com.mysql.cj.jdbc.ConnectionImpl@276fe793|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736324339473 - Start completed.|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|Successfully updated datasource with new password|-
V1|...|ForkJoinPool.commonPool-worker-1|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|开始等待连接关闭，当前活跃连接数: 0|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736323799576 - Shutdown initiated...|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736323799576 - Shutdown completed.|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|所有连接已关闭，数据源成功关闭|-
```

