package cn.com.chinastock.cnf.datasource;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GalaxyDynamicDataSource extends AbstractRoutingDataSource {
    private static final String CURRENT_KEY = "CURRENT";
    private final Map<Object, Object> dataSources = new ConcurrentHashMap<>();
    private String currentPoolName = "default";

    public GalaxyDynamicDataSource(DataSource defaultDataSource) {
        dataSources.put(CURRENT_KEY, defaultDataSource);
        super.setDefaultTargetDataSource(defaultDataSource);
        super.setTargetDataSources(dataSources);
        super.afterPropertiesSet();
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return CURRENT_KEY;
    }

    public void updateDataSource(DataSource newDataSource, String poolName) {
        currentPoolName = poolName;
        dataSources.put(CURRENT_KEY, newDataSource);
        super.setTargetDataSources(dataSources);
        super.afterPropertiesSet();
    }

    public String getCurrentPoolName() {
        return currentPoolName;
    }
}
