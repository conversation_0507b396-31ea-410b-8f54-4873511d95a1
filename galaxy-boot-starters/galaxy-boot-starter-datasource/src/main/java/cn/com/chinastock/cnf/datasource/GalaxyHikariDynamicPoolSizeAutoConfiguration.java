package cn.com.chinastock.cnf.datasource;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.ctrip.framework.apollo.core.dto.ApolloConfig;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 自动配置类，用于支持通过 Apollo 动态调整 Hikari 数据源的连接池大小。
 */
@Configuration
@EnableConfigurationProperties(GalaxyDataSourceProperties.class)
@ConditionalOnClass({ApolloConfig.class, HikariDataSource.class})
@ConditionalOnProperty(prefix = GalaxyDataSourceProperties.CONFIG_PREFIX, name = "dynamic-maximum-pool-size", havingValue = "true")
public class GalaxyHikariDynamicPoolSizeAutoConfiguration {
    private static final String DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE = "spring.datasource.hikari.maximum-pool-size";

    private final HikariDataSource dataSource;
    private final GalaxyDataSourceProperties properties;

    @Autowired
    public GalaxyHikariDynamicPoolSizeAutoConfiguration(HikariDataSource dataSource, GalaxyDataSourceProperties properties) {
        this.dataSource = dataSource;
        this.properties = properties;
    }

    /**
     * Apollo 配置变更监听器。
     *
     * @param changeEvent 配置变更事件
     */
    @ApolloConfigChangeListener
    public void onConfigChange(ConfigChangeEvent changeEvent) {
        if (!properties.isDynamicMaximumPoolSize()) {
            return;
        }

        changeEvent.changedKeys().stream()
                .filter(DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE::equals)
                .findFirst()
                .ifPresent(changedKey -> handlePoolSizeChange(changeEvent.getChange(changedKey).getNewValue()));
    }

    /**
     * 处理连接池大小的变更。
     *
     * @param newValue 新的连接池大小
     */
    private void handlePoolSizeChange(String newValue) {
        try {
            int newPoolSize = Integer.parseInt(newValue);
            int currentPoolSize = dataSource.getMaximumPoolSize();

            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, String.format(
                    "Updating maximum pool size from %d to %d", currentPoolSize, newPoolSize));

            dataSource.setMaximumPoolSize(newPoolSize);
        } catch (NumberFormatException e) {
            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, "Invalid maximum pool size value: " + newValue, e);
        }
    }
}
