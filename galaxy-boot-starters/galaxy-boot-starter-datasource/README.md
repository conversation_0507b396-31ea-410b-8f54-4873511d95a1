## Galaxy Boot DataSource Starter

> Galaxy Boot DataSource Starter 是一个通用的数据源 Spring Boot Starter，提供数据源的动态配置和管理功能。

### 特性

- 🚀 **通用数据源支持**：支持任何 JDBC 兼容的数据库驱动
- 🎯 **灵活的 ORM 支持**：支持 JPA，MyBatis和MyBatisPlus，使用者可自由选择
- 💾 **高性能连接池**：默认使用 HikariCP 连接池
- 🔄 **动态配置**：支持通过 Apollo 动态调整连接池大小和密码
- 📊 **监控支持**：集成 Spring Boot Actuator 监控
- 🎯 **简单易用**：依赖 Spring Boot 自动配置，无需额外配置

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Datasource Starter`，需要引入 `galaxy-boot-starter-datasource` 的 starter，并在
`application.yaml` 中配置对应的监控信息。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-datasource</artifactId>
</dependency>
```
