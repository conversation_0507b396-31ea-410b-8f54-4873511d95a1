### 组件介绍

#### 1. Tracing上下文传递

- Producer发送消息时在Header中增加tracing信息传递
- Consumer接收消息时解析tracing信息，实现分布式链路追踪

#### 2. Producer/Consumer日志记录

- Producer发送消息时记录详细日志
- Consumer接收消息时记录详细日志
- 支持动态开关和配置调整

#### 3. 幂等性处理支持

- Producer发送消息时在Header中增加幂等性ID
- Consumer接收消息后可以根据幂等性ID进行逻辑处理

#### 4. Galaxy默认配置优化

- 提供生产环境优化的默认配置
- 支持配置优先级：用户显式配置 > Galaxy默认配置 > Spring Boot默认配置

#### 5. 支持 MVC/Webflux 编程模式

- 完全兼容Spring MVC
- 完全兼容Spring WebFlux

### 使用方式

#### Galaxy配置项说明

| 配置项                                   | 类型      | 默认值   | 说明                 |
|---------------------------------------|---------|-------|--------------------|
| `galaxy.kafka.enabled`                | Boolean | false | 是否启用Galaxy Kafka功能 |
| `galaxy.kafka.log.enabled`            | Boolean | true  | 是否启用Kafka日志拦截器     |
| `galaxy.kafka.log.max-batch-detail-count` | Integer | 5     | 消费者详细日志记录的最大记录数    |


#### 基础配置示例

Galaxy Boot Starter Kafka 使用标准的Spring Boot Kafka配置。

Spring Boot Kafka 配置可以参考 [Spring Boot: Apache Kafka Support](https://docs.spring.io/spring-boot/reference/messaging/kafka.html)
Spring for Apache Kafka 的使用介绍和示例代码可以参考 [Spring for Apache Kafka ](https://docs.spring.io/spring-kafka/reference/index.html)

```yaml
# application.yml
spring:
  application:
    name: kafka-demo-app
  kafka:
    bootstrap-servers: localhost:9092

    # Producer配置
    producer:
      acks: 1
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      properties:
        request.timeout.ms: 2000
        max.block.ms: 1000
        linger.ms: 0

    # Consumer配置
    consumer:
      group-id: demo-group
      auto-offset-reset: earliest
      auto-commit-interval: 1000ms
      max-poll-records: 3100
      enable-auto-commit: true
      properties:
        session.timeout.ms: 30000
        max.poll.interval.ms: 15000
        max.partition.fetch.bytes: 15728640

    # Listener配置
    listener:
      type: batch
      concurrency: 3
      poll-timeout: 1500ms
      ack-mode: batch

# Galaxy特有功能配置
galaxy:
  kafka:
    enabled: true             # 启用Galaxy Kafka功能，默认: false
    log:
      enabled: true           # 启用日志拦截器，默认: true
      max-batch-detail-count: 5   # 详细日志记录的最大记录数，默认: 5
```


#### JAAS认证配置

使用Spring Boot标准的JAAS配置方式：

```yaml
spring:
  kafka:
    jaas:
      enabled: true
      login-module: org.apache.kafka.common.security.plain.PlainLoginModule
      control-flag: required
      options:
        username: your-username
        password: your-password
    properties:
      sasl.mechanism: PLAIN
      security.protocol: SASL_PLAINTEXT
```


### 日志拦截功能

Galaxy Boot Starter Kafka 提供了强大的日志拦截器功能，自动记录Kafka消息的发送、接收等操作，帮助开发者监控和调试Kafka应用。

#### 日志拦截配置

```yaml
galaxy:
  kafka:
    enabled: true              # 启用Galaxy Kafka功能
    log:
      enabled: true           # 启用Kafka日志拦截器
      max-batch-detail-count: 5   # 消费者批量消费时，详细日志记录的最大记录数
```

#### 日志格式说明

##### Producer发送日志

```
Kafka Producer Send: action=send topic=test-topic timestamp=null idempotent_id=ed0f33e2-d679-4108-90ac-13eeb08b0eff key=test-key value=test-value
```

**字段说明**:

- `action=send`: 操作类型为发送
- `topic=test-topic`: 目标主题
- `timestamp=null`: 消息时间戳
- `idempotent_id`: 幂等性ID，用于消息去重
- `key=test-key`: 消息键
- `value=test-value`: 消息值

##### Producer确认日志

```
Kafka Producer Ack: action=ack topic=test-topic partition=0 offset=0 status=success timestamp=0
```

**字段说明**:

- `action=ack`: 操作类型为确认
- `topic=test-topic`: 主题名称
- `partition=0`: 分区号
- `offset=0`: 消息偏移量
- `status=success`: 确认状态（success/failed）
- `timestamp=0`: 确认时间戳

##### Producer确认失败日志

```
Kafka Producer Ack Failed: action=ack status=failed error=Connection timeout
```

**字段说明**:

- `action=ack`: 操作类型为确认
- `status=failed`: 确认状态为失败
- `error=Connection timeout`: 错误信息

##### Consumer批量接收日志

```
Kafka Consumer Receive: action=consume recordCount=2 topic=topic1 partition=0 partitionRecordCount=1 topic=topic2 partition=1 partitionRecordCount=1
```

**字段说明**:

- `action=consume`: 操作类型为消费
- `recordCount=2`: 总记录数
- `topic=topic1 partition=0 partitionRecordCount=1`: 主题topic1分区0的记录数为1
- `topic=topic2 partition=1 partitionRecordCount=1`: 主题topic2分区1的记录数为1

##### Consumer详细记录日志

当消费的记录数量不超过`max-batch-detail-count`配置值时，系统会为每条记录输出详细日志：

```
Kafka Consumer Record: action=consume_detail topic=test-topic partition=0 offset=100 timestamp=1640995200000 key=test-key value=test-message
```

**字段说明**:

- `action=consume_detail`: 操作类型为详细消费记录
- `topic=test-topic`: 消息主题
- `partition=0`: 分区号
- `offset=100`: 消息偏移量
- `timestamp=1640995200000`: 消息时间戳
- `key=test-key`: 消息键（可选，如果为null则不显示）
- `value=test-message`: 消息值（可选，如果为null则不显示）

**注意**: 只有当单次消费的记录总数不超过`galaxy.kafka.log.max-batch-detail-count`
配置值时，才会输出每条记录的详细日志。这样设计是为了避免在高吞吐量场景下产生过多的日志。

### 使用示例

#### Producer示例

##### 基础Producer使用

```java

@RestController
public class MessageController {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @PostMapping("/send")
    public String sendMessage(@RequestParam String message) {
        // 发送消息到指定主题
        kafkaTemplate.send("test-topic", "key1", message);
        return "Message sent successfully";
    }

    @PostMapping("/send-with-callback")
    public String sendMessageWithCallback(@RequestParam String message) {
        // 发送消息并处理回调
        kafkaTemplate.send("test-topic", "key1", message)
                .addCallback(
                        result -> {
                            // 发送成功回调
                            RecordMetadata metadata = result.getRecordMetadata();
                            System.out.println("Message sent successfully to topic: " +
                                    metadata.topic() + ", partition: " + metadata.partition() +
                                    ", offset: " + metadata.offset());
                        },
                        failure -> {
                            // 发送失败回调
                            System.err.println("Failed to send message: " + failure.getMessage());
                        }
                );

        return "Message sent with callback";
    }
}
```

#### Consumer示例

##### 1. 基础Consumer使用

```java

@Component
public class MessageConsumer {

    @KafkaListener(topics = "test-topic", groupId = "test-group")
    public void listen(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value() +
                " from topic: " + record.topic() +
                ", partition: " + record.partition() +
                ", offset: " + record.offset());
    }
}
```

##### 2. 批量消费

```java

@Component
public class BatchMessageConsumer {

    @KafkaListener(topics = "test-topic", groupId = "batch-group")
    public void listenBatch(List<ConsumerRecord<String, String>> records) {
        System.out.println("Received batch of " + records.size() + " messages");

        for (ConsumerRecord<String, String> record : records) {
            System.out.println("Processing message: " + record.value() +
                    " from partition: " + record.partition() +
                    ", offset: " + record.offset());
        }
    }
}
```

##### 3. 手动确认模式

```java

@Component
public class ManualAckConsumer {

    @KafkaListener(topics = "test-topic", groupId = "manual-group")
    public void listenManual(List<ConsumerRecord<String, String>> records,
                             Acknowledgment ack) {
        try {
            // 处理消息
            for (ConsumerRecord<String, String> record : records) {
                // 业务逻辑处理
                processMessage(record.value());
            }

            // 手动确认
            ack.acknowledge();
        } catch (Exception e) {
            // 处理异常，不确认消息
            System.err.println("Error processing messages: " + e.getMessage());
        }
    }

    private void processMessage(String message) {
        // 业务逻辑处理
        System.out.println("Processing: " + message);
    }
}
```

### Apollo动态配置支持

Galaxy Boot Starter Kafka 支持通过Apollo配置中心进行动态配置更新，无需重启应用即可调整配置。

#### 支持的动态配置项

| 配置项                                   | 说明           | 示例值            |
|---------------------------------------|--------------|----------------|
| `galaxy.kafka.log.enabled`            | 动态启用/禁用日志拦截器 | `true`/`false` |
| `galaxy.kafka.log.max-batch-detail-count` | 动态调整详细日志记录数  | `5`            |

#### 动态配置示例

在Apollo配置中心中添加或修改以下配置：

```properties
# 动态启用日志拦截器
galaxy.kafka.log.enabled=true
# 动态调整详细日志记录数
galaxy.kafka.log.max-batch-detail-count=10
```

配置更新后，应用会自动应用新的配置，无需重启。

### 幂等性处理

Galaxy Boot Starter Kafka 提供了对消息幂等性处理的支持，通过在消息头中添加幂等性ID来实现。

#### 幂等性ID生成

Producer端在发送消息时，会为每条消息生成一个唯一的幂等性ID，并将其添加到消息头中。

#### 幂等性ID消费处理

Consumer端在消费消息时，会从消息头中提取幂等性ID，并将其设置到Redis等缓存中，方便业务代码使用。

示例代码如下：

```java
/**
 * 消费单个消息
 * 监听 single-message-topic 主题
 * @param record 消费记录
 */
@KafkaListener(topics = "single-message-topic", groupId = "single-metadata-group")
public void consumeSingleMessageWithMetadata(ConsumerRecord<String, String> record) {
    // 从消息头中提取幂等性 ID
    String idempotencyId = null;
    if (record.headers() != null) {
        org.apache.kafka.common.header.Header idempotencyHeader = record.headers().lastHeader("idempotent_id");
        if (idempotencyHeader != null) {
            idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
        }
    }

    // 从缓存（如Redis）中查询幂等性 ID 是否已经处理过


    logger.info(LogCategory.APP_LOG, 
               "Single message with metadata - Topic: {}, Partition: {}, Offset: {}, Idempotency ID: {}, Key: {}, Value: {}, Timestamp: {}",
               record.topic(), record.partition(), record.offset(), idempotencyId,
               record.key(), record.value(), record.timestamp());

    // 模拟业务处理
    processMessage(record.value(), "single-meta");

    // 设置幂等性 ID 到缓存中，表示已处理
}
```
