package cn.com.chinastock.cnf.kafka.interceptor;

import cn.com.chinastock.cnf.kafka.config.DynamicKafkaLogManager;
import cn.com.chinastock.cnf.kafka.config.GalaxyKafkaProperties;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Galaxy Kafka Consumer拦截器测试类
 * 测试Consumer拦截器的各种功能和配置场景
 */
@ExtendWith(MockitoExtension.class)
class GalaxyKafkaConsumerInterceptorTest {

    private GalaxyKafkaConsumerInterceptor<String, String> consumerInterceptor;
    private DynamicKafkaLogManager dynamicKafkaLogManager;

    @Mock
    private GalaxyKafkaProperties kafkaLogProperties;

    @BeforeEach
    void setUp() {
        // Mock GalaxyKafkaProperties.Log
        when(kafkaLogProperties.isLogEnabled()).thenReturn(true);
        when(kafkaLogProperties.getMaxBatchDetailCount()).thenReturn(5);

        // 创建动态配置管理器实例
        dynamicKafkaLogManager = new DynamicKafkaLogManager(kafkaLogProperties);

        // 重置动态配置管理器状态
        dynamicKafkaLogManager.updateLogEnabled(true);
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(5);

        consumerInterceptor = new GalaxyKafkaConsumerInterceptor<>();
        Map<String, Object> consumerConfigs = new HashMap<>();
        consumerConfigs.put("galaxy.kafka.log.enabled", true);
        consumerConfigs.put("galaxy.kafka.log.max-batch-detail-count", 5);
        consumerInterceptor.configure(consumerConfigs);
    }

    @Test
    void shouldReturnOriginalRecordsWhenOnConsumeWithEmptyRecords() {
        // 测试空记录
        ConsumerRecords<String, String> emptyRecords = new ConsumerRecords<>(Collections.emptyMap());
        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(emptyRecords);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertSame(emptyRecords, result); // 应该返回原始记录
    }

    @Test
    void shouldReturnOriginalRecordsWhenOnConsumeWithNonEmptyRecords() {
        // 创建包含记录的ConsumerRecords
        TopicPartition topicPartition = new TopicPartition("test-topic", 0);
        List<ConsumerRecord<String, String>> recordList = Arrays.asList(
                new ConsumerRecord<>("test-topic", 0, 0L, "key1", "value1"),
                new ConsumerRecord<>("test-topic", 0, 1L, "key2", "value2")
        );
        
        Map<TopicPartition, List<ConsumerRecord<String, String>>> recordsMap = new HashMap<>();
        recordsMap.put(topicPartition, recordList);
        ConsumerRecords<String, String> records = new ConsumerRecords<>(recordsMap);

        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(records);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(2, result.count());
        assertSame(records, result); // 应该返回原始记录
    }

    @Test
    void shouldHandleNullRecordsGracefully() {
        // 测试处理null记录的情况 - 拦截器会记录异常但返回null
        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(null);
        assertNull(result);
    }

    @Test
    void shouldReturnOriginalRecordsWhenInterceptorIsDisabled() {
        // 创建禁用日志的拦截器
        GalaxyKafkaConsumerInterceptor<String, String> disabledInterceptor =
                new GalaxyKafkaConsumerInterceptor<>();
        Map<String, Object> disabledConfigs = new HashMap<>();
        disabledConfigs.put("galaxy.kafka.log.enabled", false);
        disabledInterceptor.configure(disabledConfigs);

        // 测试拦截器禁用时的行为
        ConsumerRecords<String, String> emptyRecords = new ConsumerRecords<>(Collections.emptyMap());
        ConsumerRecords<String, String> result = disabledInterceptor.onConsume(emptyRecords);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertSame(emptyRecords, result);
    }

    @Test
    void shouldNotThrowExceptionWhenOnCommitWithEmptyOffsets() {
        // 测试空提交
        Map<TopicPartition, OffsetAndMetadata> emptyOffsets = Collections.emptyMap();
        assertDoesNotThrow(() -> consumerInterceptor.onCommit(emptyOffsets));
    }

    @Test
    void shouldNotThrowExceptionWhenOnCommitWithNonEmptyOffsets() {
        // 测试非空提交
        TopicPartition topicPartition = new TopicPartition("test-topic", 0);
        OffsetAndMetadata offsetAndMetadata = new OffsetAndMetadata(100L);
        Map<TopicPartition, OffsetAndMetadata> offsets = new HashMap<>();
        offsets.put(topicPartition, offsetAndMetadata);

        assertDoesNotThrow(() -> consumerInterceptor.onCommit(offsets));
    }

    @Test
    void shouldLogExceptionWhenOnCommitWithNullOffsets() {
        // 测试null提交 - 拦截器会记录异常但不抛出异常
        assertDoesNotThrow(() -> consumerInterceptor.onCommit(null));
    }

    @Test
    void shouldConfigureCorrectlyWhenConfigurationIsProvided() {
        GalaxyKafkaConsumerInterceptor<String, String> interceptor = new GalaxyKafkaConsumerInterceptor<>();

        // 测试配置
        Map<String, Object> configs = new HashMap<>();
        configs.put("galaxy.kafka.log.enabled", "true");
        configs.put("galaxy.kafka.log.max-batch-detail-count", "10");
        assertDoesNotThrow(() -> interceptor.configure(configs));

        // 创建空的ConsumerRecords进行测试
        ConsumerRecords<String, String> records = new ConsumerRecords<>(Collections.emptyMap());
        ConsumerRecords<String, String> result = interceptor.onConsume(records);
        assertSame(records, result);
    }

    @Test
    void shouldHandleEmptyConfiguration() {
        GalaxyKafkaConsumerInterceptor<String, String> interceptor = new GalaxyKafkaConsumerInterceptor<>();

        // 测试空配置
        Map<String, Object> emptyConfigs = new HashMap<>();
        assertDoesNotThrow(() -> interceptor.configure(emptyConfigs));

        ConsumerRecords<String, String> records = new ConsumerRecords<>(Collections.emptyMap());
        ConsumerRecords<String, String> result = interceptor.onConsume(records);
        assertSame(records, result);
    }

    @Test
    void shouldHandleNullConfiguration() {
        GalaxyKafkaConsumerInterceptor<String, String> interceptor = new GalaxyKafkaConsumerInterceptor<>();

        // 测试null配置 - 拦截器能够处理null配置
        assertDoesNotThrow(() -> interceptor.configure(null));

        ConsumerRecords<String, String> records = new ConsumerRecords<>(Collections.emptyMap());
        ConsumerRecords<String, String> result = interceptor.onConsume(records);
        assertSame(records, result);
    }

    @Test
    void shouldNotThrowExceptionWhenClose() {
        // 测试关闭方法不抛出异常
        assertDoesNotThrow(() -> consumerInterceptor.close());
        
        // 测试关闭后仍能正常工作
        ConsumerRecords<String, String> records = new ConsumerRecords<>(Collections.emptyMap());
        assertDoesNotThrow(() -> consumerInterceptor.onConsume(records));
    }

    @Test
    void shouldRespectDynamicConfigurationChanges() {
        // 测试动态配置变更的响应
        ConsumerRecords<String, String> records = new ConsumerRecords<>(Collections.emptyMap());

        // 启用日志
        dynamicKafkaLogManager.updateLogEnabled(true);
        ConsumerRecords<String, String> result1 = consumerInterceptor.onConsume(records);
        assertSame(records, result1);

        // 禁用日志
        dynamicKafkaLogManager.updateLogEnabled(false);
        ConsumerRecords<String, String> result2 = consumerInterceptor.onConsume(records);
        assertSame(records, result2);
    }

    @Test
    void shouldRespectMaxDetailRecordsCountConfiguration() {
        // 测试最大详细记录数配置
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(3);
        
        // 创建包含多个记录的ConsumerRecords
        TopicPartition topicPartition = new TopicPartition("test-topic", 0);
        List<ConsumerRecord<String, String>> recordList = Arrays.asList(
                new ConsumerRecord<>("test-topic", 0, 0L, "key1", "value1"),
                new ConsumerRecord<>("test-topic", 0, 1L, "key2", "value2"),
                new ConsumerRecord<>("test-topic", 0, 2L, "key3", "value3"),
                new ConsumerRecord<>("test-topic", 0, 3L, "key4", "value4"),
                new ConsumerRecord<>("test-topic", 0, 4L, "key5", "value5")
        );
        
        Map<TopicPartition, List<ConsumerRecord<String, String>>> recordsMap = new HashMap<>();
        recordsMap.put(topicPartition, recordList);
        ConsumerRecords<String, String> records = new ConsumerRecords<>(recordsMap);

        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(records);
        
        assertNotNull(result);
        assertEquals(5, result.count()); // 应该返回所有记录
        assertSame(records, result);
    }

    @Test
    void shouldHandleRecordsWithNullValues() {
        // 测试处理包含null值的记录
        TopicPartition topicPartition = new TopicPartition("test-topic", 0);
        List<ConsumerRecord<String, String>> recordList = Arrays.asList(
                new ConsumerRecord<>("test-topic", 0, 0L, null, "value1"),
                new ConsumerRecord<>("test-topic", 0, 1L, "key2", null),
                new ConsumerRecord<>("test-topic", 0, 2L, null, null)
        );
        
        Map<TopicPartition, List<ConsumerRecord<String, String>>> recordsMap = new HashMap<>();
        recordsMap.put(topicPartition, recordList);
        ConsumerRecords<String, String> records = new ConsumerRecords<>(recordsMap);

        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(records);
        
        assertNotNull(result);
        assertEquals(3, result.count());
        assertSame(records, result);
    }

    @Test
    void shouldHandleMultipleTopicPartitions() {
        // 测试处理多个主题分区的记录
        TopicPartition tp1 = new TopicPartition("topic1", 0);
        TopicPartition tp2 = new TopicPartition("topic2", 1);

        List<ConsumerRecord<String, String>> records1 = List.of(
                new ConsumerRecord<>("topic1", 0, 0L, "key1", "value1")
        );
        List<ConsumerRecord<String, String>> records2 = List.of(
                new ConsumerRecord<>("topic2", 1, 0L, "key2", "value2")
        );

        Map<TopicPartition, List<ConsumerRecord<String, String>>> recordsMap = new HashMap<>();
        recordsMap.put(tp1, records1);
        recordsMap.put(tp2, records2);
        ConsumerRecords<String, String> records = new ConsumerRecords<>(recordsMap);

        ConsumerRecords<String, String> result = consumerInterceptor.onConsume(records);

        assertNotNull(result);
        assertEquals(2, result.count());
        assertSame(records, result);
    }

}
