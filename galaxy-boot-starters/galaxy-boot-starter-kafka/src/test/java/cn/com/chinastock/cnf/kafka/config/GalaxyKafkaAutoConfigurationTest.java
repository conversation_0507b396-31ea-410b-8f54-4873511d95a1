package cn.com.chinastock.cnf.kafka.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * GalaxyKafkaAutoConfiguration测试类
 */
class GalaxyKafkaAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                KafkaAutoConfiguration.class,
                GalaxyKafkaAutoConfiguration.class
            ));

    @Test
    void testAutoConfigurationDisabledByDefault() {
        contextRunner.run(context -> {
            // Galaxy功能默认关闭，但Spring Kafka的Bean应该存在
            assertThat(context).hasSingleBean(KafkaTemplate.class);
            assertThat(context).hasSingleBean(ProducerFactory.class);
            assertThat(context).hasSingleBean(ConsumerFactory.class);
        });
    }

    @Test
    void shouldEnableGalaxyFeaturesWhenEnabled() {
        contextRunner
                .withPropertyValues("galaxy.kafka.enabled=true")
                .run(context -> {
                    // 验证Galaxy功能被启用
                    assertThat(context).hasSingleBean(GalaxyKafkaAutoConfiguration.class);
                    assertThat(context).hasSingleBean(GalaxyKafkaProperties.class);
                    
                    // 验证Spring Kafka Bean存在
                    assertThat(context).hasSingleBean(KafkaTemplate.class);
                    assertThat(context).hasSingleBean(ProducerFactory.class);
                    assertThat(context).hasSingleBean(ConsumerFactory.class);
                });
    }

    @Test
    void shouldUseSpringKafkaStandardConfiguration() {
        contextRunner
                .withPropertyValues(
                    "galaxy.kafka.enabled=true",
                    "spring.kafka.bootstrap-servers=localhost:9092",
                    "spring.kafka.producer.acks=all",
                    "spring.kafka.consumer.group-id=test-group",
                    "spring.kafka.listener.concurrency=5"
                )
                .run(context -> {
                    // 验证Spring配置被正确应用
                    assertThat(context).hasSingleBean(KafkaTemplate.class);
                    assertThat(context).hasSingleBean(ProducerFactory.class);
                    assertThat(context).hasSingleBean(ConsumerFactory.class);
                });
    }

    @Test
    void shouldApplyGalaxyDefaultValues() {
        contextRunner
                .withPropertyValues(
                    "galaxy.kafka.enabled=true",
                    "spring.kafka.bootstrap-servers=localhost:9092"
                )
                .run(context -> {
                    // 验证Galaxy Kafka自动配置存在
                    assertThat(context).hasSingleBean(GalaxyKafkaAutoConfiguration.class);

                    // 验证基本功能正常
                    assertThat(context).hasSingleBean(KafkaTemplate.class);
                    assertThat(context).hasSingleBean(ProducerFactory.class);
                    assertThat(context).hasSingleBean(ConsumerFactory.class);
                });
    }

    @Test
    void shouldConfigureGalaxyLogFeatures() {
        contextRunner
                .withPropertyValues(
                    "galaxy.kafka.enabled=true",
                    "galaxy.kafka.log.enabled=true",
                    "galaxy.kafka.log.max-batch-detail-count=20"
                )
                .run(context -> {
                    // 验证日志配置
                    GalaxyKafkaProperties kafkaProperties = context.getBean(GalaxyKafkaProperties.class);
                    assertThat(kafkaProperties.isLogEnabled()).isTrue();
                    assertThat(kafkaProperties.getMaxBatchDetailCount()).isEqualTo(20);

                    // 验证动态日志管理器
                    assertThat(context).hasSingleBean(DynamicKafkaLogManager.class);
                });
    }

    @Test
    void shouldRespectUserExplicitConfiguration() {
        contextRunner
                .withPropertyValues(
                    "galaxy.kafka.enabled=true",
                    "spring.kafka.bootstrap-servers=localhost:9092",
                    "spring.kafka.producer.acks=1",  // 用户显式配置，应该覆盖Galaxy默认值
                    "spring.kafka.consumer.auto-offset-reset=latest",  // 用户显式配置
                    "spring.kafka.listener.concurrency=5"  // 用户显式配置
                )
                .run(context -> {
                    // 验证用户配置优先级最高
                    assertThat(context).hasSingleBean(KafkaTemplate.class);
                    assertThat(context).hasSingleBean(ProducerFactory.class);
                    assertThat(context).hasSingleBean(ConsumerFactory.class);
                });
    }
}
