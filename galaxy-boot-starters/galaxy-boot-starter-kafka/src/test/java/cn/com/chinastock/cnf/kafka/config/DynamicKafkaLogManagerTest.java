package cn.com.chinastock.cnf.kafka.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * DynamicKafkaLogManager测试类
 */
class DynamicKafkaLogManagerTest {

    private DynamicKafkaLogManager dynamicKafkaLogManager;

    @Mock
    private GalaxyKafkaProperties kafkaLogProperties;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        dynamicKafkaLogManager = new DynamicKafkaLogManager(kafkaLogProperties);

        // 重置静态状态到默认值
        dynamicKafkaLogManager.updateLogEnabled(true);
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(GalaxyKafkaProperties.DEFAULT_MAX_DETAIL_RECORDS_COUNT);

        // 使用反射设置kafkaLogProperties字段
        try {
            java.lang.reflect.Field field = DynamicKafkaLogManager.class.getDeclaredField("kafkaLogProperties");
            field.setAccessible(true);
            field.set(dynamicKafkaLogManager, kafkaLogProperties);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void shouldReturnDefaultValuesWhenNotConfigured() {
        // 测试默认值
        assertFalse(DynamicKafkaLogManager.isLogDisabled());
        assertEquals(5, DynamicKafkaLogManager.getMaxDetailRecordsCount());
    }

    @Test
    void shouldUpdateLogEnabledWhenValueIsChanged() {
        // 测试动态更新日志启用状态
        dynamicKafkaLogManager.updateLogEnabled(false);
        assertTrue(DynamicKafkaLogManager.isLogDisabled());

        dynamicKafkaLogManager.updateLogEnabled(true);
        assertFalse(DynamicKafkaLogManager.isLogDisabled());
    }

    @Test
    void shouldUpdateMaxDetailRecordsCountWhenValueIsChanged() {
        // 测试动态更新最大详细记录数
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(10);
        assertEquals(10, DynamicKafkaLogManager.getMaxDetailRecordsCount());

        dynamicKafkaLogManager.updateMaxDetailRecordsCount(20);
        assertEquals(20, DynamicKafkaLogManager.getMaxDetailRecordsCount());
    }

    @Test
    void shouldSyncCorrectlyWhenPropertiesAreProvided() {
        // 模拟配置属性
        when(kafkaLogProperties.isLogEnabled()).thenReturn(false);
        when(kafkaLogProperties.getMaxBatchDetailCount()).thenReturn(15);

        // 同步配置
        dynamicKafkaLogManager.syncFromProperties();

        // 验证同步结果
        assertTrue(DynamicKafkaLogManager.isLogDisabled());
        assertEquals(15, DynamicKafkaLogManager.getMaxDetailRecordsCount());
    }

    @Test
    void shouldNotThrowExceptionWhenPropertiesAreNull() {
        // 设置kafkaLogProperties为null
        try {
            java.lang.reflect.Field field = DynamicKafkaLogManager.class.getDeclaredField("kafkaLogProperties");
            field.setAccessible(true);
            field.set(dynamicKafkaLogManager, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 同步配置不应该抛出异常
        assertDoesNotThrow(() -> dynamicKafkaLogManager.syncFromProperties());
    }

    @Test
    void shouldReturnCorrectStatusWhenConfigurationIsSet() {
        // 设置特定的配置值
        dynamicKafkaLogManager.updateLogEnabled(true);
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(8);

        String status = dynamicKafkaLogManager.getConfigStatus();
        assertEquals("KafkaLog[enabled=true, maxDetailRecords=8]", status);

        // 更改配置
        dynamicKafkaLogManager.updateLogEnabled(false);
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(12);

        status = dynamicKafkaLogManager.getConfigStatus();
        assertEquals("KafkaLog[enabled=false, maxDetailRecords=12]", status);
    }

    @Test
    void shouldBeThreadSafeWhenConcurrentAccess() throws InterruptedException {
        // 测试并发访问的线程安全性
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];

        // 创建多个线程同时更新配置
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                dynamicKafkaLogManager.updateLogEnabled(index % 2 == 0);
                dynamicKafkaLogManager.updateMaxDetailRecordsCount(index + 1);
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证最终状态是有效的（不会抛出异常）
        assertDoesNotThrow(() -> {
            String status = dynamicKafkaLogManager.getConfigStatus();

            // 验证状态字符串格式正确
            assertTrue(status.startsWith("KafkaLog[enabled="));
            assertTrue(status.contains("maxDetailRecords="));
            assertTrue(status.endsWith("]"));

            // 验证静态方法可以正常调用
            DynamicKafkaLogManager.isLogDisabled();
            DynamicKafkaLogManager.getMaxDetailRecordsCount();
        });
    }
}
