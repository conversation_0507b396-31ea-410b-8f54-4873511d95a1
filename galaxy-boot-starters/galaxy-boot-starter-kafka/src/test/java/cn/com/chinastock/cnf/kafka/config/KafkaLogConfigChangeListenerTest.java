package cn.com.chinastock.cnf.kafka.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * KafkaLogConfigChangeListener测试类
 * 验证Apollo配置变更监听器的正确性，特别是时机问题的修复
 */
@ExtendWith(MockitoExtension.class)
class KafkaLogConfigChangeListenerTest {

    @Mock
    private DynamicKafkaLogManager dynamicKafkaLogManager;

    @Mock
    private ConfigChangeEvent configChangeEvent;

    @Mock
    private Config apolloConfig;

    private KafkaLogConfigChangeListener listener;

    @BeforeEach
    void setUp() {
        listener = new KafkaLogConfigChangeListener(dynamicKafkaLogManager);
    }

    @Test
    void shouldHandleKafkaLogConfigChangeCorrectly() {
        // 模拟Kafka日志相关的配置变更
        when(configChangeEvent.changedKeys()).thenReturn(Set.of("galaxy.kafka.log.enabled"));
        when(apolloConfig.getBooleanProperty("galaxy.kafka.log.enabled", true)).thenReturn(false);
        when(apolloConfig.getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5)).thenReturn(10);

        try (MockedStatic<ConfigService> configServiceMock = mockStatic(ConfigService.class)) {
            configServiceMock.when(ConfigService::getAppConfig).thenReturn(apolloConfig);

            // 调用onChange方法
            listener.onChange(configChangeEvent);

            // 验证直接从Apollo配置读取并更新动态管理器
            verify(apolloConfig).getBooleanProperty("galaxy.kafka.log.enabled", true);
            verify(apolloConfig).getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5);
            verify(dynamicKafkaLogManager).updateLogEnabled(false);
            verify(dynamicKafkaLogManager).updateMaxDetailRecordsCount(10);
            verify(dynamicKafkaLogManager).getConfigStatus();
        }
    }

    @Test
    void shouldSkipNonKafkaLogConfigChanges() {
        // 模拟非Kafka日志相关的配置变更
        when(configChangeEvent.changedKeys()).thenReturn(Set.of("other.config.key", "another.setting"));

        // 调用onChange方法
        listener.onChange(configChangeEvent);

        // 验证没有调用动态管理器的更新方法
        verify(dynamicKafkaLogManager, never()).updateLogEnabled(anyBoolean());
        verify(dynamicKafkaLogManager, never()).updateMaxDetailRecordsCount(anyInt());
        verify(dynamicKafkaLogManager, never()).getConfigStatus();
    }

    @Test
    void shouldHandleMultipleKafkaLogConfigChanges() {
        // 模拟多个Kafka日志配置同时变更
        when(configChangeEvent.changedKeys()).thenReturn(Set.of(
                "galaxy.kafka.log.enabled", 
                "galaxy.kafka.log.max-batch-detail-count",
                "other.config.key"
        ));
        when(apolloConfig.getBooleanProperty("galaxy.kafka.log.enabled", true)).thenReturn(true);
        when(apolloConfig.getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5)).thenReturn(20);

        try (MockedStatic<ConfigService> configServiceMock = mockStatic(ConfigService.class)) {
            configServiceMock.when(ConfigService::getAppConfig).thenReturn(apolloConfig);

            // 调用onChange方法
            listener.onChange(configChangeEvent);

            // 验证正确处理了Kafka日志配置变更
            verify(dynamicKafkaLogManager).updateLogEnabled(true);
            verify(dynamicKafkaLogManager).updateMaxDetailRecordsCount(20);
            verify(dynamicKafkaLogManager).getConfigStatus();
        }
    }

    @Test
    void shouldInitializeFromApolloConfigOnStartup() {
        // 模拟Apollo配置
        when(apolloConfig.getBooleanProperty("galaxy.kafka.log.enabled", true)).thenReturn(false);
        when(apolloConfig.getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5)).thenReturn(15);

        try (MockedStatic<ConfigService> configServiceMock = mockStatic(ConfigService.class)) {
            configServiceMock.when(ConfigService::getAppConfig).thenReturn(apolloConfig);

            // 调用初始化方法
            listener.initialize();

            // 验证初始化时从Apollo配置读取并更新动态管理器
            verify(apolloConfig).getBooleanProperty("galaxy.kafka.log.enabled", true);
            verify(apolloConfig).getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5);
            verify(dynamicKafkaLogManager).updateLogEnabled(false);
            verify(dynamicKafkaLogManager).updateMaxDetailRecordsCount(15);
            verify(dynamicKafkaLogManager).getConfigStatus();
        }
    }

    @Test
    void shouldFallbackToSyncFromPropertiesWhenApolloFails() {
        // 模拟Apollo配置读取失败
        try (MockedStatic<ConfigService> configServiceMock = mockStatic(ConfigService.class)) {
            // 第一次调用用于获取config对象，第二次调用在initializeFromApolloConfig中失败
            configServiceMock.when(ConfigService::getAppConfig)
                    .thenReturn(apolloConfig)  // 第一次调用成功
                    .thenThrow(new RuntimeException("Apollo connection failed"));  // 第二次调用失败

            // 模拟Apollo配置读取失败
            when(apolloConfig.getBooleanProperty("galaxy.kafka.log.enabled", true))
                    .thenThrow(new RuntimeException("Apollo connection failed"));

            // 调用初始化方法
            listener.initialize();

            // 验证回退到syncFromProperties
            verify(dynamicKafkaLogManager).syncFromProperties();
        }
    }

    @Test
    void shouldHandleExceptionInOnChangeGracefully() {
        // 模拟配置变更事件
        when(configChangeEvent.changedKeys()).thenReturn(Set.of("galaxy.kafka.log.enabled"));

        try (MockedStatic<ConfigService> configServiceMock = mockStatic(ConfigService.class)) {
            configServiceMock.when(ConfigService::getAppConfig).thenThrow(new RuntimeException("Apollo error"));

            // 调用onChange方法，应该不抛出异常
            listener.onChange(configChangeEvent);

            // 验证没有调用动态管理器的更新方法（因为异常被捕获）
            verify(dynamicKafkaLogManager, never()).updateLogEnabled(anyBoolean());
            verify(dynamicKafkaLogManager, never()).updateMaxDetailRecordsCount(anyInt());
        }
    }
}
