package cn.com.chinastock.cnf.kafka.utils;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;

import java.nio.charset.StandardCharsets;
import java.util.Map;

public final class KafkaLogUtils {

    private KafkaLogUtils() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static <K, V> StringBuilder constructConsumerLogMessage(ConsumerRecords<K, V> records) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=consume");
        logMessage.append(" recordCount=").append(records.count());

        // 记录每个分区的消息数量
        for (TopicPartition partition : records.partitions()) {
            int partitionRecordCount = records.records(partition).size();
            logMessage.append(" topic=").append(partition.topic())
                    .append(" partition=").append(partition.partition())
                    .append(" partitionRecordCount=").append(partitionRecordCount);
        }
        return logMessage;
    }

    public static <K, V> StringBuilder constructConsumerDetailLogMessage(ConsumerRecord<K, V> record) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=consume_detail");
        logMessage.append(" topic=").append(record.topic());
        logMessage.append(" partition=").append(record.partition());
        logMessage.append(" offset=").append(record.offset());
        logMessage.append(" timestamp=").append(record.timestamp());

        // 添加幂等性 ID（在 key 和 value 之前）
        if (record.headers() != null) {
            Header idempotencyHeader = record.headers().lastHeader(IdempotencyUtils.IDEMPOTENCY_ID);
            if (idempotencyHeader != null) {
                String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
                logMessage.append(" idempotent_id=").append(idempotencyId);
            }
        }

        if (record.key() != null) {
            logMessage.append(" key=").append(record.key());
        }

        if (record.value() != null) {
            logMessage.append(" value=").append(record.value().toString());
        }
        return logMessage;
    }

    public static StringBuilder constructConsumerCommitLogMessage(Map<TopicPartition, OffsetAndMetadata> offsets) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=commit");
        logMessage.append(" partitionCount=").append(offsets.size());

        // 记录每个分区的偏移量信息
        for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : offsets.entrySet()) {
            TopicPartition partition = entry.getKey();
            OffsetAndMetadata offsetMetadata = entry.getValue();

            logMessage.append(" topic=").append(partition.topic())
                    .append(" partition=").append(partition.partition())
                    .append(" offset=").append(offsetMetadata.offset());
        }
        return logMessage;
    }

    public static <K, V> StringBuilder constructProducerSendLogMessage(ProducerRecord<K, V> record) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=send");
        logMessage.append(" topic=").append(record.topic());
        if (record.partition() != null) {
            logMessage.append(" partition=").append(record.partition());
        }
        logMessage.append(" timestamp=").append(record.timestamp());

        // 添加幂等性 ID（在 key 和 value 之前）
        if (record.headers() != null) {
            Header idempotencyHeader = record.headers().lastHeader(IdempotencyUtils.IDEMPOTENCY_ID);
            if (idempotencyHeader != null) {
                String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
                logMessage.append(" idempotent_id=").append(idempotencyId);
            }
        }

        if (record.key() != null) {
            logMessage.append(" key=").append(record.key());
        }

        if (record.value() != null) {
            logMessage.append(" value=").append(record.value().toString());
        }

        return logMessage;
    }

    public static StringBuilder constructProducerAckFailedLogMessage(Exception exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=ack");
        logMessage.append(" status=failed");
        logMessage.append(" error=").append(exception.getMessage());
        return logMessage;
    }

    public static StringBuilder constructProducerAckLogMessage(RecordMetadata metadata) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("action=ack");
        logMessage.append(" topic=").append(metadata.topic());
        logMessage.append(" partition=").append(metadata.partition());
        logMessage.append(" offset=").append(metadata.offset());
        logMessage.append(" status=success");
        logMessage.append(" timestamp=").append(metadata.timestamp());
        return logMessage;
    }
}
