package cn.com.chinastock.cnf.kafka.utils;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * 幂等性工具类
 * 提供幂等性 ID 的提取、验证和处理功能
 *
 * <AUTHOR>
 */
public class IdempotencyUtils {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(IdempotencyUtils.class);

    // 幂等性 ID 的 key
    public static final String IDEMPOTENCY_ID = "idempotent_id";

    /**
     * 从 ConsumerRecord 中提取幂等性 ID
     *
     * @param record Kafka 消费记录
     * @return 幂等性 ID，如果不存在则返回 Optional.empty()
     */
    public static Optional<String> extractIdempotencyId(ConsumerRecord<?, ?> record) {
        if (record == null || record.headers() == null) {
            return Optional.empty();
        }

        try {
            Header idempotencyHeader = record.headers().lastHeader(IDEMPOTENCY_ID);
            if (idempotencyHeader != null) {
                String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
                return Optional.of(idempotencyId);
            }
        } catch (Exception e) {
            logger.debug(LogCategory.FRAMEWORK_LOG, "Failed to extract idempotency ID from record", e);
        }

        return Optional.empty();
    }
}
