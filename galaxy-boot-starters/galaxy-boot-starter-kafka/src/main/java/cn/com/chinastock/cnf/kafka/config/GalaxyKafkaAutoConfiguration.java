package cn.com.chinastock.cnf.kafka.config;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaConsumerInterceptor;
import cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaProducerInterceptor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Map;

/**
 * Galaxy Kafka 自动配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "galaxy.kafka", name = "enabled", havingValue = "true")
@AutoConfigureAfter(KafkaAutoConfiguration.class)
@EnableKafka
@EnableConfigurationProperties({GalaxyKafkaProperties.class})
@Import({DynamicKafkaLogManager.class, KafkaLogConfigChangeListener.class})
public class GalaxyKafkaAutoConfiguration implements BeanPostProcessor {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(GalaxyKafkaAutoConfiguration.class);

    private final GalaxyKafkaProperties galaxyKafkaProperties;
    private final Environment environment;
    private volatile boolean configured = false;

    public GalaxyKafkaAutoConfiguration(GalaxyKafkaProperties galaxyKafkaProperties, Environment environment) {
        this.galaxyKafkaProperties = galaxyKafkaProperties;
        this.environment = environment;
        logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing Galaxy Kafka AutoConfiguration, logEnabled={}, maxBatchDetailCount={}",
                this.galaxyKafkaProperties.getLog().isEnabled(), this.galaxyKafkaProperties.getLog().getMaxBatchDetailCount());
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof KafkaProperties && !configured) {
            configureGalaxyDefaults((KafkaProperties) bean);
            configured = true;
        }
        return bean;
    }

    /**
     * 配置Galaxy期望的默认值到Spring Kafka配置中
     *
     * @param kafkaProperties Spring Boot的Kafka配置属性
     */
    private void configureGalaxyDefaults(KafkaProperties kafkaProperties) {
        logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Applying Galaxy default values to Spring Kafka configuration");

        applyProducerDefaults(kafkaProperties);
        applyConsumerDefaults(kafkaProperties);
        applyTemplateDefaults(kafkaProperties);
        applyListenerDefaults(kafkaProperties);

        logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Galaxy default values applied successfully");
    }

    /**
     * 应用Galaxy期望的Producer默认值
     *
     * @param kafkaProperties Spring Boot的Kafka配置属性
     */
    private void applyProducerDefaults(KafkaProperties kafkaProperties) {
        KafkaProperties.Producer producer = kafkaProperties.getProducer();
        Map<String, String> properties = producer.getProperties();

        // 添加Galaxy拦截器配置
        String currentInterceptor = properties.get(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG);
        String galaxyProducerInterceptor = GalaxyKafkaProducerInterceptor.class.getName();
        if (currentInterceptor == null || currentInterceptor.isEmpty()) {
            properties.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, galaxyProducerInterceptor);
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Setting {} to Kafka Producer interceptor", galaxyProducerInterceptor);
        } else {
            properties.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, galaxyProducerInterceptor + "," + currentInterceptor);
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Updating {} to Kafka Producer interceptor", galaxyProducerInterceptor + "," + currentInterceptor);
        }
    }

    /**
     * 应用Galaxy期望的Consumer默认值
     *
     * @param kafkaProperties Spring Boot的Kafka配置属性
     */
    private void applyConsumerDefaults(KafkaProperties kafkaProperties) {
        KafkaProperties.Consumer consumer = kafkaProperties.getConsumer();
        Map<String, String> properties = consumer.getProperties();

        // 添加Galaxy拦截器配置
        String currentInterceptor = properties.get(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG);
        String galaxyConsumerInterceptor = GalaxyKafkaConsumerInterceptor.class.getName();
        if (currentInterceptor == null || currentInterceptor.isEmpty()) {
            properties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, galaxyConsumerInterceptor);
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Setting {} to Kafka Consumer interceptor", galaxyConsumerInterceptor);
        } else {
            properties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, galaxyConsumerInterceptor + "," + currentInterceptor);
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Updating {} to Kafka Consumer interceptor", galaxyConsumerInterceptor + "," + currentInterceptor);
        }
    }

    /**
     * 应用Galaxy期望的Template默认值
     *
     * @param kafkaProperties Spring Boot的Kafka配置属性
     */
    private void applyTemplateDefaults(KafkaProperties kafkaProperties) {
        KafkaProperties.Template template = kafkaProperties.getTemplate();

        applyGalaxyDefaultIfNeeded(
                template.isObservationEnabled(),
                KafkaDefaultConfig.OBSERVATION_ENABLED,
                "spring.kafka.template.observation-enabled",
                () -> template.setObservationEnabled(KafkaDefaultConfig.OBSERVATION_ENABLED)
        );
    }

    /**
     * 应用Galaxy期望的Listener默认值
     *
     * @param kafkaProperties Spring Boot的Kafka配置属性
     */
    private void applyListenerDefaults(KafkaProperties kafkaProperties) {
        KafkaProperties.Listener listener = kafkaProperties.getListener();

        applyGalaxyDefaultIfNeeded(
                listener.isObservationEnabled(),
                KafkaDefaultConfig.OBSERVATION_ENABLED,
                "spring.kafka.listener.observation-enabled",
                () -> listener.setObservationEnabled(KafkaDefaultConfig.OBSERVATION_ENABLED)
        );
    }

    /**
     * 检查用户是否显式配置了指定的属性
     *
     * @param propertyName 属性名
     * @return 如果用户显式配置了该属性则返回true，否则返回false
     */
    private boolean isUserConfigured(String propertyName) {
        // 检查Environment中是否存在该属性
        // 如果属性存在且不是默认值，说明用户显式配置了
        return environment.containsProperty(propertyName);
    }

    /**
     * 统一的配置设置逻辑：检查当前值是否与Galaxy默认值不同，如果不同且不是用户显式配置，则应用Galaxy默认值
     *
     * @param currentValue       当前配置值
     * @param galaxyDefaultValue Galaxy默认值
     * @param propertyName       属性名
     * @param setter             设置方法
     * @param <T>                配置值类型
     * @return 是否应用了Galaxy默认值
     */
    private <T> boolean applyGalaxyDefaultIfNeeded(T currentValue, T galaxyDefaultValue, String propertyName,
                                                   Runnable setter) {
        // 1. 检查是否和要设置的默认值不同
        if (java.util.Objects.equals(currentValue, galaxyDefaultValue)) {
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: {} already matches Galaxy default value: [{}], skipping",
                    propertyName, galaxyDefaultValue);
            return false;
        }

        // 2. 如果和默认值不同，检查是不是用户显式设置的
        if (isUserConfigured(propertyName)) {
            logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: {} explicitly configured by user: [{}], skipping Galaxy default",
                    propertyName, currentValue);
            return false;
        }

        // 3. 如果不是用户显式设置，则修改为Galaxy的默认值
        logger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Setting {} from Spring Default Value=[{}] to Galaxy Default Value=[{}]",
                propertyName, currentValue, galaxyDefaultValue);
        setter.run();
        return true;
    }
}
