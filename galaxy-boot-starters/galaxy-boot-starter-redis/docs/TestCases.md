# Galaxy Boot Starter Redis 测试用例

## 概述

本文档定义了 Galaxy Boot Starter Redis 的完整测试用例，涵盖序列化、Redis操作、配置管理、指标监控等核心功能。

## 测试环境要求

- Redis 单机实例（用于基础功能测试）
- Redis Cluster 集群（用于集群功能测试，可选）
- Spring Boot 3.3.7+
- Java 21+

## 测试用例分类

### 1. 序列化功能测试

#### TC-001: 默认序列化器测试
**目标**: 验证默认使用 FastJson2 序列化器
**API接口**: `POST /api/redis/test/serialization/default`
**测试步骤**:
1. 存储一个复杂对象到 Redis
2. 从 Redis 读取该对象
3. 验证对象内容正确性
**预期结果**: 对象能正确序列化和反序列化

#### TC-002: 字符串序列化器测试
**目标**: 验证 String 序列化器功能
**API接口**: `POST /api/redis/test/serialization/string`
**测试步骤**:
1. 存储字符串到 Redis
2. 从 Redis 读取字符串
3. 验证内容一致性
**预期结果**: 字符串能正确存储和读取

#### TC-003: 复杂对象序列化测试
**目标**: 验证复杂对象序列化功能
**API接口**: `POST /api/redis/test/serialization/complex-object`
**测试步骤**:
1. 存储包含嵌套对象的复杂对象到 Redis
2. 从 Redis 读取对象
3. 验证对象属性完整性
**预期结果**: 复杂对象能正确序列化和反序列化

#### TC-004: 大对象序列化测试
**目标**: 验证大对象序列化性能
**API接口**: `POST /api/redis/test/serialization/large-object`
**测试步骤**:
1. 存储大容量对象到 Redis
2. 从 Redis 读取对象
3. 验证对象完整性和性能
**预期结果**: 大对象能正确序列化和反序列化

#### TC-005: 特殊字符序列化测试
**目标**: 验证特殊字符处理能力
**API接口**: `POST /api/redis/test/serialization/special-characters`
**测试步骤**:
1. 存储包含特殊字符的字符串到 Redis
2. 从 Redis 读取字符串
3. 验证特殊字符保持完整
**预期结果**: 特殊字符能正确处理

#### TC-006: 空值处理测试
**目标**: 验证空值序列化处理
**API接口**: `POST /api/redis/test/serialization/null-values`
**测试步骤**:
1. 存储包含null值的对象到 Redis
2. 从 Redis 读取对象
3. 验证null值处理正确
**预期结果**: 空值能正确处理

#### TC-007: FastJson序列化器测试
**目标**: 验证 FastJson 序列化器功能
**API接口**: `POST /api/redis/test/serialization/fastjson`
**测试步骤**:
1. 使用FastJson序列化器存储对象
2. 从 Redis 读取对象
3. 验证序列化器工作正常
**预期结果**: FastJson序列化器正常工作

### 2. Redis 操作功能测试

#### TC-008: String 操作测试
**目标**: 验证 String 类型的基本操作
**API接口**: `POST /api/redis/test/string`
**测试步骤**:
1. SET 操作存储键值对
2. GET 操作读取值
3. SETEX 操作设置带过期时间的键值对
4. TTL 操作查看剩余时间
**预期结果**: 所有 String 操作正常工作

#### TC-009: Hash 操作测试
**目标**: 验证 Hash 类型的基本操作
**API接口**: `POST /api/redis/test/hash`
**测试步骤**:
1. HSET 操作存储 Hash 字段
2. HGET 操作读取字段值
3. HGETALL 操作获取所有字段
4. HDEL 操作删除字段
**预期结果**: 所有 Hash 操作正常工作

#### TC-010: List 操作测试
**目标**: 验证 List 类型的基本操作
**API接口**: `POST /api/redis/test/list`
**测试步骤**:
1. LPUSH 操作添加元素到列表头部
2. RPUSH 操作添加元素到列表尾部
3. LPOP 操作从头部弹出元素
4. LRANGE 操作获取范围内元素
**预期结果**: 所有 List 操作正常工作

#### TC-011: Set 操作测试
**目标**: 验证 Set 类型的基本操作
**API接口**: `POST /api/redis/test/set`
**测试步骤**:
1. SADD 操作添加元素到集合
2. SMEMBERS 操作获取所有成员
3. SISMEMBER 操作检查成员存在性
4. SREM 操作删除成员
**预期结果**: 所有 Set 操作正常工作

#### TC-012: ZSet 操作测试
**目标**: 验证 ZSet 类型的基本操作
**API接口**: `POST /api/redis/test/zset`
**测试步骤**:
1. ZADD 操作添加带分数的元素
2. ZRANGE 操作按分数范围获取元素
3. ZSCORE 操作获取元素分数
4. ZREM 操作删除元素
**预期结果**: 所有 ZSet 操作正常工作

#### TC-013: 管道操作测试
**目标**: 验证 Redis 管道操作功能
**API接口**: `POST /api/redis/test/pipeline`
**测试步骤**:
1. 创建管道批量操作
2. 执行多个命令
3. 获取批量执行结果
**预期结果**: 管道操作能提高批量操作性能

### 3. 配置管理测试

#### TC-014: Redis配置查询测试
**目标**: 验证Redis配置信息获取
**API接口**: `GET /api/redis/config`
**测试步骤**:
1. 获取当前Redis配置信息
2. 验证配置项完整性
3. 检查配置值正确性
**预期结果**: 能正确获取Redis配置信息

#### TC-015: Redis健康状态测试
**目标**: 验证Redis健康状态检查
**API接口**: `GET /api/redis/health`
**测试步骤**:
1. 获取Redis健康状态
2. 验证连接状态
3. 检查响应时间
**预期结果**: 能正确反映Redis健康状态

#### TC-016: 连接池状态测试
**目标**: 验证连接池状态监控
**API接口**: `GET /api/redis/connection-pool`
**测试步骤**:
1. 获取连接池状态信息
2. 验证活跃连接数
3. 检查连接池配置
**预期结果**: 能正确获取连接池状态

#### TC-017: Redis服务器信息测试
**目标**: 验证Redis服务器信息获取
**API接口**: `GET /api/redis/server-info`
**测试步骤**:
1. 获取Redis服务器信息
2. 验证服务器版本
3. 检查内存使用情况
**预期结果**: 能正确获取服务器信息

#### TC-018: Redis连接测试
**目标**: 验证Redis连接功能
**API接口**: `POST /api/redis/test-connection`
**测试步骤**:
1. 执行连接测试
2. 验证连接响应时间
3. 检查连接稳定性
**预期结果**: 连接测试成功

#### TC-019: Galaxy Redis配置测试
**目标**: 验证Galaxy Redis增强配置
**API接口**: `GET /api/redis/galaxy-config`
**测试步骤**:
1. 获取Galaxy Redis配置详情
2. 验证序列化器配置
3. 检查增强功能配置
**预期结果**: 能正确获取Galaxy配置信息

#### TC-020: 配置有效性测试
**目标**: 验证配置变更的有效性
**API接口**: `POST /api/redis/test-config`
**测试步骤**:
1. 测试当前配置有效性
2. 验证配置项生效情况
3. 检查配置一致性
**预期结果**: 配置有效且生效

### 4. 综合测试

#### TC-021: 全量测试执行
**目标**: 执行所有测试用例
**API接口**: `POST /api/redis/test/all`
**测试步骤**:
1. 运行所有序列化测试
2. 运行所有Redis操作测试
3. 运行所有配置测试
4. 生成测试报告
**预期结果**: 所有测试通过

#### TC-022: 快速健康检查
**目标**: 快速验证系统健康状态
**API接口**: `GET /api/redis/test/quick-health`
**测试步骤**:
1. 执行基础连接测试
2. 执行简单操作测试
3. 执行序列化测试
**预期结果**: 系统健康状态良好

#### TC-023: 性能基准测试
**目标**: 执行性能基准测试
**API接口**: `POST /api/redis/test/benchmark`
**测试步骤**:
1. 执行序列化性能测试
2. 执行并发操作测试
3. 生成性能报告
**预期结果**: 性能指标达到预期

#### TC-024: 测试报告生成
**目标**: 生成详细测试报告
**API接口**: `GET /api/redis/test/report`
**测试步骤**:
1. 收集配置信息
2. 收集服务器信息
3. 收集连接池状态
4. 生成综合报告
**预期结果**: 生成完整测试报告

## API 测试接口设计

为支持上述测试用例，设计以下 API 接口：

### 1. 序列化测试接口（TC-001 ~ TC-007）
- `POST /api/redis/test/serialization/default` - TC-001: 默认序列化器测试
- `POST /api/redis/test/serialization/string` - TC-002: 字符串序列化器测试
- `POST /api/redis/test/serialization/complex-object` - TC-003: 复杂对象序列化测试
- `POST /api/redis/test/serialization/large-object` - TC-004: 大对象序列化测试
- `POST /api/redis/test/serialization/special-characters` - TC-005: 特殊字符序列化测试
- `POST /api/redis/test/serialization/null-values` - TC-006: 空值处理测试
- `POST /api/redis/test/serialization/fastjson` - TC-007: FastJson序列化器测试
- `GET /api/redis/test/serialization/config` - 获取当前序列化器配置
- `POST /api/redis/test/serialization/performance` - 序列化性能测试
- `POST /api/redis/test/serialization/all` - 运行所有序列化测试

### 2. Redis 操作测试接口（TC-008 ~ TC-013）
- `POST /api/redis/test/string` - TC-008: String 操作测试
- `POST /api/redis/test/hash` - TC-009: Hash 操作测试
- `POST /api/redis/test/list` - TC-010: List 操作测试
- `POST /api/redis/test/set` - TC-011: Set 操作测试
- `POST /api/redis/test/zset` - TC-012: ZSet 操作测试
- `POST /api/redis/test/pipeline` - TC-013: 管道操作测试
- `POST /api/redis/test/operations/all` - 运行所有Redis操作测试
- `POST /api/redis/test/concurrent` - 并发操作测试

### 3. 配置管理测试接口（TC-014 ~ TC-020）
- `GET /api/redis/config` - TC-014: Redis配置查询测试
- `GET /api/redis/health` - TC-015: Redis健康状态测试
- `GET /api/redis/connection-pool` - TC-016: 连接池状态测试
- `GET /api/redis/server-info` - TC-017: Redis服务器信息测试
- `POST /api/redis/test-connection` - TC-018: Redis连接测试
- `GET /api/redis/galaxy-config` - TC-019: Galaxy Redis配置测试
- `POST /api/redis/test-config` - TC-020: 配置有效性测试

### 4. 综合测试接口（TC-021 ~ TC-024）
- `POST /api/redis/test/all` - TC-021: 全量测试执行
- `GET /api/redis/test/quick-health` - TC-022: 快速健康检查
- `POST /api/redis/test/benchmark` - TC-023: 性能基准测试
- `GET /api/redis/test/report` - TC-024: 测试报告生成

## 测试数据

### 测试对象定义
```java
public class TestUser {
    private Long id;
    private String name;
    private String email;
    private LocalDateTime createTime;
    private List<String> tags;
    private Map<String, Object> metadata;
    // getters and setters
}
```

### 测试场景数据
- 简单字符串: "Hello Galaxy Redis"
- 复杂对象: TestUser 实例
- 大对象: 包含大量数据的对象
- 特殊字符: 包含中文、特殊符号的字符串
- 空值处理: null 值测试

## 测试执行指南

### 快速验证
```bash
# 1. 快速健康检查
curl http://localhost:8080/api/redis/test/quick-health

# 2. 运行所有测试
curl -X POST http://localhost:8080/api/redis/test/all

# 3. 查看测试报告
curl http://localhost:8080/api/redis/test/report
```

### 分类测试
```bash
# 序列化测试
curl -X POST http://localhost:8080/api/redis/test/serialization/all

# Redis操作测试
curl -X POST http://localhost:8080/api/redis/test/operations/all

# 性能基准测试
curl -X POST "http://localhost:8080/api/redis/test/benchmark?iterations=1000&threads=10"
```

## 验证标准

1. **功能正确性**: 所有测试用例返回 success=true
2. **性能要求**: FastJson 序列化性能优于其他序列化器
3. **稳定性要求**: 高并发测试无异常和错误
4. **配置有效性**: 配置变更能正确生效并反映在测试结果中
5. **健康状态**: 所有健康检查项目状态为 UP
