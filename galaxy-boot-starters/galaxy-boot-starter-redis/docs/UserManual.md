
### 组件配置说明

#### 1 序列化配置

```yaml
galaxy:
  redis:
    serialization:
      key-serializer: string        # Key 序列化器类型
      value-serializer: fastjson    # Value 序列化器类型
      hash-key-serializer: string   # Hash Key 序列化器类型
      hash-value-serializer: fastjson # Hash Value 序列化器类型
```

**序列化器类型说明：**

- `string`: String 序列化器，适用于简单字符串
- `fastjson`: FastJson2 序列化器，性能最优，推荐用于对象序列化
- `jackson`: <PERSON> 序列化器，兼容性好
- `jdk`: JDK 序列化器，不推荐使用

#### 2 Redis部署模式

组件采用 `Spring Boot Starter Data Redis` 默认支持方式。
更多`Spring Boot Starter Data Redis`的配置说明请参考[官方文档](https://spring.io/projects/spring-data-redis)。

##### 单机模式

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: your-password
      database: 0
      timeout: 2s
      connect-timeout: 2s
```

##### 哨兵模式

```yaml
spring:
  data:
    redis:
       password: redis-password
       sentinel:
         master: mymaster
         nodes:
         - ************:26379
         - ************:26379
         - ************:26379
         password: sentinel-password
```

##### 集群模式

```yaml
spring:
  data:
    redis:
      cluster:
        nodes:
          - ************:7000
          - ************:7001
          - ************:7002
          - ************:7000
          - ************:7001
          - ************:7002
        max-redirects: 3
      password: cluster-password
```

##### 集群拓扑刷新配置

集群拓扑刷新的机制（Topology Refresh）是 Lettuce 客户端在 Cluster 模式下的专有能力，不适用其他模式。

```yaml
spring:
   data:
      redis:
         client-type: lettuce
         cluster:
            nodes:
               - 192.168.1.1:7000
               - 192.168.1.2:7001
            max-redirects: 5
         lettuce:
            cluster:
               refresh:
                  adaptive: true                        # 是否开启 自适应刷新（根据 MOVED、ASK、RECONNECT 等自动触发刷新）
                  period: 30s                           # 启用 定期刷新 时的刷新周期，例如 30s、1m
                  dynamic-refresh-sources: true         # 是否根据集群节点变化动态更新节点列表（默认 false）
                  refresh-timeout: 10s                  # 自适应刷新触发后的最小时间间隔，默认不限制
                  close-stale-connections: true         # 是否在刷新拓扑后关闭旧连接，释放资源（默认 false）
```


#### 3 连接池配置（Spring Boot Starter Data Redis默认支持方式）

**注意：** Lettuce 使用异步、非阻塞的 **NIO** 架构，不同线程通过一个 **EventLoopGroup** 并发复用底层连接，无需为每个线程单独分配连接。
连接池仅在以下情况下启用：

- 执行阻塞命令（如 BLPOP、BRPOP 等）
- 执行 Redis 事务
- 有特殊的并发需求

```yaml
spring:
  data:
    redis:
      lettuce:
        pool:
          max-active: 20    # 连接池最大连接数（使用中+空闲）
          max-idle: 10      # 最大空闲连接数
          min-idle: 2       # 最小空闲连接数
          max-wait: 5s      # 无可用连接时最大等待时间（负数表示无限等待）
```

#### 4. 监控指标与 Actuator 集成

`spring-boot-starter-data-redis` 提供了完整的监控指标支持，可以通过 Spring Boot Actuator 端点访问 Redis 监控信息。

在 `Galaxy Boot` 框架中，可以直接引用 `galaxy-boot-starter-metrics`:

```xml
<dependency>
   <groupId>cn.com.chinastock</groupId>
   <artifactId>galaxy-boot-starter-metrics</artifactId>
</dependency>
```

##### 可用的监控指标

**Lettuce提供的监控指标如下：**

- `lettuce_command_completion_seconds` - 命令完成延迟，从发送命令到收到完整响应的延迟。
- `lettuce_command_firstresponse_seconds` - 首次响应延迟，从发送命令到收到第一个响应的延迟。

**示例数据如下（通过`/actuator/prometheus`端点获取）：**

```
# HELP lettuce_command_completion_seconds Latency between command send and command completion (complete response received
# TYPE lettuce_command_completion_seconds summary
lettuce_command_completion_seconds_count{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 2
lettuce_command_completion_seconds_sum{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 0.005258043
lettuce_command_completion_seconds_count{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 1
lettuce_command_completion_seconds_sum{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.008504917
lettuce_command_completion_seconds_count{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 1
lettuce_command_completion_seconds_sum{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.002955792
# HELP lettuce_command_completion_seconds_max Latency between command send and command completion (complete response received
# TYPE lettuce_command_completion_seconds_max gauge
lettuce_command_completion_seconds_max{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
lettuce_command_completion_seconds_max{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
lettuce_command_completion_seconds_max{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
# HELP lettuce_command_firstresponse_seconds Latency between command send and first response (first response received)
# TYPE lettuce_command_firstresponse_seconds summary
lettuce_command_firstresponse_seconds_count{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 2
lettuce_command_firstresponse_seconds_sum{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 0.004896417
lettuce_command_firstresponse_seconds_count{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 1
lettuce_command_firstresponse_seconds_sum{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.006244917
lettuce_command_firstresponse_seconds_count{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 1
lettuce_command_firstresponse_seconds_sum{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.002846417
# HELP lettuce_command_firstresponse_seconds_max Latency between command send and first response (first response received)
# TYPE lettuce_command_firstresponse_seconds_max gauge
lettuce_command_firstresponse_seconds_max{command="CLIENT",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
lettuce_command_firstresponse_seconds_max{command="HELLO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
lettuce_command_firstresponse_seconds_max{command="INFO",local="local:any",remote="localhost/127.0.0.1:6379"} 0.0
```

#### 5. 日志配置

通过配置 `logging.level` 可以开启 Redis 相关的日志输出：

```yaml
logging:
  level:
    org.springframework.data.redis: DEBUG
    io.lettuce.core: DEBUG
```

### 组件使用示例

#### 基本使用

```java
@Service
public class CacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }
    
    public void setWithExpire(String key, Object value, Duration timeout) {
        redisTemplate.opsForValue().set(key, value, timeout);
    }
}
```

#### 响应式使用

```java
@Service
public class ReactiveCacheService {
    
    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;
    
    public Mono<Boolean> set(String key, Object value) {
        return reactiveRedisTemplate.opsForValue().set(key, value);
    }
    
    public Mono<Object> get(String key) {
        return reactiveRedisTemplate.opsForValue().get(key);
    }
    
    public Flux<String> keys(String pattern) {
        return reactiveRedisTemplate.keys(pattern);
    }
}
```

#### Hash 操作

```java
@Service
public class HashCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void hset(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }
    
    public Object hget(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }
    
    public Map<Object, Object> hgetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }
}
```

#### 列表操作

```java
@Service
public class ListCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void lpush(String key, Object... values) {
        redisTemplate.opsForList().leftPushAll(key, values);
    }
    
    public Object lpop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }
    
    public List<Object> lrange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }
}
```
