package cn.com.chinastock.cnf.redis.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.redis.serializer.GalaxyBootRedisSerializerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * Galaxy Boot Redis 自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(before = RedisAutoConfiguration.class)
@ConditionalOnClass({RedisTemplate.class})
@EnableConfigurationProperties(GalaxyBootRedisProperties.class)
public class GalaxyBootRedisAutoConfiguration {

    private final GalaxyBootRedisProperties properties;

    public GalaxyBootRedisAutoConfiguration(GalaxyBootRedisProperties properties) {
        this.properties = properties;
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Galaxy Boot Redis Auto Configuration initialized with properties: {}", properties);
    }

    /**
     * 创建 Galaxy Boot Redis Template
     *
     * @param connectionFactory Redis 连接工厂
     * @return 配置好的 RedisTemplate
     */
    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);

        // 统一配置序列化器
        Serializers serializers = createSerializers();
        redisTemplate.setKeySerializer(serializers.keySerializer);
        redisTemplate.setValueSerializer(serializers.valueSerializer);
        redisTemplate.setHashKeySerializer(serializers.hashKeySerializer);
        redisTemplate.setHashValueSerializer(serializers.hashValueSerializer);

        // 启用事务支持
        redisTemplate.setEnableTransactionSupport(true);
        redisTemplate.afterPropertiesSet();

        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Galaxy Boot RedisTemplate configured with serializers: key={}, value={}, hashKey={}, hashValue={}",
                serializers.keySerializer.getClass().getSimpleName(),
                serializers.valueSerializer.getClass().getSimpleName(),
                serializers.hashKeySerializer.getClass().getSimpleName(),
                serializers.hashValueSerializer.getClass().getSimpleName()
        );
        return redisTemplate;
    }

    /**
     * 配置响应式 RedisTemplate
     *
     * @param connectionFactory Redis 连接工厂
     * @return 配置好的 ReactiveRedisTemplate
     */
    @Bean
    @ConditionalOnMissingBean(name = "reactiveRedisTemplate")
    @ConditionalOnClass(name = "org.springframework.data.redis.core.ReactiveRedisTemplate")
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(ReactiveRedisConnectionFactory connectionFactory) {
        // 统一配置序列化器
        Serializers serializers = createSerializers();
        ReactiveRedisTemplate<String, Object> template = new ReactiveRedisTemplate<>(
                connectionFactory,
                org.springframework.data.redis.serializer.RedisSerializationContext
                        .<String, Object>newSerializationContext()
                        .key(serializers.keySerializer)
                        .value(serializers.valueSerializer)
                        .hashKey(serializers.hashKeySerializer)
                        .hashValue(serializers.hashValueSerializer)
                        .build()
        );
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Galaxy ReactiveRedisTemplate configured with serializers: key={}, value={}, hashKey={}, hashValue={}",
                serializers.keySerializer.getClass().getSimpleName(),
                serializers.valueSerializer.getClass().getSimpleName(),
                serializers.hashKeySerializer.getClass().getSimpleName(),
                serializers.hashValueSerializer.getClass().getSimpleName()
        );
        return template;
    }

    /**
     * 统一创建序列化器
     *
     * @return 序列化器组合结构体
     */
    private Serializers createSerializers() {
        RedisSerializer<String> keySerializer = GalaxyBootRedisSerializerFactory.createSerializer(
                properties.getSerialization().getKeySerializer()
        );
        RedisSerializer<Object> valueSerializer = GalaxyBootRedisSerializerFactory.createSerializer(
                properties.getSerialization().getValueSerializer()
        );
        RedisSerializer<String> hashKeySerializer = GalaxyBootRedisSerializerFactory.createSerializer(
                properties.getSerialization().getHashKeySerializer()
        );
        RedisSerializer<Object> hashValueSerializer = GalaxyBootRedisSerializerFactory.createSerializer(
                properties.getSerialization().getHashValueSerializer()
        );
        return new Serializers(keySerializer, valueSerializer, hashKeySerializer, hashValueSerializer);
    }

    /**
     * 序列化器组合结构体
     */
    private static class Serializers {
        final RedisSerializer<String> keySerializer;
        final RedisSerializer<Object> valueSerializer;
        final RedisSerializer<String> hashKeySerializer;
        final RedisSerializer<Object> hashValueSerializer;

        Serializers(RedisSerializer<String> keySerializer, RedisSerializer<Object> valueSerializer,
                    RedisSerializer<String> hashKeySerializer, RedisSerializer<Object> hashValueSerializer) {
            this.keySerializer = keySerializer;
            this.valueSerializer = valueSerializer;
            this.hashKeySerializer = hashKeySerializer;
            this.hashValueSerializer = hashValueSerializer;
        }
    }
}
