package cn.com.chinastock.cnf.redis.serializer;

import cn.com.chinastock.cnf.redis.config.GalaxyBootRedisProperties;
import com.alibaba.fastjson2.support.spring6.data.redis.GenericFastJsonRedisSerializer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Galaxy Boot Redis 序列化器工厂
 *
 * <AUTHOR>
 */
public class GalaxyBootRedisSerializerFactory {

    private static final StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();
    private static final GenericFastJsonRedisSerializer FASTJSON_SERIALIZER = new GenericFastJsonRedisSerializer();
    private static final GenericJackson2JsonRedisSerializer JACKSON_SERIALIZER = new GenericJackson2JsonRedisSerializer();
    private static final JdkSerializationRedisSerializer JDK_SERIALIZER = new JdkSerializationRedisSerializer();

    /**
     * 创建序列化器
     *
     * @param <T>  序列化器类型参数
     * @param type 序列化器类型
     * @return Redis 序列化器
     */
    @SuppressWarnings("unchecked")
    public static <T> RedisSerializer<T> createSerializer(GalaxyBootRedisProperties.SerializerType type) {
        return switch (type) {
            case STRING -> (RedisSerializer<T>) STRING_SERIALIZER;
            case FASTJSON -> (RedisSerializer<T>) FASTJSON_SERIALIZER;
            case JACKSON -> (RedisSerializer<T>) JACKSON_SERIALIZER;
            case JDK -> (RedisSerializer<T>) JDK_SERIALIZER;
        };
    }
}
