package cn.com.chinastock.cnf.redis.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Galaxy Boot Redis 自定义配置属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = GalaxyBootRedisProperties.CONFIG_PREFIX)
public class GalaxyBootRedisProperties {

    public static final String CONFIG_PREFIX = "galaxy.redis";

    /**
     * 序列化配置
     */
    private Serialization serialization = new Serialization();

    public Serialization getSerialization() {
        return serialization;
    }

    public void setSerialization(Serialization serialization) {
        this.serialization = serialization;
    }

    public static class Serialization {

        /**
         * Key 序列化器类型，默认 STRING
         */
        private SerializerType keySerializer = SerializerType.STRING;

        /**
         * Value 序列化器类型，默认 FASTJSON
         */
        private SerializerType valueSerializer = SerializerType.FASTJSON;

        /**
         * Hash Key 序列化器类型，默认 STRING
         */
        private SerializerType hashKeySerializer = SerializerType.STRING;

        /**
         * Hash Value 序列化器类型，默认 FASTJSON
         */
        private SerializerType hashValueSerializer = SerializerType.FASTJSON;

        public SerializerType getKeySerializer() {
            return keySerializer;
        }

        public void setKeySerializer(SerializerType keySerializer) {
            this.keySerializer = keySerializer;
        }

        public SerializerType getValueSerializer() {
            return valueSerializer;
        }

        public void setValueSerializer(SerializerType valueSerializer) {
            this.valueSerializer = valueSerializer;
        }

        public SerializerType getHashKeySerializer() {
            return hashKeySerializer;
        }

        public void setHashKeySerializer(SerializerType hashKeySerializer) {
            this.hashKeySerializer = hashKeySerializer;
        }

        public SerializerType getHashValueSerializer() {
            return hashValueSerializer;
        }

        public void setHashValueSerializer(SerializerType hashValueSerializer) {
            this.hashValueSerializer = hashValueSerializer;
        }
    }

    /**
     * 序列化器类型
     */
    public enum SerializerType {
        STRING, FASTJSON, JACKSON, JDK
    }
}
