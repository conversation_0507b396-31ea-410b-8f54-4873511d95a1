package cn.com.chinastock.cnf.redis.config;

import com.alibaba.fastjson2.support.spring6.data.redis.GenericFastJsonRedisSerializer;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.*;

import java.lang.reflect.Field;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Galaxy Redis Auto Configuration 测试
 */
class GalaxyBootRedisAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                    RedisAutoConfiguration.class,
                    GalaxyBootRedisAutoConfiguration.class
            ));

    @Test
    void shouldCreateGalaxyBootRedisTemplateWithCorrectSerializers() {
        contextRunner
                .run(context -> {
                    assertThat(context).hasBean("redisTemplate");
                    @SuppressWarnings("unchecked")
                    RedisTemplate<String, Object> redisTemplate = (RedisTemplate<String, Object>) context.getBean("redisTemplate");

                    // 验证序列化器配置
                    assertThat(redisTemplate.getKeySerializer()).isInstanceOf(StringRedisSerializer.class);
                    assertThat(redisTemplate.getValueSerializer()).isInstanceOf(GenericFastJsonRedisSerializer.class);
                    assertThat(redisTemplate.getHashKeySerializer()).isInstanceOf(StringRedisSerializer.class);
                    assertThat(redisTemplate.getHashValueSerializer()).isInstanceOf(GenericFastJsonRedisSerializer.class);
                });
    }

    @Test
    void shouldConfigureCustomSerializers() {
        contextRunner
                .withPropertyValues(
                        "galaxy.redis.serialization.key-serializer=string",
                        "galaxy.redis.serialization.value-serializer=jackson",
                        "galaxy.redis.serialization.hash-key-serializer=string",
                        "galaxy.redis.serialization.hash-value-serializer=jdk"
                )
                .run(context -> {
                    assertThat(context).hasBean("redisTemplate");
                    @SuppressWarnings("unchecked")
                    RedisTemplate<String, Object> redisTemplate = (RedisTemplate<String, Object>) context.getBean("redisTemplate");

                    // 验证自定义序列化器
                    assertThat(redisTemplate.getKeySerializer()).isInstanceOf(StringRedisSerializer.class);
                    assertThat(redisTemplate.getValueSerializer()).isInstanceOf(GenericJackson2JsonRedisSerializer.class);
                    assertThat(redisTemplate.getHashKeySerializer()).isInstanceOf(StringRedisSerializer.class);
                    assertThat(redisTemplate.getHashValueSerializer()).isInstanceOf(JdkSerializationRedisSerializer.class);
                });
    }

    @Test
    void shouldCreateGalaxyBootReactiveRedisTemplateWithCorrectSerializers() {
        contextRunner
                .run(context -> {
                    assertThat(context).hasBean("reactiveRedisTemplate");
                    @SuppressWarnings("unchecked")
                    ReactiveRedisTemplate<String, Object> reactiveRedisTemplate = (ReactiveRedisTemplate<String, Object>) context.getBean("reactiveRedisTemplate");

                    // 验证序列化器配置 - 通过反射访问底层序列化器
                    RedisSerializationContext.SerializationPair<String> keyPair = reactiveRedisTemplate.getSerializationContext().getKeySerializationPair();
                    RedisSerializationContext.SerializationPair<Object> valuePair = reactiveRedisTemplate.getSerializationContext().getValueSerializationPair();
                    RedisSerializationContext.SerializationPair<String> hashKeyPair = reactiveRedisTemplate.getSerializationContext().getHashKeySerializationPair();
                    RedisSerializationContext.SerializationPair<Object> hashValuePair = reactiveRedisTemplate.getSerializationContext().getHashValueSerializationPair();

                    assertThat(extractSerializer(keyPair)).isInstanceOf(StringRedisSerializer.class);
                    assertThat(extractSerializer(valuePair)).isInstanceOf(GenericFastJsonRedisSerializer.class);
                    assertThat(extractSerializer(hashKeyPair)).isInstanceOf(StringRedisSerializer.class);
                    assertThat(extractSerializer(hashValuePair)).isInstanceOf(GenericFastJsonRedisSerializer.class);
                });
    }

    @Test
    void shouldConfigureCustomSerializersForReactiveRedisTemplate() {
        contextRunner
                .withPropertyValues(
                        "galaxy.redis.serialization.key-serializer=string",
                        "galaxy.redis.serialization.value-serializer=jackson",
                        "galaxy.redis.serialization.hash-key-serializer=string",
                        "galaxy.redis.serialization.hash-value-serializer=jdk"
                )
                .run(context -> {
                    assertThat(context).hasBean("reactiveRedisTemplate");
                    @SuppressWarnings("unchecked")
                    ReactiveRedisTemplate<String, Object> reactiveRedisTemplate = (ReactiveRedisTemplate<String, Object>) context.getBean("reactiveRedisTemplate");

                    // 验证自定义序列化器 - 通过反射访问底层序列化器
                    RedisSerializationContext.SerializationPair<String> keyPair = reactiveRedisTemplate.getSerializationContext().getKeySerializationPair();
                    RedisSerializationContext.SerializationPair<Object> valuePair = reactiveRedisTemplate.getSerializationContext().getValueSerializationPair();
                    RedisSerializationContext.SerializationPair<String> hashKeyPair = reactiveRedisTemplate.getSerializationContext().getHashKeySerializationPair();
                    RedisSerializationContext.SerializationPair<Object> hashValuePair = reactiveRedisTemplate.getSerializationContext().getHashValueSerializationPair();

                    assertThat(extractSerializer(keyPair)).isInstanceOf(StringRedisSerializer.class);
                    assertThat(extractSerializer(valuePair)).isInstanceOf(GenericJackson2JsonRedisSerializer.class);
                    assertThat(extractSerializer(hashKeyPair)).isInstanceOf(StringRedisSerializer.class);
                    assertThat(extractSerializer(hashValuePair)).isInstanceOf(JdkSerializationRedisSerializer.class);
                });
    }

    private RedisSerializer<?> extractSerializer(RedisSerializationContext.SerializationPair<?> pair) {
        try {
            // Try different possible field names for the underlying serializer
            Field[] fields = pair.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(pair);
                if (value instanceof RedisSerializer) {
                    return (RedisSerializer<?>) value;
                }
            }

            // If no direct serializer field found, try to get it from writer/reader
            Object writer = pair.getWriter();
            if (writer instanceof RedisSerializer) {
                return (RedisSerializer<?>) writer;
            }

            // Fallback: check if the writer wraps a serializer
            Field[] writerFields = writer.getClass().getDeclaredFields();
            for (Field field : writerFields) {
                field.setAccessible(true);
                Object value = field.get(writer);
                if (value instanceof RedisSerializer) {
                    return (RedisSerializer<?>) value;
                }
            }

            throw new IllegalStateException("Could not extract serializer from SerializationPair: " + pair.getClass());
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract serializer", e);
        }
    }

    @Test
    void shouldUseDefaultPropertiesWhenNotSpecified() {
        contextRunner
                .run(context -> {
                    assertThat(context).hasSingleBean(GalaxyBootRedisProperties.class);
                    GalaxyBootRedisProperties properties = context.getBean(GalaxyBootRedisProperties.class);

                    // 验证默认值
                    assertThat(properties.getSerialization().getKeySerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.STRING);
                    assertThat(properties.getSerialization().getValueSerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.FASTJSON);
                    assertThat(properties.getSerialization().getHashKeySerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.STRING);
                    assertThat(properties.getSerialization().getHashValueSerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.FASTJSON);
                });
    }

    @Test
    void shouldConfigureCustomSerializersWhenSpecified() {
        contextRunner
                .withPropertyValues(
                        "galaxy.redis.serialization.key-serializer=string",
                        "galaxy.redis.serialization.value-serializer=jackson",
                        "galaxy.redis.serialization.hash-key-serializer=jdk",
                        "galaxy.redis.serialization.hash-value-serializer=fastjson"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(GalaxyBootRedisProperties.class);
                    GalaxyBootRedisProperties properties = context.getBean(GalaxyBootRedisProperties.class);

                    // 验证默认值
                    assertThat(properties.getSerialization().getKeySerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.STRING);
                    assertThat(properties.getSerialization().getValueSerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.JACKSON);
                    assertThat(properties.getSerialization().getHashKeySerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.JDK);
                    assertThat(properties.getSerialization().getHashValueSerializer())
                            .isEqualTo(GalaxyBootRedisProperties.SerializerType.FASTJSON);
                });
    }
}
