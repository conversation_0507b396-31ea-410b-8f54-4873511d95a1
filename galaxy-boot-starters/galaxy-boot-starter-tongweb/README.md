## Galaxy Boot TongWeb Starter

> TongWeb 嵌入式版是支持 Java EE 企业级标准的嵌入式应用服务器，并通过集成 Spring Boot、Spring Cloud 框架进行使用。同时，Java EE 
企业级标准容器服务提供了对 React 响应式编程标准容器的适配。

### 依赖配置

如果要在您的项目中使用 TongWeb 来实现配置管理，只需要引入对应 starter 即可：使用 group ID 为 `cn.com.chinastock` 和 artifact ID 为 
`galaxy-boot-starter-tongweb` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-tongweb</artifactId>
</dependency>
```

引入 `galaxy-boot-starter-tongweb` 后，无需额外配置，即可使用 `TongWeb` 的功能。

启动日志示例：

```
     _____               __        __   _
    |_   _|__  _ __   __ \ \      / /__| |__
      | |/ _ \| '_ \ / _` \ \ /\ / / _ \ '_ \
      | | (_) | | | | (_| |\ V  V /  __/ |_) |
      |_|\___/|_| |_|\__, | \_/\_/ \___|_.__/
                     |___/
Copyright  2023 Beijing Tongtech Co., Ltd.  All rights reserved.
2024-12-09T14:03:02.094+08:00  INFO 75211 --- [           main] c.c.c.c.examples.CoreExampleApplication  : Starting CoreExampleApplication using Java 21 with PID 75211 (/Users/<USER>/works/galaxy/galaxy-boot/galaxy-boot-examples/galaxy-boot-core-example/target/classes started by phodal in /Users/<USER>/works/galaxy/galaxy-boot)
2024-12-09T14:03:02.096+08:00  INFO 75211 --- [           main] c.c.c.c.examples.CoreExampleApplication  : No active profile set, falling back to 1 default profile: "default"
2024-12-09T14:03:03.283+08:00  INFO 75211 --- [           main] c.t.commons.license.LicenseProvider      : TongTech License SDK Version Number [ 4.5.1.6 ]
```