package cn.com.chinastock.cnf.tongweb;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * GalaxyTongWebAutoConfiguration 类用于自动配置 TongWeb 相关的属性。
 *
 * `server.tongweb.license.path=classpath:tongweb/license.dat`
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnMissingBean(TongWebLicensePropertyConfigurator.class)
public class GalaxyTongWebAutoConfiguration {
    public GalaxyTongWebAutoConfiguration() {

    }

    @Bean
    public TongWebLicensePropertyConfigurator tongWebProperty() {
        return new TongWebLicensePropertyConfigurator();
    }
}
