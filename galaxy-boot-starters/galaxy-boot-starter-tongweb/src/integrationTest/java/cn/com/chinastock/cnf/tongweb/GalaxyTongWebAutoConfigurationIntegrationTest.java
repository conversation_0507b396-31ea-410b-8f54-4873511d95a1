package cn.com.chinastock.cnf.tongweb;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = GalaxyTongWebAutoConfiguration.class)
@ActiveProfiles("test")
@EnableAutoConfiguration
@ExtendWith(OutputCaptureExtension.class)
public class GalaxyTongWebAutoConfigurationIntegrationTest {
    @Test
    void shouldCaptureTongWebLog(CapturedOutput output) {
        String all = output.getAll();
        assertTrue(all.contains("Beijing Tongtech Co., Ltd.  All rights reserved."));
    }
}