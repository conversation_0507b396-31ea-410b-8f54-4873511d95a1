package cn.com.chinastock.cnf.tongweb;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertySource;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class GalaxyTongWebAutoConfigurationTest {
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyTongWebAutoConfiguration.class));

    @Test
    void shouldCreateTongWebLicensePropertyConfiguratorBeanWhenNoExistingBean() {
        contextRunner.run(context -> {
            TongWebLicensePropertyConfigurator configurator = context.getBean(TongWebLicensePropertyConfigurator.class);
            assertThat(configurator).isNotNull();
        });
    }

    @Test
    void shouldConfigurePropertySourceWithCorrectValues() {
        contextRunner.run(context -> {
            ConfigurableEnvironment environment = context.getEnvironment();
            PropertySource<?> propertySource = environment.getPropertySources().get(TongWebLicensePropertyConfigurator.PROPERTY_SOURCES);

            assertThat(propertySource).isNotNull();
            assertThat(propertySource.getName()).isEqualTo(TongWebLicensePropertyConfigurator.PROPERTY_SOURCES);
            assertThat(propertySource.getSource()).isEqualTo(Map.of("server.tongweb.license.path", "classpath:tongweb/license.dat"));
        });
    }
}

