package cn.com.chinastock.cnf.swagger;

import cn.com.chinastock.cnf.swagger.annotations.ApiMetaCodesCollector;
import cn.com.chinastock.cnf.swagger.annotations.ApiMetaCodesCustomizer;
import cn.com.chinastock.cnf.swagger.interceptor.SwaggerAuthInterceptor;
import cn.com.chinastock.cnf.swagger.properties.SwaggerProperties;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.properties.SpringDocConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 自动配置类，用于配置 Swagger 相关的属性。
 * <p>
 * 由于 Spring Boot Security 的限制，这里采用的是拦截器的方式，对 Swagger 的访问进行权限控制。
 */
@Configuration
@EnableConfigurationProperties(SwaggerProperties.class)
public class GalaxySwaggerAutoConfigure implements WebMvcConfigurer {
    private final SwaggerProperties swaggerProperties;

    public GalaxySwaggerAutoConfigure(SwaggerProperties swaggerProperties) {
        this.swaggerProperties = swaggerProperties;
    }

    @Bean
    @Primary
    public SwaggerUiConfigProperties swaggerUiConfig() {
        SwaggerUiConfigProperties config = new SwaggerUiConfigProperties();
        config.setPath("/swagger-ui.html");
        config.setEnabled(true);
        return config;
    }

    @Bean
    @Primary
    public SpringDocConfigProperties springDocConfig() {
        SpringDocConfigProperties properties = new SpringDocConfigProperties();
        properties.setApiDocs(new SpringDocConfigProperties.ApiDocs());
        properties.getApiDocs().setPath("/v3/api-docs");
        return properties;
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SwaggerAuthInterceptor(swaggerProperties))
                .addPathPatterns("/swagger-ui.html", "/v3/api-docs/**", "/swagger-ui/**");
    }


    @Bean
    public ApiMetaCodesCollector apiMetaCodesCollector() {
        return new ApiMetaCodesCollector();
    }

    @Bean
    public OpenApiCustomizer openApiCustomizer() {
        return new ApiMetaCodesCustomizer();
    }

}
