package cn.com.chinastock.cnf.swagger.annotations;

import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.http.annotations.ApiMetaCodes;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import org.springdoc.core.customizers.OpenApiCustomizer;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * ApiMetaCodesCustomizer 类用于处理 {@link ApiMetaCodes}，生成{@link Meta}的文档描述。
 *
 * <AUTHOR>
 */
public class ApiMetaCodesCustomizer implements OpenApiCustomizer {

    private static final String EXTENSION_X_API_META_CODES = "x-api-meta-codes";

    @Override
    public void customise(OpenAPI openApi) {
        Components components = openApi.getComponents();
        if (components == null) {
            return;
        }

        try {
            openApi.getPaths().forEach((path, pathItem) ->
                    pathItem.readOperations().forEach(operation -> processOperation(openApi, operation, components)));
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, "process ApiMetaCodes error", e);
        }
    }

    private void processOperation(OpenAPI openApi, Operation operation, Components components) {
        if (operation.getExtensions() == null
                || !operation.getExtensions().containsKey(EXTENSION_X_API_META_CODES)
                || !(operation.getExtensions().get(EXTENSION_X_API_META_CODES) instanceof ApiMetaCodes)) {
            return;
        }

        ApiResponse apiResponse = operation.getResponses().get("200");
        if (apiResponse == null) {
            return;
        }

        ApiMetaCodes apiMetaCodes = (ApiMetaCodes) operation.getExtensions().get(EXTENSION_X_API_META_CODES);
        String description = Arrays.stream(apiMetaCodes.value())
                .map(apiMetaCode -> apiMetaCode.code() + ":" + apiMetaCode.description())
                .collect(Collectors.joining("; "));

        apiResponse.getContent().values().forEach(mediaType ->
                        modifyCodeSchema(openApi, mediaType.getSchema(), components, description));
    }

    private void modifyCodeSchema(OpenAPI openApi, Schema<?> schema, Components components, String description) {
        if (schema == null) {
            return;
        }

        if (schema.get$ref() != null) {
            String schemaName = schema.get$ref().replace("#/components/schemas/", "");
            Schema<?> resolvedSchema = components.getSchemas().get(schemaName);
            if (resolvedSchema != null) {
                modifyCodeSchema(openApi, resolvedSchema, components, description);
            }
        }
        // 处理内联 Schema
        else if (schema.getProperties() != null && schema.getProperties().containsKey("meta")) {
            Schema<?> metaSchema = (Schema<?>) schema.getProperties().get("meta");
            if (metaSchema == null) return;

            // 如果 meta 是引用，替换为内联 Schema（保留其他字段）
            if (metaSchema.get$ref() != null) {
                String metaRef = metaSchema.get$ref().replace("#/components/schemas/", "");
                Schema<?> globalMeta = openApi.getComponents().getSchemas().get(metaRef);
                if (globalMeta != null) {
                    Schema<?> clonedMeta = new Schema<>();
                    clonedMeta.setType(globalMeta.getType());
                    Map<String, Schema> clonedProperties = new LinkedHashMap<>();
                    globalMeta.getProperties().forEach((key, value) -> {
                        Schema<?> clonedValue = new Schema<>();
                        clonedValue.setType(value.getType());
                        clonedValue.setDescription(value.getDescription());
                        clonedValue.setEnum(value.getEnum());
                        clonedProperties.put(key, clonedValue);
                    });
                    clonedProperties.put("code", new Schema<>()
                            .type("string")
                            .description(description));
                    clonedMeta.setProperties(clonedProperties);
                    schema.getProperties().put("meta", clonedMeta);
                }
            }
            // 直接修改内联 meta
            else if (metaSchema.getProperties() != null) {
                Schema<?> codeSchema = (Schema<?>) metaSchema.getProperties().get("code");
                if (codeSchema != null) {
                    codeSchema.setDescription(description);
                }
            }
        }
    }
}
