package cn.com.chinastock.cnf.swagger;

import cn.com.chinastock.cnf.core.http.annotations.ApiMetaCode;
import cn.com.chinastock.cnf.core.http.annotations.ApiMetaCodes;
import cn.com.chinastock.cnf.swagger.annotations.ApiMetaCodesCollector;
import cn.com.chinastock.cnf.swagger.annotations.ApiMetaCodesCustomizer;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.media.BooleanSchema;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.ObjectSchema;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import org.junit.jupiter.api.Test;
import org.springdoc.core.properties.SpringDocConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.method.HandlerMethod;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GalaxySwaggerAutoConfigureTest {
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxySwaggerAutoConfigure.class))
            .withPropertyValues(
                    "galaxy.swagger.auth.username=testuser",
                    "galaxy.swagger.auth.password=testpass"
            );

    @Test
    void should_create_swagger_ui_config() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SwaggerUiConfigProperties.class);
            SwaggerUiConfigProperties properties = context.getBean(SwaggerUiConfigProperties.class);
            assertThat(properties.getPath()).isEqualTo("/swagger-ui.html");
            assertThat(properties.isEnabled()).isTrue();
        });
    }

    @Test
    void should_create_springdoc_config() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SpringDocConfigProperties.class);
            SpringDocConfigProperties properties = context.getBean(SpringDocConfigProperties.class);
            assertThat(properties.getApiDocs().getPath()).isEqualTo("/v3/api-docs");
        });
    }

    @Test
    void should_add_extension_when_annotation_present() {
        ApiMetaCodesCollector collector = new ApiMetaCodesCollector();
        Operation operation = new Operation();

        ApiMetaCodes apiMetaCodes = mock(ApiMetaCodes.class);
        ApiMetaCode code1 = mock(ApiMetaCode.class);
        when(code1.code()).thenReturn("1001");
        when(code1.description()).thenReturn("错误");
        when(apiMetaCodes.value()).thenReturn(new ApiMetaCode[]{code1});

        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethodAnnotation(ApiMetaCodes.class)).thenReturn(apiMetaCodes);

        Operation result = collector.customize(operation, handlerMethod);
        assertNotNull(result.getExtensions().get("x-api-meta-codes"));
    }

    @Test
    void should_return_null_when_annotation_absent() {
        // 模拟无注解的方法
        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethodAnnotation(ApiMetaCodes.class)).thenReturn(null);

        Operation operation = new Operation();
        Operation result = new ApiMetaCodesCollector().customize(operation, handlerMethod);

        assertNull(result.getExtensions()); // 无扩展被添加
    }


    @Test
    void customize_shouldModifyCodeSchemaWhenExtensionExists() {
        OpenAPI openApi = new OpenAPI();
        prepareBaseResponseSchema(openApi);

        ApiResponses mockApiResponses = mock(ApiResponses.class);
        ApiResponse mockApiResponse = mock(ApiResponse.class);
        when(mockApiResponses.get("200")).thenReturn(mockApiResponse);

        Content mockContent = mock(Content.class);
        MediaType mockMediaType = mock(MediaType.class);
        Schema<?> responseRef = new Schema<>().$ref("#/components/schemas/BaseResponse«User»");

        when(mockApiResponse.getContent()).thenReturn(mockContent);
        when(mockContent.values()).thenReturn(Collections.singletonList(mockMediaType));
        when(mockMediaType.getSchema()).thenReturn(responseRef);

        Operation operation = new Operation();
        operation.addExtension("x-api-meta-codes", createMockApiMetaCodes("200:成功"));
        operation.responses(mockApiResponses);

        openApi.path("/api/user", new PathItem().get(operation));

        new ApiMetaCodesCustomizer().customise(openApi);

        Schema<?> baseResponseSchema = openApi.getComponents().getSchemas().get("BaseResponse«User»");
        Schema<?> metaSchema = (Schema<?>) baseResponseSchema.getProperties().get("meta");
        Schema<?> code = (Schema<?>) metaSchema.getProperties().get("code");

        assertEquals(code.getDescription(), "200:成功");
    }

    private ApiMetaCodes createMockApiMetaCodes(String... codeDescPairs) {
        return new ApiMetaCodes() {
            @Override
            public ApiMetaCode[] value() {
                return Arrays.stream(codeDescPairs)
                        .map(pair -> {
                            String[] parts = pair.split(":");
                            return new ApiMetaCode() {
                                public String code() { return parts[0]; }
                                public String description() { return parts[1]; }
                                public Class<? extends Annotation> annotationType() { return ApiMetaCode.class; }
                            };
                        })
                        .toArray(ApiMetaCode[]::new);
            }
            public Class<? extends Annotation> annotationType() {
                return ApiMetaCodes.class;
            }
        };
    }

    private void prepareBaseResponseSchema(OpenAPI openApi) {
        // 1. 定义 Meta 对象的 Schema
        Schema<?> metaSchema = new Schema<>()
                .type("object")
                .addProperty("code", new StringSchema())
                .addProperty("success", new BooleanSchema())
                .addProperty("message", new StringSchema());

        // 2. 定义 BaseResponse 的 Schema（引用 Meta）
        Schema<?> baseResponseSchema = new Schema<>()
                .type("object")
                .addProperty("meta", new Schema<>().$ref("#/components/schemas/Meta"))
                .addProperty("data", new ObjectSchema())
                .addProperty("pageInfo", new ObjectSchema());

        // 3. 注册到 Components
        openApi.components(new Components()
                .addSchemas("Meta", metaSchema)
                .addSchemas("BaseResponse«User»", baseResponseSchema));
    }

}
