package cn.com.chinastock.cnf.metrics;

import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.logging.LoggingMeterRegistry;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class GalaxyHikariMetricAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyHikariMetricAutoConfiguration.class));

    @Test
    void should_create_loggingMeterRegistry_when_property_is_enabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.logging.enable=true")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .run(context -> {
                    // Verify MeterRegistry bean exists
                    assertThat(context).hasSingleBean(MeterRegistry.class);
                    assertThat(context.getBean(MeterRegistry.class)).isInstanceOf(LoggingMeterRegistry.class);

                    // Verify HikariDataSource metric registry is set correctly
                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);
                    LoggingMeterRegistry meterRegistry = (LoggingMeterRegistry) context.getBean(MeterRegistry.class);

                    assertThat(dataSource.getMetricRegistry()).isEqualTo(meterRegistry);
                });
    }

    @Test
    void should_not_create_loggingMeterRegistry_when_property_is_disabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.logging.enable=false")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .run(context -> {
                    // Verify MeterRegistry bean does not exist
                    assertThat(context).doesNotHaveBean(MeterRegistry.class);

                    // Verify HikariDataSource metric registry is not set
                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);
                    assertThat(dataSource.getMetricRegistry()).isNull();
                });
    }


    @Test
    void should_not_create_loggingMeterRegistry_when_property_is_enabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.prometheus.enable=true")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .withBean(PrometheusMeterRegistry.class, () -> new PrometheusMeterRegistry(key -> null))
                .run(context -> {
                    // Verify MeterRegistry bean does not exist
                    Map<String, MeterRegistry> beans = context.getBeansOfType(MeterRegistry.class);

                    assertThat(beans).hasSize(2);
                    assertThat(beans.get("customPrometheusMeterRegistry")).isInstanceOf(PrometheusMeterRegistry.class);

                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);

                    assertThat(dataSource.getMetricRegistry()).isInstanceOf(PrometheusMeterRegistry.class);
                });
    }



    @Test
    void should_update_pool_size_when_config_changes() {
        contextRunner
                .withPropertyValues(
                        "app.id=test-app",
                        "galaxy.datasource.dynamic-maximum-pool-size=true",
                        "spring.datasource.hikari.maximum-pool-size=20"
                )
                .withBean(HikariDataSource.class, () -> {
                    HikariDataSource ds = new HikariDataSource();
                    ds.setMaximumPoolSize(10);
                    return ds;
                })
                .run(context -> {
                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);
                    assertThat(dataSource.getMaximumPoolSize()).isEqualTo(10);
                });
    }

}
