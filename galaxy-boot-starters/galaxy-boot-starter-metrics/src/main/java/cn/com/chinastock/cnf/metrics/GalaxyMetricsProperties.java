package cn.com.chinastock.cnf.metrics;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = GalaxyMetricsProperties.CONFIG_PREFIX)
public class GalaxyMetricsProperties {
    public static final String CONFIG_PREFIX = "galaxy.metrics";
    
    private Datasource datasource = new Datasource();

    public Datasource getDatasource() {
        return datasource;
    }

    public void setDatasource(Datasource datasource) {
        this.datasource = datasource;
    }

    public static class Datasource {
        private Logging logging = new Logging();
        private Prometheus prometheus = new Prometheus();

        public Logging getLogging() {
            return logging;
        }

        public void setLogging(Logging logging) {
            this.logging = logging;
        }

        public Prometheus getPrometheus() {
            return prometheus;
        }

        public void setPrometheus(Prometheus prometheus) {
            this.prometheus = prometheus;
        }
    }

    public static class Logging {
        /**
         * 是否启用日志指标，记录 HikariCP 连接池指标到日志中。
         */
        private boolean enable = false;
        
        /**
         * 日志输出的间隔时间，默认 300 秒。
         */
        private int interval = 300;

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public int getInterval() {
            return interval;
        }

        public void setInterval(int interval) {
            this.interval = interval;
        }
    }

    public static class Prometheus {
        /**
         * 是否启用 Prometheus 指标收集
         */
        private boolean enable = false;

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }
    }
}
