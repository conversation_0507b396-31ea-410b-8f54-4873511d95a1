### 组件实例教程

参考配置示例：

```yaml
galaxy:
  security:
    input-protect: true   # 对请求内容进行转义，主要处理 xss 攻击
    output-protect: true  # 对响应内容进行转义
    user:                 # 默认用户配置
      name: user          # 用户名
      password: password  # 密码
    cors:                 # 不使用时不需要配置
      protect: true       
      whitelist:          # 不建议启用 CORS，对于前端应用，推荐使用 Nginx 等反向代理进行跨域处理
        - https://*.chinastock.com.cn  # 只支持白名单路径，如果不配置则默认允许所有跨域请求
    csrf:                 # 适用于表单场景，API 场景通常不需要配置
      protect: false      # 是否开启CSRF保护，默认为 false，推荐在表单场景中开启
      whitelist:
        - /example/**
      blacklist:
        - /example/**
    actuator:             # Actuator 端点保护配置
      protect: true       # 是否开启 Actuator 保护，默认为 false
      whitelist:          # Actuator 白名单 IP
        - 127.0.0.1
        - ***********/24    # 表示从 `***********` 到 `*************` 的所有 IP 地址。
        - 0:0:0:0:0:0:0:1  # 表示 `localhost` 的 IP 地址。
```

### galaxy.security.user
                                                       
适用场景：

- **注意**：`galaxy.security.user` 和 `galaxy.security.actuator` 是互斥的配置，用户只能选择其中一种方式。
- 如果同时配置了 `galaxy.security.user` 和 `galaxy.security.actuator`，系统将优先使用 `galaxy.security.actuator` 的配置。

- actuator 端点保护 

默认用户配置用于快速设置一个简单的用户认证，可以通过以下配置启用：

```yaml
galaxy.security.user.name=user
galaxy.security.user.password=password
```

特性：

1. 默认用户名为 `user`，密码为 `password`。
2. 用户角色为 `USER`。
3. 适用于开发和测试环境，生产环境应使用更安全的用户管理方式。

### galaxy.security.actuator

- **注意**：`galaxy.security.actuator` 和 `galaxy.security.user` 是互斥的配置，用户只能选择其中一种方式。
- 如果同时配置了 `galaxy.security.actuator` 和 `galaxy.security.user`，系统将优先使用 `galaxy.security.actuator` 的配置。

- **注意**：开启 Actuator 保护后，只有白名单中的 IP 可以访问 Actuator 端点。

Actuator 保护配置用于控制对 Actuator 端点的访问，配置方式示例：

```yaml
galaxy.security.actuator.protect=true
galaxy.security.actuator.whitelist[0]=127.0.0.1
```

特性：

1. 当未配置白名单时，默认禁止所有 IP 访问 Actuator 端点。
2. 配置白名单后，仅允许白名单中的 IP 访问 Actuator 端点。
3. 支持配置多个白名单 IP，如 `galaxy.security.actuator.whitelist[0]=127.0.0.1`。

注意事项：

- 开启保护且配置白名单后，未在白名单中的 IP 将禁止访问 Actuator 端点。
- 未配置白名单时默认禁止所有 IP 访问 Actuator 端点。
- 可以通过配置 `galaxy.security.actuator.protect=false` 完全关闭 Actuator 保护。

### galaxy.security.input-protect

- **注意**：开启输入过滤会对所有请求进行内容检测，对高并发系统有一定性能开销，应视业务需求评估。
- **注意**：开启输入过滤会对所有请求进行内容检测，对高并发系统有一定性能开销，应视业务需求评估。
- **注意**：开启输入过滤会对所有请求进行内容检测，对高并发系统有一定性能开销，应视业务需求评估。

输入验证过滤器用于防止 XSS 攻击，可以通过以下配置启用：

```
galaxy.security.input-protect=true
```

特性：

1. 自动过滤所有请求参数中的 XSS 内容
2. 对以下文件扩展名的请求会自动跳过验证：.gif、.jpg、.png 等静态资源
3. 会自动转义 HTML 标签，如 `<script>` 将被转换为 `&lt;script&gt;`
4. 会自动跳过对静态资源文件及以下路径的验证，包括 `.css`、`.js`、`.png`、`.jpg`、`.gif`、`.ico`、`.svg`、`.woff`、`.woff2`、
   `.ttf`、`.eot`、`.mp3`、`.mp4`、`.pdf`，以及诸如 `/static/`、`/assets/`、`/resources/`、`/images/`、`/fonts/` 等常见目录
5. 若请求体为 JSON，过滤器将递归清理其所有文本字段；若 JSON 解析失败，则直接对整个字符串进行清理

### galaxy.security.output-protect

- **注意**：因为会对响应内容做JSON解析并转义操作，尤其针对大体量或频繁返回数据时会增加一定的 CPU 开销。
- **注意**：因为会对响应内容做JSON解析并转义操作，尤其针对大体量或频繁返回数据时会增加一定的 CPU 开销。
- **注意**：因为会对响应内容做JSON解析并转义操作，尤其针对大体量或频繁返回数据时会增加一定的 CPU 开销。

输出检查过滤器用于防止响应中包含不安全的内容，配置方式示例：

```
galaxy.security.output-protect=true
```

特性：

1. 自动处理所有响应内容中的特殊字符
2. 支持复杂的 JSON 对象递归处理
3. 支持以下数据类型的处理：
    - 字符串类型：直接进行 HTML 转义
    - JSON 对象：递归处理所有字段
    - JSON 数组：递归处理所有元素
    - 嵌套结构：支持任意深度的嵌套对象处理
4. 自动转义 HTML 特殊字符，包括：
    - `<` 转义为 `&lt;`
    - `>` 转义为 `&gt;`
    - `"` 转义为 `&quot;`
    - `'` 转义为 `&#39;`
    - `&` 转义为 `&amp;`

注意事项：

- 适用于所有返回类型的响应
- 对于非字符串类型的数据不会进行处理
- 如果 JSON 解析失败，将返回原始数据
- 建议在接口返回敏感内容时启用此功能

### galaxy.security.cors

CORS（跨域资源共享）过滤器用于控制跨域访问请求，配置方式示例：

```
galaxy.security.cors.protect=true
galaxy.security.cors.whitelist[0]=https://*.chinastock.com.cn
```

特性：

1. 当未配置白名单时，默认允许所有跨域请求
2. 配置白名单后，仅允许白名单中的路径进行跨域访问
3. 默认配置以下 CORS 属性：
    - Access-Control-Allow-Credentials: true
    - Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
    - Access-Control-Allow-Headers: * (允许所有请求头)

白名单配置说明：

- 支持配置多个白名单路径，如 `galaxy.security.cors.whitelist[0]=http://domain.chinastock.com.cn`

注意事项：

- 开启保护且配置白名单后，未在白名单中的路径将禁止跨域访问
- 未配置白名单时允许所有跨域请求
- 可以通过配置 `galaxy.security.cors.protect=false` 完全关闭 CORS 功能

### galaxy.security.csrf （适用于表单场景）

**CSRF（Cross-Site Request Forgery，跨站请求伪造）保护**通常用于 Web 应用程序，特别是那些使用表单提交的场景。然而，在纯 API
场景中，CSRF 的适用性较低，因为 API 通常使用 Token（如 JWT）或其他无状态的认证方式，而这些方式并不依赖于 Cookie。因此，对于基于
Token 的 API 认证方式（如 JWT），CSRF 的风险较小，可以考虑禁用 CSRF 保护。

### 配置方式示例：

```
galaxy.security.csrf.protect=true
galaxy.security.csrf.whitelist[0]=/api/public/**
galaxy.security.csrf.blacklist[0]=/api/private/**
```

### 特性：

1. 默认对所有非 GET、HEAD、TRACE、OPTIONS 的请求进行 CSRF 校验。
2. 支持配置白名单和黑名单路径：
    - **白名单**：不进行 CSRF 校验的路径。
    - **黑名单**：必须进行 CSRF 校验的路径。
3. 支持 Ant 风格的路径匹配，如 `/**`、`/api/*`。
4. 验证规则优先级：
    - 首先检查黑名单，若匹配则强制要求 CSRF 令牌。
    - 然后检查白名单，若匹配则跳过 CSRF 检查。
    - 最后按默认规则处理。

### 注意事项：

- 在基于 Token 的 API 场景中，建议 **禁用** CSRF 保护（`galaxy.security.csrf.protect=false`），因为 CSRF 攻击主要针对基于
  Cookie 的会话认证。
- 对于需要修改数据的接口（如 POST、PUT、DELETE 等），如果采用 Cookie 认证，建议启用 CSRF 保护。
- 前端请求需要在 Header 中携带 CSRF 令牌，通常通过 `X-CSRF-TOKEN` 请求头或 `_csrf` 参数传递。如果禁用 CSRF 保护，可以跳过此配置。
- 可以通过配置 `galaxy.security.csrf.protect=false` 完全关闭 CSRF 保护。

### Swagger 展示

从安全机制的角度来看，线上环境开启 Swagger 可能会带来一定的风险。虽然 Swagger 提供了便捷的 API
文档和调试功能，但它也可能暴露系统的内部结构和敏感信息，增加了潜在的安全隐患。因此，建议在生产环境中谨慎使用
Swagger，并确保仅对受信任的 IP 或用户开放访问权限。为了避免泄露敏感信息，可以将 Swagger API 相关的路径添加到白名单中：

```yaml
whitelist:
  - /api/**
  - /swagger-ui/**
  - /v3/api-docs/**
```
