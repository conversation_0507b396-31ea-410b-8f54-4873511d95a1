package cn.com.chinastock.cnf.security;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.csrf.CustomCsrfRequestMatcher;
import cn.com.chinastock.cnf.security.input.XssInputValidationFilter;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.XssResponseBodyAdvice;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.header.writers.XXssProtectionHeaderWriter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.security.config.Customizer.withDefaults;

@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@EnableConfigurationProperties({GalaxySecurityProperties.class, CopiedFastJsonProperties.class})
public class GalaxySecurityAutoConfiguration {
    private final GalaxySecurityProperties securityProperties;
    private final CopiedFastJsonProperties fastJsonProperties;

    public GalaxySecurityAutoConfiguration(GalaxySecurityProperties securityProperties, CopiedFastJsonProperties fastJsonProperties) {
        this.securityProperties = securityProperties;
        this.fastJsonProperties = fastJsonProperties;
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "output-protect", havingValue = "true")
    public XssResponseBodyAdvice xssResponseBodyAdvice() {
        return new XssResponseBodyAdvice(fastJsonProperties);
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "input-protect", havingValue = "true")
    public FilterRegistrationBean<XssInputValidationFilter> xssPreventFilter() {
        FilterRegistrationBean<XssInputValidationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new XssInputValidationFilter());
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "securityProperties: " + securityProperties.toString());

        http.headers(headers -> headers
                // 启用 XSS 保护，并在检测到 XSS 攻击时阻止页面加载
                .xssProtection(xss -> xss.headerValue(XXssProtectionHeaderWriter.HeaderValue.ENABLED_MODE_BLOCK))
                // 只允许从当前域名加载资源，防止加载外部恶意资源。
                .contentSecurityPolicy(csp -> csp.policyDirectives("default-src 'self'"))
                // 控制浏览器在导航到其他页面时发送的 Referer 头信息。
                .referrerPolicy(referrer -> referrer.policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN))
                // 禁止页面在 <frame>、<iframe> 或 <object> 中加载，防止页面被嵌入到其他网站中。
                .frameOptions(frame -> frame.deny())
                //  禁用 MIME 类型嗅探，强制浏览器使用服务器提供的 Content-Type 头来确定文件类型。
                .contentTypeOptions(content -> content.disable())
        );

        // todo: 评估 /env, /heapdump, /mappings 是否需要保护
        if (securityProperties.getActuator().isProtect()) {
            List<String> whitelist = securityProperties.getActuator().getWhitelist();
            if (whitelist != null && !whitelist.isEmpty()) {
                String ipExpression = whitelist.stream()
                        .map(ip -> "hasIpAddress('" + ip + "')")
                        .collect(Collectors.joining(" or "));

                http.securityMatcher(EndpointRequest.toAnyEndpoint())
                        .authorizeHttpRequests(authorize ->
                                authorize.anyRequest().access(new WebExpressionAuthorizationManager(ipExpression))
                        );
            } else {
                http.securityMatcher(EndpointRequest.toAnyEndpoint())
                        .authorizeHttpRequests(authorize -> authorize
                                .anyRequest().authenticated()
                        )
                        .httpBasic(withDefaults());
            }
        } else {
            http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll());
        }

        http.csrf(csrf -> {
            if (securityProperties.getCsrf().isProtect()) {
                List<String> whitelist = securityProperties.getCsrf().getWhitelist();
                List<String> blacklist = securityProperties.getCsrf().getBlacklist();
                csrf.requireCsrfProtectionMatcher(new CustomCsrfRequestMatcher(whitelist, blacklist));
            } else {
                csrf.disable();
            }
        });

        return http.build();
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "cors.protect", havingValue = "true")
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        List<String> whitelist = securityProperties.getCors().getWhitelist();
        if (whitelist != null && !whitelist.isEmpty()) {
            CorsConfiguration configuration = new CorsConfiguration();
            configuration.setAllowCredentials(true);
            configuration.setAllowedOriginPatterns(whitelist);
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
            configuration.setAllowedHeaders(List.of("*"));
            source.registerCorsConfiguration("/**", configuration);
        } else {
            CorsConfiguration configuration = new CorsConfiguration();
            configuration.setAllowCredentials(false);
            configuration.setAllowedOrigins(List.of());
            configuration.setAllowedMethods(List.of());
            configuration.setAllowedHeaders(List.of());
            source.registerCorsConfiguration("/**", configuration);
        }

        return new CorsFilter(source);
    }

    @Bean
    @ConditionalOnClass(UserDetailsService.class)
    public UserDetailsService userDetailsService() {
        String name = securityProperties.getUser().getName();
        String password = securityProperties.getUser().getPassword();

        if (name == null || password == null) {
            return new InMemoryUserDetailsManager();
        }

        String encodePassword = passwordEncoder().encode(password);
        UserDetails user = User.withUsername(name)
                .password(encodePassword)
                .roles("USER")
                .build();

        return new InMemoryUserDetailsManager(user);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
