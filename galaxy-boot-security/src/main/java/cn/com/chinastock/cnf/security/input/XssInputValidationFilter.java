package cn.com.chinastock.cnf.security.input;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Set;

@Component
@Order(1)
public class XssInputValidationFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        if (isStaticResource(httpRequest.getRequestURI())) {
            chain.doFilter(request, response);
            return;
        }

        XssRequestWrapper xssRequest = new XssRequestWrapper(httpRequest);
        chain.doFilter(xssRequest, response);
    }

    private static final Set<String> STATIC_RESOURCE_EXTENSIONS = Set.of(
            ".css", ".js", ".png", ".jpg", ".gif", ".ico", ".svg",
            ".woff", ".woff2", ".ttf", ".eot", ".mp3", ".mp4",
            ".pdf", ".xlsx", ".doc", ".docx", ".xls", ".ppt", ".pptx", ".zip"
    );

    private static final Set<String> STATIC_RESOURCE_PATHS = Set.of(
            "/static/", "/assets/", "/resources/", "/images/", "/fonts/"
    );

    private boolean isStaticResource(String url) {
        return STATIC_RESOURCE_EXTENSIONS.stream().anyMatch(url::endsWith) ||
                STATIC_RESOURCE_PATHS.stream().anyMatch(url::contains);
    }

    public static class XssRequestWrapper extends HttpServletRequestWrapper {
        private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
        private static final String DEFAULT_CHARSET = "UTF-8";

        public XssRequestWrapper(HttpServletRequest request) {
            super(request);
        }

        @Override
        public String[] getParameterValues(String parameter) {
            return sanitizeValues(super.getParameterValues(parameter));
        }

        @Override
        public String getParameter(String parameter) {
            String value = super.getParameter(parameter);
            return value != null ? XssSanitizerUtil.stripXSS(value) : null;
        }

        @Override
        public String getHeader(String name) {
            String value = super.getHeader(name);
            return value != null ? XssSanitizerUtil.stripXSS(value) : null;
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            try {
                String sanitizedBody = sanitizeRequestBody(super.getInputStream());
                return createServletInputStream(sanitizedBody);
            } catch (IOException e) {
                throw new IOException("Error processing input stream", e);
            }
        }

        private String[] sanitizeValues(String[] values) {
            if (values == null) {
                return null;
            }
            String[] sanitizedValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                sanitizedValues[i] = values[i] != null ? XssSanitizerUtil.stripXSS(values[i]) : null;
            }
            return sanitizedValues;
        }

        private String sanitizeRequestBody(ServletInputStream inputStream) throws IOException {
            if (inputStream == null) {
                return null;
            }

            String requestBody = new String(inputStream.readAllBytes(), DEFAULT_CHARSET);
            if (requestBody.isEmpty()) {
                return requestBody;
            }

            try {
                JsonNode jsonNode = OBJECT_MAPPER.readTree(requestBody);
                sanitizeJsonNode(jsonNode);
                return OBJECT_MAPPER.writeValueAsString(jsonNode);
            } catch (IOException e) {
                // 如果不是 JSON 格式，直接对整个字符串进行清理
                return XssSanitizerUtil.stripXSS(requestBody);
            }
        }

        private ServletInputStream createServletInputStream(final String sanitizedBody) {
            final ByteArrayInputStream byteArrayInputStream =
                    new ByteArrayInputStream(sanitizedBody.getBytes());

            return new ServletInputStream() {
                @Override
                public int read() {
                    return byteArrayInputStream.read();
                }

                @Override
                public boolean isFinished() {
                    return byteArrayInputStream.available() == 0;
                }

                @Override
                public boolean isReady() {
                    return true;
                }

                @Override
                public void setReadListener(ReadListener readListener) {
                    throw new UnsupportedOperationException("ReadListener is not supported");
                }
            };
        }


        private void sanitizeJsonNode(JsonNode node) {
            if (node == null) {
                return;
            }

            if (node.isObject()) {
                sanitizeObjectNode((ObjectNode) node);
            } else if (node.isArray()) {
                sanitizeArrayNode((ArrayNode) node);
            }
        }

        private void sanitizeObjectNode(ObjectNode objectNode) {
            objectNode.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();

                if (value.isTextual()) {
                    String value1 = value.textValue();
                    objectNode.put(key, value1 != null ? XssSanitizerUtil.stripXSS(value1) : null);
                } else {
                    sanitizeJsonNode(value);
                }
            });
        }

        private void sanitizeArrayNode(ArrayNode arrayNode) {
            for (int i = 0; i < arrayNode.size(); i++) {
                JsonNode element = arrayNode.get(i);
                if (element.isTextual()) {
                    String value = element.textValue();
                    arrayNode.set(i, OBJECT_MAPPER.valueToTree(
                            value != null ? XssSanitizerUtil.stripXSS(value) : null
                    ));
                } else {
                    sanitizeJsonNode(element);
                }
            }
        }
    }

}
