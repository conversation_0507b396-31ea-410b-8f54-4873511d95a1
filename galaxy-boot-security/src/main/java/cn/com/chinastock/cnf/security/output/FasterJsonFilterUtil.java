package cn.com.chinastock.cnf.security.output;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import org.springframework.web.util.HtmlUtils;

public class FasterJsonFilterUtil {
    public static Object handleJacksonResponse(Object body) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            if (body instanceof String) {
                JsonNode jsonNode = objectMapper.readTree(body.toString());
                JsonNode sanitizedNode = sanitizeJsonNode(jsonNode, objectMapper);
                return objectMapper.writeValueAsString(sanitizedNode);
            }

            String jsonString = objectMapper.writeValueAsString(body);
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            JsonNode sanitizedNode = sanitizeJsonNode(jsonNode, objectMapper);

            return objectMapper.treeToValue(sanitizedNode, body.getClass());
        } catch (Exception e) {
            return body;
        }
    }

    private static JsonNode sanitizeJsonNode(JsonNode node, ObjectMapper objectMapper) {
        if (node.isTextual()) {
            return new TextNode(HtmlUtils.htmlEscape(node.asText()));
        } else if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode newNode = objectMapper.createObjectNode();
            objectNode.fields().forEachRemaining(entry ->
                    newNode.set(entry.getKey(), sanitizeJsonNode(entry.getValue(), objectMapper))
            );
            return newNode;
        } else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            ArrayNode newNode = objectMapper.createArrayNode();
            arrayNode.forEach(element -> newNode.add(sanitizeJsonNode(element, objectMapper)));
            return newNode;
        }
        return node;
    }
}
