package cn.com.chinastock.cnf.security.csrf;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableWebSecurity
public class CustomCsrfRequestMatcher implements RequestMatcher {
    private List<AntPathRequestMatcher> whitelistMatchers = new ArrayList<>();
    private List<AntPathRequestMatcher> blacklistMatchers = new ArrayList<>();

    public CustomCsrfRequestMatcher(List<String> whitelist, List<String> blacklist) {
        if (whitelist != null) {
            for (String pattern : whitelist) {
                whitelistMatchers.add(new AntPathRequestMatcher(pattern));
            }
        }

        if (blacklist != null) {
            for (String pattern : blacklist) {
                blacklistMatchers.add(new AntPathRequestMatcher(pattern));
            }
        }
    }

    @Override
    public boolean matches(HttpServletRequest request) {
        for (AntPathRequestMatcher blacklistMatcher : blacklistMatchers) {
            if (blacklistMatcher.matches(request)) {
                return true;
            }
        }

        for (AntPathRequestMatcher whitelistMatcher : whitelistMatchers) {
            if (whitelistMatcher.matches(request)) {
                return false;
            }
        }

        return true;
    }
}