package cn.com.chinastock.cnf.security.output;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "galaxy.fastjson")
public class CopiedFastJsonProperties {
    private boolean writeMapNullValue;
    private boolean writeNullStringAsEmpty;

    private boolean supportSmartMatch;

    public boolean isWriteMapNullValue() {
        return writeMapNullValue;
    }

    public void setWriteMapNullValue(boolean writeMapNullValue) {
        this.writeMapNullValue = writeMapNullValue;
    }

    public boolean isWriteNullStringAsEmpty() {
        return writeNullStringAsEmpty;
    }

    public void setWriteNullStringAsEmpty(boolean writeNullStringAsEmpty) {
        this.writeNullStringAsEmpty = writeNullStringAsEmpty;
    }

    public boolean isSupportSmartMatch() {
        return supportSmartMatch;
    }

    public void setSupportSmartMatch(boolean supportSmartMatch) {
        this.supportSmartMatch = supportSmartMatch;
    }
}
