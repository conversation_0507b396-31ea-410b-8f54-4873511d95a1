package cn.com.chinastock.cnf.security.actuator;

import cn.com.chinastock.cnf.security.controller.SecurityTestController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@EnableAutoConfiguration
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {SecurityTestController.class},
        properties = {
                "galaxy.security.actuator.protect=true",
                "galaxy.security.actuator.whitelist[0]=127.0.0.1"
        })
@AutoConfigureMockMvc
public class ProtectedActuatorWithWhitelistTests {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    public void shouldAllowWhitelistedIp() {
        ResponseEntity<String> response = restTemplate.getForEntity(
                "/actuator/health", String.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void shouldBlockNonWhitelistedIp() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = get("/actuator/health")
                .with(request -> {
                    request.setRemoteAddr("*************"); // 模拟 IP 地址
                    return request;
                });

        mockMvc.perform(requestBuilder)
                .andExpect(status().isForbidden())
                .andReturn();
    }
}