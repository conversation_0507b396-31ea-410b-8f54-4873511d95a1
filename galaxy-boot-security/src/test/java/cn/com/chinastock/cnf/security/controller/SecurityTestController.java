package cn.com.chinastock.cnf.security.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/security")
public class SecurityTestController {
    @PostMapping("/echo")
    public String test(@RequestBody(required = false) String content) {
        return content;
    }

    @GetMapping("/echo")
    public String testGet(@RequestParam(required = false) String content) {
        return content;
    }

    @PostMapping("/echo-object")
    public TestModel test(@RequestBody TestModel model) {
        return model;
    }

    @PostMapping("/test")
    public ResponseEntity<String> apiTest(@RequestBody(required = false) String content) {
        return ResponseEntity.ok(content);
    }

    @GetMapping("/unsafe-response")
    public ResponseEntity<String> unsafeResponse() {
        return ResponseEntity.ok("{\"content\":\"<script>alert('xss')</script>\"}");
    }

    @GetMapping("/safe-response")
    public TestModel safeResponse() {
        return new TestModel("<script>alert('xss')</script>", "<script>alert('xss')</script>");
    }
}
