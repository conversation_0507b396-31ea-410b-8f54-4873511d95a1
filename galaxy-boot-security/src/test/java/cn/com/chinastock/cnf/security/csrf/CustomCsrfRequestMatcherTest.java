package cn.com.chinastock.cnf.security.csrf;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CustomCsrfRequestMatcherTest {
    @Test
    void should_return_true_when_request_matches_blacklist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        CustomCsrfRequestMatcher matcher = new CustomCsrfRequestMatcher(whitelist, blacklist);

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getServletPath()).thenReturn("/secure/resource");

        // When
        boolean result = matcher.matches(request);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void should_return_false_when_request_matches_whitelist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        CustomCsrfRequestMatcher matcher = new CustomCsrfRequestMatcher(whitelist, blacklist);

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getServletPath()).thenReturn("/public/resource");

        // When
        boolean result = matcher.matches(request);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void should_return_true_when_request_does_not_match_whitelist_or_blacklist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        CustomCsrfRequestMatcher matcher = new CustomCsrfRequestMatcher(whitelist, blacklist);

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getServletPath()).thenReturn("/other/resource");

        // When
        boolean result = matcher.matches(request);

        // Then
        assertThat(result).isTrue();
    }

}
