package cn.com.chinastock.cnf.security;

import cn.com.chinastock.cnf.security.controller.SecurityTestController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {GalaxySecurityAutoConfiguration.class, SecurityTestController.class}
)
@EnableAutoConfiguration
@AutoConfigureMockMvc
@TestPropertySource(properties = {
        "management.endpoints.web.exposure.include=*"
})
class GalaxySecurityAutoConfigurationTest {
    @LocalManagementPort
    private int managementPort;

    @Autowired
    private TestRestTemplate restTemplate;

    private String baseUrl;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + managementPort;
    }

    @Nested
    @TestPropertySource(properties = {
            "galaxy.security.actuator.protect=true"
    })
    @DisplayName("Actuator Endpoint Tests - Protected")
    class ProtectedActuatorTests {
        @Test
        void shouldBlockShutdown() {
            ResponseEntity<String> response = restTemplate.getForEntity(
                    baseUrl + "/actuator/shutdown", String.class);
            assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        }
    }

    @Nested
    @TestPropertySource(properties = {
            "galaxy.security.user.name=user",
            "galaxy.security.user.password=password"
    })
    @DisplayName("User Configuration Tests")
    class UserConfigurationTests {
        @Test
        void shouldAuthenticateWithDefaultUser() {
            TestRestTemplate authenticatedRestTemplate = new TestRestTemplate("user", "password");
            ResponseEntity<String> response = authenticatedRestTemplate.getForEntity(
                    baseUrl + "/api/security/echo", String.class);
            assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        }
    }

    @Nested
    @TestPropertySource(properties = {
            "galaxy.security.csrf.protect=true",
            "galaxy.security.csrf.whitelist[0]=/api/**"
    })
    @DisplayName("CSRF Protection Tests - Protected")
    class ProtectedCsrfTests {
        @Test
        void shouldBlockNonWhitelistedPostRequests() {
            ResponseEntity<String> response = restTemplate.postForEntity(
                    baseUrl + "/api/security/echo", "test content", String.class);
            assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
            assertThat(response.getBody()).isEqualTo("test content");
        }
    }

    @Nested
    @TestPropertySource(properties = {
            "galaxy.security.csrf.protect=false"
    })
    @DisplayName("CSRF Protection Tests - Unprotected")
    class UnprotectedCsrfTests {
        @Test
        void shouldAllowAllPostRequests() {
            ResponseEntity<String> response = restTemplate.postForEntity(
                    baseUrl + "/api/security/echo", null, String.class);
            assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        }
    }
}
