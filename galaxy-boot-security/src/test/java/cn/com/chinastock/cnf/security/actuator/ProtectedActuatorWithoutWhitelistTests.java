package cn.com.chinastock.cnf.security.actuator;

import cn.com.chinastock.cnf.security.controller.SecurityTestController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;

@EnableAutoConfiguration
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {SecurityTestController.class},
        properties = {
                "galaxy.security.actuator.protect=true"
        })
public class ProtectedActuatorWithoutWhitelistTests {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    public void shouldBlockAllIps() {
        ResponseEntity<String> response = restTemplate.getForEntity(
                "/actuator/health", String.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }
}