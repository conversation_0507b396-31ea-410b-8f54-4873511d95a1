package cn.com.chinastock.cnf.security;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.options;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {GalaxySecurityAutoConfiguration.class}
)
@EnableAutoConfiguration
@AutoConfigureMockMvc
@TestPropertySource(properties = {
        "galaxy.security.cors.protect=true",
        "galaxy.security.cors.whitelist[0]=http://example.com"
})
public class CorsProtectionTest {
    @Autowired
    private MockMvc mockMvc;

    @Test
    public void shouldAllowCorsForWhitelistedPath() throws Exception {
        mockMvc.perform(options("/api/match/some-resource")
                        .header("Origin", "http://example.com")
                        .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk())
                .andExpect(header().exists("Access-Control-Allow-Origin"))
                .andExpect(header().string("Access-Control-Allow-Origin", "http://example.com"))
                .andExpect(header().exists("Access-Control-Allow-Methods"));

        mockMvc.perform(options("/api/other")
                        .header("Origin", "http://example2.com")
                        .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isForbidden());
    }
}