package cn.com.chinastock.cnf.security;

import cn.com.chinastock.cnf.security.controller.SecurityTestController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {GalaxySecurityAutoConfiguration.class, SecurityTestController.class}
)
@EnableAutoConfiguration
@AutoConfigureMockMvc
public class SecurityHeadersTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testSecurityHeaders() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/test"))
                .andExpect(MockMvcResultMatchers.header().string("X-XSS-Protection", "1; mode=block"))
                .andExpect(MockMvcResultMatchers.header().string("Content-Security-Policy", "default-src 'self'"))
                .andExpect(MockMvcResultMatchers.header().string("Referrer-Policy", "strict-origin"))
                .andExpect(MockMvcResultMatchers.header().string("X-Frame-Options", "DENY"));
    }
}