# Galaxy Boot Redis Example

这是一个展示 Galaxy Boot Redis Starter 功能的示例应用程序。

## 功能特性

- **Redis 操作测试** - 测试各种 Redis 数据类型操作（String、Hash、List、Set、ZSet）
- **序列化测试** - 测试不同序列化器的功能和性能
- **配置测试** - 验证 Redis 配置的有效性
- **指标监控** - 展示 Redis 操作的监控指标
- **健康检查** - 提供 Redis 连接健康状态检查
- **性能基准测试** - 测试并发操作和序列化性能

## 前置条件

1. **Java 21+**
2. **Maven 3.6+**
3. **Redis 服务器** - 本地运行在 localhost:6379（可在配置文件中修改）

## 快速开始

### 1. 启动 Redis 服务器

```bash
# 使用 Docker 启动 Redis（推荐）
docker run -d --name redis -p 6379:6379 redis:latest

# 或者使用本地安装的 Redis
redis-server

# 验证 Redis 是否正常运行
redis-cli ping
# 应该返回 PONG
```

**注意**: 如果Redis运行在不同的主机或端口上，请修改`src/main/resources/application.yml`中的Redis配置：

```yaml
spring:
  data:
    redis:
      host: your-redis-host    # 默认: localhost
      port: your-redis-port    # 默认: 6379
      password: your-password  # 如果有密码的话
      database: 0              # 默认数据库
```

### 2. 编译和运行应用

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

### 3. 访问测试接口

应用启动后，可以通过以下接口进行测试：

#### 综合测试接口
- `POST /api/redis/test/all` - 运行所有测试用例
- `GET /api/redis/test/quick-health` - 快速健康检查
- `POST /api/redis/test/benchmark` - 性能基准测试
- `GET /api/redis/test/report` - 获取测试报告
- `POST /api/redis/test/cleanup` - 清理测试数据

#### Redis 操作测试接口
- `POST /api/redis/test/string` - String 操作测试
- `POST /api/redis/test/hash` - Hash 操作测试
- `POST /api/redis/test/list` - List 操作测试
- `POST /api/redis/test/set` - Set 操作测试
- `POST /api/redis/test/zset` - ZSet 操作测试
- `POST /api/redis/test/expiration` - 过期操作测试
- `POST /api/redis/test/transaction` - 事务操作测试
- `POST /api/redis/test/pipeline` - 管道操作测试
- `POST /api/redis/test/concurrent` - 并发操作测试
- `POST /api/redis/test/operations/all` - 运行所有Redis操作测试

#### 序列化测试接口
- `POST /api/redis/test/serialization/default` - 默认序列化器测试
- `POST /api/redis/test/serialization/string` - 字符串序列化器测试
- `POST /api/redis/test/serialization/complex-object` - 复杂对象序列化测试
- `POST /api/redis/test/serialization/large-object` - 大对象序列化测试
- `POST /api/redis/test/serialization/special-characters` - 特殊字符序列化测试
- `POST /api/redis/test/serialization/null-values` - 空值处理测试
- `GET /api/redis/test/serialization/config` - 获取序列化器配置
- `POST /api/redis/test/serialization/performance` - 序列化性能测试
- `POST /api/redis/test/serialization/all` - 运行所有序列化测试



#### 配置测试接口
- `GET /api/redis/config` - 获取当前 Redis 配置信息
- `GET /api/redis/health` - 获取 Redis 健康状态
- `GET /api/redis/connection-pool` - 获取连接池状态
- `GET /api/redis/server-info` - 获取 Redis 服务器信息
- `POST /api/redis/test-connection` - 测试 Redis 连接
- `GET /api/redis/galaxy-config` - 获取 Galaxy Redis 配置详情
- `POST /api/redis/test-config` - 测试配置有效性

## 配置说明

应用的配置文件位于 `src/main/resources/application.yml`，包含以下主要配置：

### Redis 基础配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
```

### Galaxy Redis 增强配置
```yaml
galaxy:
  redis:
    enabled: true
    serialization:
      use-fast-json: true
      key-serializer: string
      value-serializer: fastjson
    metrics:
      enabled: true
      prefix: galaxy.redis
```

## 示例用法

### 1. 运行所有测试
```bash
curl -X POST http://localhost:8080/api/redis/test/all
```

### 2. 检查健康状态
```bash
curl http://localhost:8080/api/redis/test/quick-health
```

### 3. 性能基准测试
```bash
curl -X POST "http://localhost:8080/api/redis/test/benchmark?iterations=1000&threads=10"
```



## 监控和指标

应用集成了 Spring Boot Actuator，可以通过以下端点查看监控信息：

- `/actuator/metrics` - 所有指标
- `/actuator/prometheus` - Prometheus 格式指标（如果启用）
- `/actuator/health` - 健康检查

Redis 相关的指标可以通过 Actuator 端点获取，无需额外的自定义指标接口。

## 故障排除

### 1. Redis 连接失败
- 确保 Redis 服务器正在运行
- 检查 Redis 连接配置（host、port、password）
- 确认防火墙设置允许连接

### 2. 序列化错误
- 检查对象是否实现了 Serializable 接口
- 确认序列化器配置正确



## 技术栈

- **Spring Boot 3.3.7+**
- **Galaxy Boot Redis Starter**
- **Spring Data Redis**
- **Lettuce Redis Client**
- **FastJson2 序列化器**
- **Micrometer 指标收集**

## 许可证

本示例应用遵循 Galaxy Boot 项目的许可证。
