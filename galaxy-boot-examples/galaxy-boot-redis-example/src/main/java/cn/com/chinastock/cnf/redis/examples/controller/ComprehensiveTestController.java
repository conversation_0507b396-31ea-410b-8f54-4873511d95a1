package cn.com.chinastock.cnf.redis.examples.controller;

import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import cn.com.chinastock.cnf.redis.examples.service.ConfigTestService;
import cn.com.chinastock.cnf.redis.examples.service.RedisOperationTestService;
import cn.com.chinastock.cnf.redis.examples.service.SerializationTestService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 综合测试控制器.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/redis/test")
public class ComprehensiveTestController {

    /** 序列化测试服务. */
    private final SerializationTestService serializationTestService;

    /** Redis操作测试服务. */
    private final RedisOperationTestService redisOperationTestService;

    /** 配置测试服务. */
    private final ConfigTestService configTestService;

    public ComprehensiveTestController(SerializationTestService serializationTestService, RedisOperationTestService redisOperationTestService, ConfigTestService configTestService) {
        this.serializationTestService = serializationTestService;
        this.redisOperationTestService = redisOperationTestService;
        this.configTestService = configTestService;
    }

    /**
     * 运行所有测试用例.
     *
     * @return 测试结果映射
     */
    @PostMapping("/all")
    public Map<String, Object> runAllTests() {
        Map<String, Object> allResults = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // 序列化测试
            List<TestResult> serializationResults =
                serializationTestService.runAllSerializationTests();
            allResults.put("serialization", serializationResults);

            // Redis 操作测试
            List<TestResult> operationResults =
                redisOperationTestService.runAllRedisOperationTests();
            allResults.put("operations", operationResults);

            // 配置测试
            TestResult configResult = configTestService.testConfigEffectiveness();
            allResults.put("config", configResult);

            // 健康检查
            TestResult healthResult = configTestService.getRedisHealth();
            allResults.put("health", healthResult);

            // 统计信息
            final int additionalTests = 3;
            long totalTests = serializationResults.size() + operationResults.size()
                + additionalTests;
            long successfulTests = serializationResults.stream()
                .mapToLong(r -> r.isSuccess() ? 1 : 0).sum()
                + operationResults.stream()
                .mapToLong(r -> r.isSuccess() ? 1 : 0).sum()
                + (configResult.isSuccess() ? 1 : 0)
                + (healthResult.isSuccess() ? 1 : 0);
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalTests", totalTests);
            summary.put("successfulTests", successfulTests);
            summary.put("failedTests", totalTests - successfulTests);
            final double percentMultiplier = 100.0;
            summary.put("successRate", (double) successfulTests / totalTests
                * percentMultiplier);
            summary.put("executionTime", executionTime);
            summary.put("timestamp", System.currentTimeMillis());
            
            allResults.put("summary", summary);
            
        } catch (Exception e) {
            allResults.put("error", e.getMessage());
        }
        
        return allResults;
    }

    /**
     * 快速健康检查.
     *
     * @return 健康检查结果映射
     */
    @GetMapping("/quick-health")
    public Map<String, Object> quickHealthCheck() {
        Map<String, Object> health = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // Redis 连接测试
            TestResult connectionTest = configTestService.testRedisConnection();
            health.put("connection", connectionTest);
            
            // 简单操作测试
            TestResult operationTest = redisOperationTestService.testStringOperations();
            health.put("operations", operationTest);
            
            // 序列化测试
            TestResult serializationTest = serializationTestService.testDefaultSerialization();
            health.put("serialization", serializationTest);
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            boolean allHealthy = connectionTest.isSuccess()
                && operationTest.isSuccess()
                && serializationTest.isSuccess();
            
            health.put("overall", Map.of(
                "status", allHealthy ? "UP" : "DOWN",
                "executionTime", executionTime,
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            health.put("error", e.getMessage());
            health.put("overall", Map.of("status", "DOWN"));
        }
        
        return health;
    }
    
    /**
     * 性能基准测试.
     *
     * @param iterations 迭代次数
     * @param threads 线程数
     * @return 基准测试结果映射
     */
    @PostMapping("/benchmark")
    public Map<String, Object> runBenchmarkTests(
        @RequestParam(defaultValue = "1000") final int iterations,
        @RequestParam(defaultValue = "10") final int threads) {
        Map<String, Object> benchmark = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // 序列化性能测试
            TestResult serializationPerf =
                serializationTestService.testSerializationPerformance(iterations);
            benchmark.put("serializationPerformance", serializationPerf);

            // 并发操作测试
            TestResult concurrentTest = redisOperationTestService
                .testConcurrentOperations(threads, iterations / threads);
            benchmark.put("concurrentOperations", concurrentTest);
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalIterations", iterations);
            summary.put("threadCount", threads);
            summary.put("totalExecutionTime", executionTime);
            summary.put("timestamp", System.currentTimeMillis());
            
            benchmark.put("summary", summary);
            
        } catch (Exception e) {
            benchmark.put("error", e.getMessage());
        }
        
        return benchmark;
    }
    
    /**
     * 获取测试报告.
     *
     * @return 测试报告映射
     */
    @GetMapping("/report")
    public Map<String, Object> getTestReport() {
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 配置信息
            report.put("config", configTestService.getRedisConfig());
            
            // 服务器信息
            report.put("serverInfo", configTestService.getRedisServerInfo());
            
            // 连接池状态
            report.put("connectionPool", configTestService.getConnectionPoolStatus());
            
            // 序列化器配置
            report.put("serializers", serializationTestService.getSerializationConfig());
            
            report.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            report.put("error", e.getMessage());
        }
        
        return report;
    }
}
