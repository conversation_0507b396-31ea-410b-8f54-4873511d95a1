package cn.com.chinastock.cnf.redis.examples.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 测试结果模型
 * 
 * <AUTHOR>
 */
public class TestResult {
    
    private String testCase;
    private boolean success;
    private String message;
    private Object data;
    private long executionTime;
    private LocalDateTime timestamp;
    private Map<String, Object> metrics;
    
    /**
     * 默认构造函数.
     */
    public TestResult() {
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数.
     *
     * @param testCase 测试用例名称
     * @param success 是否成功
     * @param message 消息
     */
    public TestResult(String testCase, boolean success, String message) {
        this();
        this.testCase = testCase;
        this.success = success;
        this.message = message;
    }

    /**
     * 创建成功的测试结果.
     *
     * @param testCase 测试用例名称
     * @param message 消息
     * @return 成功的测试结果
     */
    public static TestResult success(String testCase, String message) {
        return new TestResult(testCase, true, message);
    }

    /**
     * 创建成功的测试结果（带数据）.
     *
     * @param testCase 测试用例名称
     * @param message 消息
     * @param data 测试数据
     * @return 成功的测试结果
     */
    public static TestResult success(String testCase, String message, Object data) {
        TestResult result = new TestResult(testCase, true, message);
        result.setData(data);
        return result;
    }

    /**
     * 创建失败的测试结果.
     *
     * @param testCase 测试用例名称
     * @param message 消息
     * @return 失败的测试结果
     */
    public static TestResult failure(String testCase, String message) {
        return new TestResult(testCase, false, message);
    }

    /**
     * 创建失败的测试结果（带异常信息）.
     *
     * @param testCase 测试用例名称
     * @param message 消息
     * @param throwable 异常
     * @return 失败的测试结果
     */
    public static TestResult failure(String testCase, String message, Throwable throwable) {
        return new TestResult(testCase, false, message + ": " + throwable.getMessage());
    }
    
    /**
     * 获取测试用例名称.
     *
     * @return 测试用例名称
     */
    public String getTestCase() {
        return testCase;
    }

    /**
     * 设置测试用例名称.
     *
     * @param testCase 测试用例名称
     */
    public void setTestCase(String testCase) {
        this.testCase = testCase;
    }

    /**
     * 判断是否成功.
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 设置是否成功.
     *
     * @param success 是否成功
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * 获取消息.
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置消息.
     *
     * @param message 消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取测试数据.
     *
     * @return 测试数据
     */
    public Object getData() {
        return data;
    }

    /**
     * 设置测试数据.
     *
     * @param data 测试数据
     */
    public void setData(Object data) {
        this.data = data;
    }

    /**
     * 获取执行时间.
     *
     * @return 执行时间（毫秒）
     */
    public long getExecutionTime() {
        return executionTime;
    }

    /**
     * 设置执行时间.
     *
     * @param executionTime 执行时间（毫秒）
     */
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

    /**
     * 获取时间戳.
     *
     * @return 时间戳
     */
    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    /**
     * 设置时间戳.
     *
     * @param timestamp 时间戳
     */
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * 获取指标信息.
     *
     * @return 指标信息
     */
    public Map<String, Object> getMetrics() {
        return metrics;
    }

    /**
     * 设置指标信息.
     *
     * @param metrics 指标信息
     */
    public void setMetrics(Map<String, Object> metrics) {
        this.metrics = metrics;
    }
}
