package cn.com.chinastock.cnf.redis.examples.controller;

import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import cn.com.chinastock.cnf.redis.examples.service.ConfigTestService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 配置和健康检查测试控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/redis")
public class ConfigTestController {
    
    private final ConfigTestService configTestService;

    public ConfigTestController(ConfigTestService configTestService) {
        this.configTestService = configTestService;
    }

    /**
     * 获取当前 Redis 配置信息.
     *
     * @return Redis配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getRedisConfig() {
        return configTestService.getRedisConfig();
    }

    /**
     * 获取 Redis 健康状态.
     *
     * @return Redis健康状态检查结果
     */
    @GetMapping("/health")
    public TestResult getRedisHealth() {
        return configTestService.getRedisHealth();
    }

    /**
     * 测试连接池状态.
     *
     * @return 连接池状态信息
     */
    @GetMapping("/connection-pool")
    public Map<String, Object> getConnectionPoolStatus() {
        return configTestService.getConnectionPoolStatus();
    }

    /**
     * 获取 Redis 服务器信息.
     *
     * @return Redis服务器信息
     */
    @GetMapping("/server-info")
    public Map<String, Object> getRedisServerInfo() {
        return configTestService.getRedisServerInfo();
    }

    /**
     * 测试 Redis 连接.
     *
     * @return 连接测试结果
     */
    @PostMapping("/test-connection")
    public TestResult testRedisConnection() {
        return configTestService.testRedisConnection();
    }

    /**
     * 获取 Galaxy Redis 配置详情.
     *
     * @return Galaxy Redis配置详情
     */
    @GetMapping("/galaxy-config")
    public Map<String, Object> getGalaxyRedisConfig() {
        return configTestService.getGalaxyRedisConfig();
    }

    /**
     * 测试配置有效性.
     *
     * @return 配置有效性测试结果
     */
    @PostMapping("/test-config")
    public TestResult testConfigEffectiveness() {
        return configTestService.testConfigEffectiveness();
    }
}
