package cn.com.chinastock.cnf.redis.examples.service;

import cn.com.chinastock.cnf.redis.config.GalaxyBootRedisProperties;
import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * 配置测试服务.
 *
 * <AUTHOR>
 */
@Service
public class ConfigTestService {

    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisConnectionFactory redisConnectionFactory;

    private final RedisProperties redisProperties;

    /**
     * Galaxy Redis属性配置.
     */
    private final GalaxyBootRedisProperties galaxyBootRedisProperties;

    public ConfigTestService(RedisTemplate<String, Object> redisTemplate, RedisConnectionFactory redisConnectionFactory, RedisProperties redisProperties, GalaxyBootRedisProperties galaxyBootRedisProperties) {
        this.redisTemplate = redisTemplate;
        this.redisConnectionFactory = redisConnectionFactory;
        this.redisProperties = redisProperties;
        this.galaxyBootRedisProperties = galaxyBootRedisProperties;
    }

    /**
     * 获取当前 Redis 配置信息.
     *
     * @return Redis配置信息映射
     */
    public Map<String, Object> getRedisConfig() {
        Map<String, Object> config = new HashMap<>();

        try {
            // Spring Data Redis 配置
            Map<String, Object> springConfig = new HashMap<>();
            springConfig.put("host", redisProperties.getHost());
            springConfig.put("port", redisProperties.getPort());
            springConfig.put("database", redisProperties.getDatabase());
            springConfig.put("timeout", redisProperties.getTimeout());
            springConfig.put("ssl", redisProperties.getSsl() != null && redisProperties.getSsl().isEnabled());

            if (redisProperties.getLettuce() != null) {
                springConfig.put("lettuce", Map.of(
                        "pool", Map.of(
                                "maxActive", redisProperties.getLettuce().getPool().getMaxActive(),
                                "maxIdle", redisProperties.getLettuce().getPool().getMaxIdle(),
                                "minIdle", redisProperties.getLettuce().getPool().getMinIdle(),
                                "maxWait", redisProperties.getLettuce().getPool().getMaxWait()
                        )
                ));
            }

            config.put("springRedis", springConfig);

            // Galaxy Redis 配置
            if (galaxyBootRedisProperties != null) {
                Map<String, Object> galaxyConfig = new HashMap<>();

                // 序列化配置
                Map<String, Object> serializationConfig = new HashMap<>();
                serializationConfig.put("keySerializer", galaxyBootRedisProperties.getSerialization().getKeySerializer());
                serializationConfig.put("valueSerializer", galaxyBootRedisProperties.getSerialization().getValueSerializer());
                serializationConfig.put("hashKeySerializer", galaxyBootRedisProperties.getSerialization().getHashKeySerializer());
                serializationConfig.put("hashValueSerializer", galaxyBootRedisProperties.getSerialization().getHashValueSerializer());
                galaxyConfig.put("serialization", serializationConfig);

                config.put("galaxyRedis", galaxyConfig);
            }

            // RedisTemplate 配置
            Map<String, Object> templateConfig = new HashMap<>();
            templateConfig.put("keySerializer", redisTemplate.getKeySerializer().getClass().getSimpleName());
            templateConfig.put("valueSerializer", redisTemplate.getValueSerializer().getClass().getSimpleName());
            templateConfig.put("hashKeySerializer", redisTemplate.getHashKeySerializer().getClass().getSimpleName());
            templateConfig.put("hashValueSerializer", redisTemplate.getHashValueSerializer().getClass().getSimpleName());
            // templateConfig.put("enableTransactionSupport", redisTemplate.isEnableTransactionSupport());
            config.put("redisTemplate", templateConfig);

            // 连接工厂配置
            Map<String, Object> connectionConfig = new HashMap<>();
            connectionConfig.put("factoryClass", redisConnectionFactory.getClass().getSimpleName());
            config.put("connectionFactory", connectionConfig);

        } catch (Exception e) {
            config.put("error", e.getMessage());
        }

        return config;
    }

    /**
     * 获取 Redis 健康状态.
     *
     * @return Redis健康状态检查结果
     */
    public TestResult getRedisHealth() {
        try {
            long startTime = System.currentTimeMillis();

            // 测试连接
            String testKey = "health:check:" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(testKey, "OK");
            String result = (String) redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);

            long executionTime = System.currentTimeMillis() - startTime;

            if ("OK".equals(result)) {
                TestResult testResult = TestResult.success("TC-015", "Redis 连接健康");
                testResult.setExecutionTime(executionTime);
                testResult.setData(Map.of("status", "UP", "responseTime", executionTime));
                return testResult;
            } else {
                return TestResult.failure("TC-015", "Redis 连接异常");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-015", "Redis 健康检查失败", e);
        }
    }

    /**
     * 获取连接池状态.
     *
     * @return 连接池状态信息
     */
    public Map<String, Object> getConnectionPoolStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 连接工厂信息
            status.put("connectionFactoryClass", redisConnectionFactory.getClass().getSimpleName());

            // 尝试获取连接池统计信息（如果可用）
            // 注意：不同的连接工厂实现可能有不同的方式获取池状态
            status.put("factoryToString", redisConnectionFactory.toString());

        } catch (Exception e) {
            status.put("error", e.getMessage());
        }

        return status;
    }

    /**
     * 获取 Redis 服务器信息.
     *
     * @return Redis服务器信息
     */
    public Map<String, Object> getRedisServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();

        try {
            // 执行 INFO 命令获取服务器信息
            Properties info = redisTemplate.execute((RedisCallback<Properties>) connection -> connection.serverCommands().info());

            if (info != null) {
                // 转换为 Map
                info.forEach((key, value) -> serverInfo.put(key.toString(), value.toString()));
            }

            // 添加一些基本信息
            serverInfo.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            serverInfo.put("error", e.getMessage());
        }

        return serverInfo;
    }

    /**
     * 测试 Redis 连接.
     *
     * @return 连接测试结果
     */
    public TestResult testRedisConnection() {
        try {
            long startTime = System.currentTimeMillis();

            // 执行 PING 命令
            String pong = redisTemplate.execute(RedisConnectionCommands::ping);

            long executionTime = System.currentTimeMillis() - startTime;

            if ("PONG".equals(pong)) {
                TestResult result = TestResult.success("TC-018", "Redis 连接测试成功");
                result.setExecutionTime(executionTime);
                result.setData(Map.of("ping", pong, "responseTime", executionTime));
                return result;
            } else {
                return TestResult.failure("TC-018", "Redis PING 响应异常: " + pong);
            }

        } catch (Exception e) {
            return TestResult.failure("TC-018", "Redis 连接测试失败", e);
        }
    }

    /**
     * 获取 Galaxy Redis 配置详情.
     *
     * @return Galaxy Redis配置详情
     */
    public Map<String, Object> getGalaxyRedisConfig() {
        Map<String, Object> config = new HashMap<>();

        if (galaxyBootRedisProperties != null) {
            config.put("serialization", galaxyBootRedisProperties.getSerialization());
        } else {
            config.put("status", "Galaxy Redis Properties not available");
        }

        return config;
    }

    /**
     * 测试配置有效性.
     *
     * @return 配置有效性测试结果
     */
    public TestResult testConfigEffectiveness() {
        try {
            long startTime = System.currentTimeMillis();

            Map<String, Object> testResults = new HashMap<>();

            // 测试序列化器配置是否生效
            String testKey = "config:test:" + System.currentTimeMillis();
            Map<String, Object> testData = Map.of(
                    "string", "测试字符串",
                    "number", 12345,
                    "boolean", true
            );

            redisTemplate.opsForValue().set(testKey, testData);
            @SuppressWarnings("unchecked")
            Map<String, Object> retrievedData = (Map<String, Object>) redisTemplate.opsForValue().get(testKey);

            boolean serializationWorks = testData.equals(retrievedData);
            testResults.put("serializationTest", serializationWorks);

            // 测试过期时间配置
            String expireKey = "config:expire:" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(expireKey, "expire test", 1, java.util.concurrent.TimeUnit.SECONDS);
            Long ttl = redisTemplate.getExpire(expireKey, TimeUnit.MILLISECONDS);
            testResults.put("expirationTest", ttl > 0);

            // 清理测试数据
            redisTemplate.delete(testKey);

            long executionTime = System.currentTimeMillis() - startTime;

            boolean allTestsPassed = testResults.values().stream()
                    .allMatch(result -> result instanceof Boolean && (Boolean) result);

            if (allTestsPassed) {
                TestResult result = TestResult.success("TC-020", "配置有效性测试成功", testResults);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                TestResult result = TestResult.failure("TC-020", "部分配置测试失败");
                result.setData(testResults);
                result.setExecutionTime(executionTime);
                return result;
            }

        } catch (Exception e) {
            return TestResult.failure("TC-020", "配置有效性测试失败", e);
        }
    }
}
