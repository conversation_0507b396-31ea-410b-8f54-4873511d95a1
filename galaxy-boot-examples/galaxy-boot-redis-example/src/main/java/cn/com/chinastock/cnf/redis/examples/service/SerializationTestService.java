package cn.com.chinastock.cnf.redis.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import cn.com.chinastock.cnf.redis.examples.model.TestUser;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 序列化测试服务
 *
 * <AUTHOR>
 */
@Service
public class SerializationTestService {

    private final RedisTemplate<String, Object> redisTemplate;

    public SerializationTestService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 测试默认序列化器（FastJson2）.
     *
     * @return 测试结果
     */
    public TestResult testDefaultSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            // 创建测试用户
            TestUser user = createTestUser();
            String key = "test:serialization:default:" + System.currentTimeMillis();

            // 存储对象
            redisTemplate.opsForValue().set(key, user, 60, TimeUnit.SECONDS);

            // 读取对象
            TestUser retrievedUser = (TestUser) redisTemplate.opsForValue().get(key);

            // 验证数据完整性
            boolean isValid = validateUser(user, retrievedUser);

            long executionTime = System.currentTimeMillis() - startTime;

            if (isValid) {
                TestResult result = TestResult.success("TC-001", "默认序列化器测试成功", retrievedUser);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return TestResult.failure("TC-001", "序列化后数据不一致");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-001", "默认序列化器测试失败", e);
        }
    }

    /**
     * 测试字符串序列化器.
     *
     * @return 测试结果
     */
    public TestResult testStringSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            String testString = "Hello Galaxy Redis! 你好世界! 🚀";
            String key = "test:serialization:string:" + System.currentTimeMillis();

            // 存储字符串
            redisTemplate.opsForValue().set(key, testString, 60, TimeUnit.SECONDS);

            // 读取字符串
            String retrievedString = (String) redisTemplate.opsForValue().get(key);

            long executionTime = System.currentTimeMillis() - startTime;

            if (testString.equals(retrievedString)) {
                TestResult result = TestResult.success("TC-002", "字符串序列化器测试成功", retrievedString);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return TestResult.failure("TC-002", "字符串序列化后数据不一致");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-002", "字符串序列化器测试失败", e);
        }
    }

    /**
     * 测试复杂对象序列化.
     *
     * @return 测试结果
     */
    public TestResult testComplexObjectSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            // 创建复杂对象
            TestUser user = createComplexTestUser();
            String key = "test:serialization:complex:" + System.currentTimeMillis();

            // 存储对象
            GalaxyLogger.info("testComplexObjectSerialization, Set: {} = {}", key, user);
            redisTemplate.opsForValue().set(key, user, 60, TimeUnit.SECONDS);

            // 读取对象
            TestUser retrievedUser = (TestUser) redisTemplate.opsForValue().get(key);
            GalaxyLogger.info("testComplexObjectSerialization, Get: {} = {}", key, retrievedUser);

            // 验证复杂数据
            boolean isValid = validateComplexUser(user, retrievedUser);

            long executionTime = System.currentTimeMillis() - startTime;

            if (isValid) {
                TestResult result = TestResult.success("TC-003", "复杂对象序列化测试成功", retrievedUser);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return TestResult.failure("TC-003", "复杂对象序列化后数据不一致");
            }

        } catch (Exception e) {
            GalaxyLogger.error("复杂对象序列化测试失败", e);
            return TestResult.failure("TC-003", "复杂对象序列化测试失败", e);
        }
    }

    /**
     * 测试大对象序列化.
     *
     * @return 测试结果
     */
    public TestResult testLargeObjectSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            // 创建大对象
            Map<String, Object> largeObject = createLargeObject();
            String key = "test:serialization:large:" + System.currentTimeMillis();

            // 存储大对象
            redisTemplate.opsForValue().set(key, largeObject, 60, TimeUnit.SECONDS);

            // 读取大对象
            @SuppressWarnings("unchecked")
            Map<String, Object> retrievedObject = (Map<String, Object>) redisTemplate.opsForValue().get(key);

            long executionTime = System.currentTimeMillis() - startTime;

            if (largeObject.size() == retrievedObject.size()) {
                TestResult result = TestResult.success("TC-004", "大对象序列化测试成功");
                result.setExecutionTime(executionTime);
                result.setData(Map.of("originalSize", largeObject.size(), "retrievedSize", retrievedObject.size()));
                return result;
            } else {
                return TestResult.failure("TC-004", "大对象序列化后大小不一致");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-004", "大对象序列化测试失败", e);
        }
    }

    /**
     * 测试特殊字符序列化.
     *
     * @return 测试结果
     */
    public TestResult testSpecialCharactersSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            String specialString = "特殊字符测试: !@#$%^&*()_+ 中文 🚀🎉 \n\t\r";
            String key = "test:serialization:special:" + System.currentTimeMillis();

            redisTemplate.opsForValue().set(key, specialString, 60, TimeUnit.SECONDS);
            String retrievedString = (String) redisTemplate.opsForValue().get(key);

            long executionTime = System.currentTimeMillis() - startTime;

            if (specialString.equals(retrievedString)) {
                TestResult result = TestResult.success("TC-005", "特殊字符序列化测试成功", retrievedString);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return TestResult.failure("TC-005", "特殊字符序列化后数据不一致");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-005", "特殊字符序列化测试失败", e);
        }
    }

    /**
     * 测试空值处理.
     *
     * @return 测试结果
     */
    public TestResult testNullValuesSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            String key = "test:serialization:null:" + System.currentTimeMillis();

            // 测试存储 null 值
            redisTemplate.opsForValue().set(key, null, 60, TimeUnit.SECONDS);
            Object retrievedValue = redisTemplate.opsForValue().get(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("retrievedValue", retrievedValue);
            data.put("isNull", retrievedValue == null);

            TestResult result = TestResult.success("TC-006", "空值序列化测试完成");
            result.setExecutionTime(executionTime);
            result.setData(data);
            return result;

        } catch (Exception e) {
            GalaxyLogger.error("空值序列化测试失败", e);
            return TestResult.failure("TC-006", "空值序列化测试失败", e);
        }
    }

    /**
     * 获取序列化器配置.
     *
     * @return 序列化器配置信息
     */
    public Map<String, String> getSerializationConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("keySerializer", redisTemplate.getKeySerializer().getClass().getSimpleName());
        config.put("valueSerializer", redisTemplate.getValueSerializer().getClass().getSimpleName());
        config.put("hashKeySerializer", redisTemplate.getHashKeySerializer().getClass().getSimpleName());
        config.put("hashValueSerializer", redisTemplate.getHashValueSerializer().getClass().getSimpleName());
        return config;
    }

    /**
     * 序列化性能测试.
     *
     * @param iterations 测试迭代次数
     * @return 测试结果
     */
    public TestResult testSerializationPerformance(int iterations) {
        try {
            TestUser user = createTestUser();
            String keyPrefix = "test:performance:" + System.currentTimeMillis() + ":";

            long startTime = System.currentTimeMillis();

            for (int i = 0; i < iterations; i++) {
                String key = keyPrefix + i;
                redisTemplate.opsForValue().set(key, user, 60, TimeUnit.SECONDS);
                TestUser retrievedUser = (TestUser) redisTemplate.opsForValue().get(key);
                // 验证反序列化是否成功
                if (retrievedUser == null || !Objects.equals(user.getId(), retrievedUser.getId())) {
                    throw new RuntimeException("序列化测试失败：数据不一致");
                }
            }

            long executionTime = System.currentTimeMillis() - startTime;
            double avgTime = (double) executionTime / iterations;

            TestResult result = TestResult.success("TC-023", "序列化性能测试完成");
            result.setExecutionTime(executionTime);
            result.setData(Map.of(
                    "iterations", iterations,
                    "totalTime", executionTime,
                    "averageTime", avgTime,
                    "operationsPerSecond", iterations * 1000.0 / executionTime
            ));
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-023", "序列化性能测试失败", e);
        }
    }

    /**
     * 测试FastJson序列化器（专门测试）.
     *
     * @return 测试结果
     */
    public TestResult testFastJsonSerialization() {
        try {
            long startTime = System.currentTimeMillis();

            // 创建测试用户
            TestUser user = createTestUser();
            String key = "test:serialization:fastjson:" + System.currentTimeMillis();

            // 存储对象
            redisTemplate.opsForValue().set(key, user, 60, TimeUnit.SECONDS);

            // 读取对象并处理可能的类型转换
            Object retrievedObj = redisTemplate.opsForValue().get(key);
            TestUser retrievedUser = convertToTestUser(retrievedObj);

            // 验证数据完整性
            boolean isValid = validateUser(user, retrievedUser);

            long executionTime = System.currentTimeMillis() - startTime;

            if (isValid) {
                TestResult result = TestResult.success("TC-007", "FastJson序列化器测试成功", retrievedUser);
                result.setExecutionTime(executionTime);
                return result;
            } else {
                return TestResult.failure("TC-007", "FastJson序列化后数据不一致");
            }

        } catch (Exception e) {
            return TestResult.failure("TC-007", "FastJson序列化器测试失败", e);
        }
    }

    /**
     * 运行所有序列化测试.
     *
     * @return 所有序列化测试结果列表
     */
    public List<TestResult> runAllSerializationTests() {
        List<TestResult> results = new ArrayList<>();
        results.add(testDefaultSerialization());
        results.add(testStringSerialization());
        results.add(testComplexObjectSerialization());
        results.add(testLargeObjectSerialization());
        results.add(testSpecialCharactersSerialization());
        results.add(testNullValuesSerialization());
        results.add(testFastJsonSerialization());
        return results;
    }

    // 辅助方法
    private TestUser createTestUser() {
        TestUser user = new TestUser(1L, "张三", "<EMAIL>");
        user.setTags(Arrays.asList("developer", "java", "redis"));
        user.setMetadata(Map.of("department", "IT", "level", "senior"));
        return user;
    }

    private TestUser createComplexTestUser() {
        TestUser user = createTestUser();

        // 添加复杂的嵌套数据
        Map<String, Object> complexMetadata = new HashMap<>();
        complexMetadata.put("profile", Map.of(
                "age", 30,
                "skills", Arrays.asList("Java", "Spring", "Redis", "Docker"),
                "address", Map.of("city", "北京", "district", "朝阳区")
        ));
        complexMetadata.put("projects", Arrays.asList(
                Map.of("name", "项目A", "status", "active"),
                Map.of("name", "项目B", "status", "completed")
        ));

        user.setMetadata(complexMetadata);
        return user;
    }

    private Map<String, Object> createLargeObject() {
        Map<String, Object> largeObject = new HashMap<>();
        for (int i = 0; i < 1000; i++) {
            largeObject.put("key" + i, "value" + i + "_" + UUID.randomUUID());
        }
        return largeObject;
    }

    private boolean validateUser(TestUser original, TestUser retrieved) {
        if (retrieved == null) return false;
        return Objects.equals(original.getId(), retrieved.getId()) &&
                Objects.equals(original.getName(), retrieved.getName()) &&
                Objects.equals(original.getEmail(), retrieved.getEmail());
    }

    private boolean validateComplexUser(TestUser original, TestUser retrieved) {
        if (!validateUser(original, retrieved)) return false;
        return Objects.equals(original.getTags(), retrieved.getTags()) &&
                Objects.equals(original.getMetadata(), retrieved.getMetadata());
    }

    /**
     * 将Redis返回的对象转换为TestUser.
     *
     * @param obj Redis返回的对象
     * @return TestUser实例
     */
    private TestUser convertToTestUser(Object obj) {
        switch (obj) {
            case null -> {
                return null;
            }
            case TestUser testUser -> {
                return testUser;
            }


            // 如果是JSONObject，转换为TestUser
            case JSONObject jsonObject -> {
                return jsonObject.toJavaObject(TestUser.class);
            }
            default -> {
            }
        }

        // 如果是其他类型，尝试通过JSON转换
        try {
            String jsonStr = JSON.toJSONString(obj);
            return JSON.parseObject(jsonStr, TestUser.class);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to convert object to TestUser: {}", obj, e);
            return null;
        }
    }
}
