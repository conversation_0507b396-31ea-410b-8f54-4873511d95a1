package cn.com.chinastock.cnf.redis.examples.controller;

import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import cn.com.chinastock.cnf.redis.examples.service.RedisOperationTestService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Redis操作测试控制器.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/redis/test")
public class RedisOperationTestController {

    /** Redis操作测试服务. */
    private final RedisOperationTestService redisOperationTestService;

    public RedisOperationTestController(RedisOperationTestService redisOperationTestService) {
        this.redisOperationTestService = redisOperationTestService;
    }

    /**
     * 测试String操作.
     *
     * @return 测试结果
     */
    @PostMapping("/string")
    public TestResult testStringOperations() {
        return redisOperationTestService.testStringOperations();
    }

    /**
     * 测试Hash操作.
     *
     * @return 测试结果
     */
    @PostMapping("/hash")
    public TestResult testHashOperations() {
        return redisOperationTestService.testHashOperations();
    }

    /**
     * 测试List操作.
     *
     * @return 测试结果
     */
    @PostMapping("/list")
    public TestResult testListOperations() {
        return redisOperationTestService.testListOperations();
    }

    /**
     * 测试Set操作.
     *
     * @return 测试结果
     */
    @PostMapping("/set")
    public TestResult testSetOperations() {
        return redisOperationTestService.testSetOperations();
    }

    /**
     * 测试ZSet操作.
     *
     * @return 测试结果
     */
    @PostMapping("/zset")
    public TestResult testZSetOperations() {
        return redisOperationTestService.testZSetOperations();
    }

    /**
     * 测试过期操作.
     *
     * @return 测试结果
     */
    @PostMapping("/expiration")
    public TestResult testExpirationOperations() {
        return redisOperationTestService.testExpirationOperations();
    }

    /**
     * 测试事务操作.
     *
     * @return 测试结果
     */
    @PostMapping("/transaction")
    public TestResult testTransactionOperations() {
        return redisOperationTestService.testTransactionOperations();
    }

    /**
     * 测试管道操作.
     *
     * @return 测试结果
     */
    @PostMapping("/pipeline")
    public TestResult testPipelineOperations() {
        return redisOperationTestService.testPipelineOperations();
    }

    /**
     * 运行所有Redis操作测试.
     *
     * @return 所有测试结果列表
     */
    @PostMapping("/operations/all")
    public List<TestResult> runAllRedisOperationTests() {
        return redisOperationTestService.runAllRedisOperationTests();
    }

    /**
     * 测试并发操作.
     *
     * @param threadCount 线程数量
     * @param operationsPerThread 每个线程的操作数量
     * @return 测试结果
     */
    @PostMapping("/concurrent")
    public TestResult testConcurrentOperations(@RequestParam(defaultValue = "10") int threadCount,
                                               @RequestParam(defaultValue = "100") int operationsPerThread) {
        return redisOperationTestService.testConcurrentOperations(threadCount, operationsPerThread);
    }
}
