package cn.com.chinastock.cnf.redis.examples.controller;

import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import cn.com.chinastock.cnf.redis.examples.service.SerializationTestService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 序列化测试控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/redis/test/serialization")
public class SerializationTestController {

    private final SerializationTestService serializationTestService;

    public SerializationTestController(SerializationTestService serializationTestService) {
        this.serializationTestService = serializationTestService;
    }

    /**
     * 测试默认序列化器（FastJson2）.
     *
     * @return 测试结果
     */
    @PostMapping("/default")
    public TestResult testDefaultSerialization() {
        return serializationTestService.testDefaultSerialization();
    }

    /**
     * 测试字符串序列化器.
     *
     * @return 测试结果
     */
    @PostMapping("/string")
    public TestResult testStringSerialization() {
        return serializationTestService.testStringSerialization();
    }

    /**
     * 测试复杂对象序列化.
     *
     * @return 测试结果
     */
    @PostMapping("/complex-object")
    public TestResult testComplexObjectSerialization() {
        return serializationTestService.testComplexObjectSerialization();
    }

    /**
     * 测试大对象序列化.
     *
     * @return 测试结果
     */
    @PostMapping("/large-object")
    public TestResult testLargeObjectSerialization() {
        return serializationTestService.testLargeObjectSerialization();
    }

    /**
     * 测试特殊字符序列化.
     *
     * @return 测试结果
     */
    @PostMapping("/special-characters")
    public TestResult testSpecialCharactersSerialization() {
        return serializationTestService.testSpecialCharactersSerialization();
    }

    /**
     * 测试空值处理.
     *
     * @return 测试结果
     */
    @PostMapping("/null-values")
    public TestResult testNullValuesSerialization() {
        return serializationTestService.testNullValuesSerialization();
    }

    /**
     * 获取当前序列化器配置.
     *
     * @return 序列化器配置信息
     */
    @GetMapping("/config")
    public Map<String, String> getSerializationConfig() {
        return serializationTestService.getSerializationConfig();
    }

    /**
     * 序列化性能对比测试.
     *
     * @param iterations 测试迭代次数
     * @return 测试结果
     */
    @PostMapping("/performance")
    public TestResult testSerializationPerformance(@RequestParam(defaultValue = "1000") int iterations) {
        return serializationTestService.testSerializationPerformance(iterations);
    }

    /**
     * 测试FastJson序列化器.
     *
     * @return 测试结果
     */
    @PostMapping("/fastjson")
    public TestResult testFastJsonSerialization() {
        return serializationTestService.testFastJsonSerialization();
    }

    /**
     * 运行所有序列化测试.
     *
     * @return 所有序列化测试结果列表
     */
    @PostMapping("/all")
    public List<TestResult> runAllSerializationTests() {
        return serializationTestService.runAllSerializationTests();
    }
}
