package cn.com.chinastock.cnf.redis.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.redis.examples.model.TestResult;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

/**
 * Redis 操作测试服务
 *
 * <AUTHOR>
 */
@Service
public class RedisOperationTestService {

    private final RedisTemplate<String, Object> redisTemplate;

    public RedisOperationTestService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 测试 String 操作.
     *
     * @return 测试结果
     */
    public TestResult testStringOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = "test:string:" + System.currentTimeMillis() + ":";

            // SET 操作
            String key = keyPrefix + "simple";
            String valueToSet = "Hello Redis";
            GalaxyLogger.info("testStringOperations, Set: {} = {}", key, valueToSet);
            redisTemplate.opsForValue().set(key, valueToSet);

            // GET 操作
            GalaxyLogger.info("testStringOperations, Get: {}", key);
            String value = (String) redisTemplate.opsForValue().get(key);

            // SETEX 操作（带过期时间）
            GalaxyLogger.info("testStringOperations, SetEX: {} = {} (10 seconds)", keyPrefix + "expire", "Will expire");
            redisTemplate.opsForValue().set(keyPrefix + "expire", "Will expire", 10, TimeUnit.SECONDS);

            // TTL 操作
            GalaxyLogger.info("testStringOperations, getExpire: {}", keyPrefix + "expire");
            Long ttl = redisTemplate.getExpire(keyPrefix + "expire", TimeUnit.SECONDS);

            // INCR 操作
            GalaxyLogger.info("testStringOperations, Set: {} = 0", keyPrefix + "counter");
            redisTemplate.opsForValue().set(keyPrefix + "counter", 0);

            GalaxyLogger.info("testStringOperations, INCR: {}", keyPrefix + "counter");
            Long counter = redisTemplate.opsForValue().increment(keyPrefix + "counter");

            long executionTime = System.currentTimeMillis() - startTime;


            Map<String, Object> data = new HashMap<>();
            data.put("getValue", value);
            data.put("ttl", ttl);
            data.put("counter", counter);

            TestResult result = TestResult.success("TC-008", "String 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            GalaxyLogger.error("String 操作测试失败", e);
            return TestResult.failure("TC-008", "String 操作测试失败", e);
        }
    }

    /**
     * 测试 Hash 操作.
     *
     * @return 测试结果
     */
    public TestResult testHashOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = "test:hash:" + System.currentTimeMillis();

            // HSET 操作
            redisTemplate.opsForHash().put(key, "field1", "value1");
            redisTemplate.opsForHash().put(key, "field2", "value2");

            // HGET 操作
            Object field1Value = redisTemplate.opsForHash().get(key, "field1");

            // HGETALL 操作
            Map<Object, Object> allFields = redisTemplate.opsForHash().entries(key);

            // HDEL 操作
            redisTemplate.opsForHash().delete(key, "field2");

            // 验证删除
            Boolean field2Exists = redisTemplate.opsForHash().hasKey(key, "field2");

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("field1Value", field1Value);
            data.put("allFieldsCount", allFields.size());
            data.put("field2Exists", field2Exists);

            TestResult result = TestResult.success("TC-009", "Hash 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-009", "Hash 操作测试失败", e);
        }
    }

    /**
     * 测试 List 操作.
     *
     * @return 测试结果
     */
    public TestResult testListOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = "test:list:" + System.currentTimeMillis();

            // LPUSH 操作
            redisTemplate.opsForList().leftPush(key, "item1");
            redisTemplate.opsForList().leftPush(key, "item2");

            // RPUSH 操作
            redisTemplate.opsForList().rightPush(key, "item3");

            // LRANGE 操作
            List<Object> allItems = redisTemplate.opsForList().range(key, 0, -1);

            // LPOP 操作
            Object leftPoppedItem = redisTemplate.opsForList().leftPop(key);

            // 获取列表长度
            Long listSize = redisTemplate.opsForList().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("allItems", allItems);
            data.put("leftPoppedItem", leftPoppedItem);
            data.put("finalSize", listSize);

            TestResult result = TestResult.success("TC-010", "List 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-010", "List 操作测试失败", e);
        }
    }

    /**
     * 测试 Set 操作.
     *
     * @return 测试结果
     */
    public TestResult testSetOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = "test:set:" + System.currentTimeMillis();

            // SADD 操作
            redisTemplate.opsForSet().add(key, "member1", "member2", "member3");

            // SMEMBERS 操作
            Set<Object> allMembers = redisTemplate.opsForSet().members(key);

            // SISMEMBER 操作
            Boolean isMember = redisTemplate.opsForSet().isMember(key, "member1");

            // SREM 操作
            redisTemplate.opsForSet().remove(key, "member2");

            // 获取集合大小
            Long setSize = redisTemplate.opsForSet().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("allMembers", allMembers);
            data.put("isMember1", isMember);
            data.put("finalSize", setSize);

            TestResult result = TestResult.success("TC-011", "Set 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-011", "Set 操作测试失败", e);
        }
    }

    /**
     * 测试 ZSet 操作.
     *
     * @return 测试结果
     */
    public TestResult testZSetOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = "test:zset:" + System.currentTimeMillis();

            // ZADD 操作
            redisTemplate.opsForZSet().add(key, "member1", 1.0);
            redisTemplate.opsForZSet().add(key, "member2", 2.0);
            redisTemplate.opsForZSet().add(key, "member3", 3.0);

            // ZRANGE 操作
            Set<Object> rangeMembers = redisTemplate.opsForZSet().range(key, 0, -1);

            // ZSCORE 操作
            Double score = redisTemplate.opsForZSet().score(key, "member2");

            // ZREM 操作
            redisTemplate.opsForZSet().remove(key, "member1");

            // 获取有序集合大小
            Long zsetSize = redisTemplate.opsForZSet().size(key);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("rangeMembers", rangeMembers);
            data.put("member2Score", score);
            data.put("finalSize", zsetSize);

            TestResult result = TestResult.success("TC-012", "ZSet 操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-012", "ZSet 操作测试失败", e);
        }
    }

    /**
     * 测试过期时间操作.
     *
     * @return 测试结果
     */
    public TestResult testExpirationOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String key = "test:expiration:" + System.currentTimeMillis();

            // 设置带过期时间的键
            redisTemplate.opsForValue().set(key, "expiring value", 5, TimeUnit.SECONDS);

            // 检查 TTL
            Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            // 延长过期时间
            redisTemplate.expire(key, 10, TimeUnit.SECONDS);
            Long newTtl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            // 移除过期时间
            redisTemplate.persist(key);
            Long persistTtl = redisTemplate.getExpire(key, TimeUnit.SECONDS);

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = Map.of(
                    "initialTtl", ttl,
                    "extendedTtl", newTtl,
                    "persistTtl", persistTtl
            );

            TestResult result = TestResult.success("TC-EXT-002", "过期时间操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-EXT-002", "过期时间操作测试失败", e);
        }
    }

    /**
     * 测试事务操作.
     *
     * @return 测试结果
     */
    @SuppressWarnings("unchecked")
    public TestResult testTransactionOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = "test:transaction:" + System.currentTimeMillis() + ":";

            // 执行事务
            List<Object> results = redisTemplate.execute((RedisCallback<List<Object>>) connection -> {
                connection.multi();
                for (int i = 1; i <= 2; i++) {
                    String key = keyPrefix + "key" + i;
                    String value = "value" + i;
                    byte[] serializedKey = ((RedisSerializer<String>) redisTemplate.getKeySerializer()).serialize(key);
                    byte[] serializedValue = ((RedisSerializer<String>) redisTemplate.getValueSerializer()).serialize(value);

                    connection.stringCommands().set(serializedKey, serializedValue);
                }
                return connection.exec();
            });

            // 验证事务结果
            String value1 = (String) redisTemplate.opsForValue().get(keyPrefix + "key1");
            String value2 = (String) redisTemplate.opsForValue().get(keyPrefix + "key2");

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = new HashMap<>();
            data.put("transactionResults", results != null ? results.size() : 0);
            data.put("value1", value1);
            data.put("value2", value2);

            TestResult result = TestResult.success("TC-EXT-001", "事务操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            GalaxyLogger.error("事务操作测试失败", e);
            return TestResult.failure("TC-EXT-001", "事务操作测试失败", e);
        }
    }

    /**
     * 测试管道操作.
     *
     * @return 测试结果
     */
    @SuppressWarnings("unchecked")
    public TestResult testPipelineOperations() {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = "test:pipeline:" + System.currentTimeMillis() + ":";

            // 执行管道操作
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (int i = 0; i < 10; i++) {
                    String key = keyPrefix + i;
                    String value = "value" + i;
                    byte[] serializedKey = ((RedisSerializer<String>) redisTemplate.getKeySerializer()).serialize(key);
                    byte[] serializedValue = ((RedisSerializer<String>) redisTemplate.getValueSerializer()).serialize(value);
                    connection.stringCommands().set(serializedKey, serializedValue);
                }
                return null;
            });

            // 验证管道结果
            List<String> values = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                String value = (String) redisTemplate.opsForValue().get(keyPrefix + i);
                values.add(value);
            }

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = Map.of(
                    "pipelineResults", results.size(),
                    "sampleValues", values
            );

            TestResult result = TestResult.success("TC-013", "管道操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            GalaxyLogger.error("管道操作测试失败", e);
            return TestResult.failure("TC-013", "管道操作测试失败", e);
        }
    }

    /**
     * 并发操作测试.
     *
     * @param threadCount         线程数量
     * @param operationsPerThread 每个线程的操作数量
     * @return 测试结果
     */
    public TestResult testConcurrentOperations(int threadCount, int operationsPerThread) {
        try {
            long startTime = System.currentTimeMillis();
            String keyPrefix = "test:concurrent:" + System.currentTimeMillis() + ":";

            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            CountDownLatch latch = new CountDownLatch(threadCount);
            List<Future<Integer>> futures = new ArrayList<>();

            for (int t = 0; t < threadCount; t++) {
                final int threadId = t;
                Future<Integer> future = executor.submit(() -> {
                    try {
                        for (int i = 0; i < operationsPerThread; i++) {
                            String key = keyPrefix + threadId + ":" + i;
                            redisTemplate.opsForValue().set(key, "value" + i);
                            redisTemplate.opsForValue().get(key);
                        }
                        return operationsPerThread;
                    } finally {
                        latch.countDown();
                    }
                });
                futures.add(future);
            }

            // 等待所有线程完成
            latch.await(30, TimeUnit.SECONDS);
            executor.shutdown();

            // 统计结果
            int totalOperations = futures.stream()
                    .mapToInt(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            return 0;
                        }
                    })
                    .sum();

            long executionTime = System.currentTimeMillis() - startTime;

            Map<String, Object> data = Map.of(
                    "threadCount", threadCount,
                    "operationsPerThread", operationsPerThread,
                    "totalOperations", totalOperations,
                    "operationsPerSecond", totalOperations * 1000.0 / executionTime
            );

            TestResult result = TestResult.success("TC-023", "并发操作测试成功", data);
            result.setExecutionTime(executionTime);
            return result;

        } catch (Exception e) {
            return TestResult.failure("TC-023", "并发操作测试失败", e);
        }
    }

    /**
     * 运行所有 Redis 操作测试.
     *
     * @return 所有Redis操作测试结果列表
     */
    public List<TestResult> runAllRedisOperationTests() {
        List<TestResult> results = new ArrayList<>();
        results.add(testStringOperations());     // TC-008
        results.add(testHashOperations());       // TC-009
        results.add(testListOperations());       // TC-010
        results.add(testSetOperations());        // TC-011
        results.add(testZSetOperations());       // TC-012
        results.add(testPipelineOperations());   // TC-013
        return results;
    }
}
