package cn.com.chinastock.cnf.examples.data.controller;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import cn.com.chinastock.cnf.examples.data.service.BlogService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 博客控制器 - MyBatisPlus示例
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/blog")
public class BlogController {

    @Autowired
    private BlogService blogService;

    /**
     * 自动创建博客
     *
     * @return 创建的博客
     */
    @PostMapping("/")
    public ResponseEntity<Blog> autoCreate() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        Blog createdBlog = blogService.createBlog(
                "title-" + timestamp,
                "content-" + timestamp,
                "author-" + timestamp
        );
        return ResponseEntity.ok(createdBlog);
    }

    /**
     * 根据ID获取博客
     *
     * @param id 博客ID
     * @return 博客信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Blog> getBlogById(@PathVariable Long id) {
        Blog blog = blogService.getBlogById(id);
        if (blog != null) {
            return ResponseEntity.ok(blog);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取所有博客
     *
     * @return 所有博客列表
     */
    @GetMapping("/all")
    public ResponseEntity<List<Blog>> getAllBlogs() {
        List<Blog> blogs = blogService.getAllBlogs();
        return ResponseEntity.ok(blogs);
    }

    /**
     * 根据标题关键字查找博客
     *
     * @param title 标题关键字
     * @return 博客列表
     */
    @GetMapping("/search/title")
    public ResponseEntity<List<Blog>> findByTitleContaining(@RequestParam String title) {
        List<Blog> blogs = blogService.findByTitleContaining(title);
        return ResponseEntity.ok(blogs);
    }

    /**
     * 根据作者查找博客
     *
     * @param author 作者
     * @return 博客列表
     */
    @GetMapping("/search/author")
    public ResponseEntity<List<Blog>> findByAuthor(@RequestParam String author) {
        List<Blog> blogs = blogService.findByAuthor(author);
        return ResponseEntity.ok(blogs);
    }

    /**
     * 更新博客
     *
     * @param id   博客ID
     * @param blog 博客信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public ResponseEntity<String> updateBlog(@PathVariable Long id, @RequestBody Blog blog) {
        blog.setId(id);
        boolean success = blogService.updateBlog(blog);
        if (success) {
            return ResponseEntity.ok("Blog updated successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to update blog");
        }
    }

    /**
     * 删除博客
     *
     * @param id 博客ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteBlog(@PathVariable Long id) {
        boolean success = blogService.deleteBlog(id);
        if (success) {
            return ResponseEntity.ok("Blog deleted successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to delete blog");
        }
    }

    /**
     * 根据作者统计博客数量
     *
     * @param author 作者
     * @return 博客数量
     */
    @GetMapping("/count/author")
    public ResponseEntity<Map<String, Object>> countByAuthor(@RequestParam String author) {
        long count = blogService.countByAuthor(author);
        return ResponseEntity.ok(Map.of("author", author, "count", count));
    }

    /**
     * 统计总博客数量
     *
     * @return 总博客数量
     */
    @GetMapping("/count/all")
    public ResponseEntity<Map<String, Object>> countAllBlogs() {
        long count = blogService.countAllBlogs();
        return ResponseEntity.ok(Map.of("totalCount", count));
    }

    /**
     * 统计各作者的博客数量
     *
     * @return 作者博客数量统计
     */
    @GetMapping("/count/group-by-author")
    public ResponseEntity<List<Object>> countBlogsGroupByAuthor() {
        List<Object> stats = blogService.countBlogsGroupByAuthor();
        return ResponseEntity.ok(stats);
    }
}
