package cn.com.chinastock.cnf.examples.data.mapper;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 博客Mapper接口 - MyBatisPlus示例
 * 继承BaseMapper后自动拥有基础的CRUD方法
 *
 * <AUTHOR>
 */
@Mapper
public interface BlogMapper extends BaseMapper<Blog> {

    /**
     * 根据标题关键字查找博客（自定义查询）
     *
     * @param title 标题关键字
     * @return 博客列表
     */
    @Select("SELECT * FROM blog WHERE title LIKE CONCAT('%',#{title},'%')")
    List<Blog> findByTitleContaining(@Param("title") String title);

    /**
     * 根据作者统计博客数量（自定义查询）
     *
     * @param author 作者
     * @return 博客数量
     */
    @Select("SELECT COUNT(*) FROM blog WHERE author = #{author}")
    long countByAuthor(@Param("author") String author);

    /**
     * 统计各作者的博客数量（自定义查询）
     *
     * @return 作者博客数量统计
     */
    @Select("SELECT author, COUNT(*) as count FROM blog GROUP BY author")
    List<Object> countBlogsGroupByAuthor();
}
