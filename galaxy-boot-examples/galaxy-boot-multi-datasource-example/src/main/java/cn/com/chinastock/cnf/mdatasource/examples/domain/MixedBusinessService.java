package cn.com.chinastock.cnf.mdatasource.examples.domain;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity.Order;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.service.OrderService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.service.ProductService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.service.UserService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 混合业务服务类
 * 提供统计和验证功能
 *
 * <AUTHOR>
 */
@Service
public class MixedBusinessService {

    private final UserService userService;
    private final ProductService productService;
    private final OrderService orderService;

    public MixedBusinessService(UserService userService,
                              ProductService productService,
                              OrderService orderService) {
        this.userService = userService;
        this.productService = productService;
        this.orderService = orderService;
    }

    /**
     * 验证数据
     *
     * @return 验证结果映射
     */
    public Map<String, Object> verifyData() {
        GalaxyLogger.info("================== Verifying Mixed Data Sources ==================");

        // 验证各个Domain的数据
        List<User> users = userService.verifyData();
        List<Product> products = productService.verifyData();
        List<Order> orders = orderService.verifyData();

        Map<String, Object> result = new HashMap<>();
        result.put("userCount", users.size());
        result.put("productCount", products.size());
        result.put("orderCount", orders.size());
        result.put("users", users);
        result.put("products", products);
        result.put("orders", orders);

        GalaxyLogger.info("==========================================================");
        return result;
    }



    /**
     * 统计数据
     *
     * @return 统计信息映射
     */
    public Map<String, Long> getStatistics() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("userCount", userService.countUsers());
        stats.put("productCount", productService.countProducts());
        stats.put("orderCount", orderService.countOrders());
        return stats;
    }
}
