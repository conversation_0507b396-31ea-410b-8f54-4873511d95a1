package cn.com.chinastock.cnf.mdatasource.examples.domain.product.mapper;

import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * 产品Mapper接口
 * 用于演示MyBatis数据源的使用
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper {
    
    /**
     * 根据ID查找产品
     * 
     * @param id 产品ID
     * @return 产品信息
     */
    Optional<Product> findById(@Param("id") Long id);
    
    /**
     * 根据名称查找产品
     * 
     * @param name 产品名称
     * @return 产品信息
     */
    Optional<Product> findByName(@Param("name") String name);
    
    /**
     * 查找所有产品
     * 
     * @return 产品列表
     */
    List<Product> findAll();
    
    /**
     * 保存产品
     * 
     * @param product 产品信息
     * @return 影响的行数
     */
    int save(Product product);
    
    /**
     * 更新产品
     * 
     * @param product 产品信息
     * @return 影响的行数
     */
    int update(Product product);
    
    /**
     * 根据ID删除产品
     * 
     * @param id 产品ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 统计产品数量
     * 
     * @return 产品总数
     */
    long count();
}
