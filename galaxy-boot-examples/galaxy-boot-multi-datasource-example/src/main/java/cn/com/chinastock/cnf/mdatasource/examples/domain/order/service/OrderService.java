package cn.com.chinastock.cnf.mdatasource.examples.domain.order.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity.Order;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.mapper.OrderMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 订单服务 - MyBatisPlus示例
 *
 * <AUTHOR>
 */
@Service
public class OrderService extends ServiceImpl<OrderMapper, Order> {

    @Autowired
    @Qualifier("orderDataSource")
    private DataSource orderDataSource;

    /**
     * 初始化订单数据库
     */
    public void initializeDatabase() {
        try {
            GalaxyLogger.info("----> Initializing Order database schema...");
            ScriptUtils.executeSqlScript(orderDataSource.getConnection(),
                    new ClassPathResource("sql/order-schema.sql"));
            GalaxyLogger.info("----> Order database schema initialized successfully.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Failed to initialize Order database schema: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize Order database", e);
        }
    }

    /**
     * 创建订单
     *
     * @param amount 订单金额
     * @param status 订单状态
     * @return 创建的订单
     */
    @Transactional(transactionManager = "orderTransactionManager")
    public Order createOrder(BigDecimal amount, String status) {
        Order order = new Order();
        order.setOrderNo("ORD-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        order.setAmount(amount);
        order.setStatus(status);
        order.setDeleted(0);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        save(order);
        return order;
    }

    /**
     * 根据ID查询订单
     *
     * @param id 订单ID
     * @return 订单信息
     */
    public Order findById(Long id) {
        return findById(id);
    }

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    public Order findByOrderNo(String orderNo) {
        return baseMapper.findByOrderNo(orderNo);
    }

    /**
     * 查询所有订单
     *
     * @return 所有订单列表
     */
    public List<Order> findAll() {
        return baseMapper.selectList(null);
    }

    /**
     * 根据状态查询订单
     *
     * @param status 订单状态
     * @return 指定状态的订单列表
     */
    public List<Order> findByStatus(String status) {
        return baseMapper.findByStatus(status);
    }

    /**
     * 更新订单状态
     *
     * @param id     订单ID
     * @param status 新状态
     */
    @Transactional(transactionManager = "orderTransactionManager")
    public void updateStatus(Long id, String status) {
        Order order = findById(id);
        if (order != null) {
            order.setStatus(status);
            order.setUpdateTime(LocalDateTime.now());
            baseMapper.updateById(order);
        }
    }

    /**
     * 删除订单（逻辑删除）
     *
     * @param id 订单ID
     */
    @Transactional(transactionManager = "orderTransactionManager")
    public void deleteOrder(Long id) {
        deleteOrder(id);
    }

    /**
     * 统计订单数量
     *
     * @return 订单总数
     */
    public long countOrders() {
        return baseMapper.countOrders();
    }

    /**
     * 根据状态统计订单数量
     *
     * @param status 订单状态
     * @return 指定状态的订单数量
     */
    public long countByStatus(String status) {
        return baseMapper.countByStatus(status);
    }

    /**
     * 初始化测试数据
     */
    @Transactional(transactionManager = "orderTransactionManager")
    public void initTestData() {
        // 清空现有数据
        baseMapper.delete(null);

        // 创建测试订单
        createOrder(new BigDecimal("100.00"), "PENDING");
        createOrder(new BigDecimal("200.50"), "PAID");
        createOrder(new BigDecimal("150.75"), "SHIPPED");
        createOrder(new BigDecimal("300.25"), "COMPLETED");
    }

    /**
     * 测试事务回滚
     */
    @Transactional(transactionManager = "orderTransactionManager")
    public void testTransactionRollback() {
        // 创建一个订单
        createOrder(new BigDecimal("999.99"), "TEST");

        // 抛出异常触发回滚
        throw new RuntimeException("测试MyBatisPlus事务回滚");
    }

    /**
     * 验证MyBatisPlus事务回滚
     *
     * @return 回滚验证结果
     */
    public boolean verifyTransactionRollback() {
        long testOrderCount = countByStatus("TEST");
        if (testOrderCount > 0) {
            GalaxyLogger.error("!!!!!! [FAILURE] MyBatisPlus transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] MyBatisPlus transaction rolled back successfully.");
            return true;
        }
    }

    /**
     * 验证订单数据
     *
     * @return 订单列表
     */
    public List<Order> verifyData() {
        List<Order> orders = findAll();
        orders.forEach(o ->
                GalaxyLogger.info("[MyBatisPlus Order] Found Order: ID={}, OrderNo={}, Amount={}, Status={}",
                        o.getId(), o.getOrderNo(), o.getAmount(), o.getStatus())
        );
        return orders;
    }
}
