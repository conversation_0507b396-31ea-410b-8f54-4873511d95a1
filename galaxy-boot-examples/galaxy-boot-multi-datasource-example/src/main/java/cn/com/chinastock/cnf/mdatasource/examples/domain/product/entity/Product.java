package cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity;

import java.math.BigDecimal;

/**
 * 产品实体类 - MyBatis版本
 * 用于演示MyBatis数据源的使用
 *
 * <AUTHOR>
 */
public class Product {
    
    private Long id;
    private String name;
    private BigDecimal price;
    private String description;
    
    public Product() {
    }
    
    public Product(String name, BigDecimal price, String description) {
        this.name = name;
        this.price = price;
        this.description = description;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", description='" + description + '\'' +
                '}';
    }
}
