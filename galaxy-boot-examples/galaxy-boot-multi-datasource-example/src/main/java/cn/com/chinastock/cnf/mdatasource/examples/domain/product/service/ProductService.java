package cn.com.chinastock.cnf.mdatasource.examples.domain.product.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.mapper.ProductMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 产品服务类 - MyBatis实现
 * 处理产品相关的业务逻辑
 *
 * <AUTHOR>
 */
@Service
public class ProductService {

    private final ProductMapper productMapper;

    private final DataSource productDataSource;

    public ProductService(ProductMapper productMapper, @Qualifier("productDataSource") DataSource productDataSource) {
        this.productMapper = productMapper;
        this.productDataSource = productDataSource;
    }

    /**
     * 初始化产品数据库
     */
    public void initializeDatabase() {
        try {
            GalaxyLogger.info("----> Initializing Product database schema...");
            ScriptUtils.executeSqlScript(productDataSource.getConnection(),
                new ClassPathResource("sql/product-schema.sql"));
            GalaxyLogger.info("----> Product database schema initialized successfully.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Failed to initialize Product database schema: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize Product database", e);
        }
    }

    /**
     * 创建产品
     * 
     * @param name 产品名称
     * @param price 产品价格
     * @param description 产品描述
     * @return 创建的产品
     */
    @Transactional(transactionManager = "productTransactionManager")
    public Product createProduct(String name, BigDecimal price, String description) {
        Product product = new Product(name, price, description);
        productMapper.save(product);
        return product;
    }

    /**
     * 根据ID查找产品
     * 
     * @param id 产品ID
     * @return 产品信息
     */
    public Optional<Product> findById(Long id) {
        return productMapper.findById(id);
    }

    /**
     * 根据名称查找产品
     * 
     * @param name 产品名称
     * @return 产品信息
     */
    public Optional<Product> findByName(String name) {
        return productMapper.findByName(name);
    }

    /**
     * 查找所有产品
     * 
     * @return 所有产品列表
     */
    public List<Product> findAll() {
        return productMapper.findAll();
    }

    /**
     * 更新产品信息
     * 
     * @param product 产品信息
     * @return 更新的行数
     */
    @Transactional(transactionManager = "productTransactionManager")
    public int updateProduct(Product product) {
        return productMapper.update(product);
    }

    /**
     * 删除产品
     * 
     * @param id 产品ID
     * @return 删除的行数
     */
    @Transactional(transactionManager = "productTransactionManager")
    public int deleteProduct(Long id) {
        return productMapper.deleteById(id);
    }

    /**
     * 统计产品数量
     * 
     * @return 产品总数
     */
    public long countProducts() {
        return productMapper.count();
    }

    /**
     * 初始化测试数据
     */
    @Transactional(transactionManager = "productTransactionManager")
    public void initTestData() {
        GalaxyLogger.info("----> Writing to Product DataSource (MyBatis)...");
        try {
            Product product = new Product("Gaming Laptop", new BigDecimal("1299.99"), "High-end gaming laptop");
            productMapper.save(product);
            GalaxyLogger.info("----> Product write successful.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Product write failed: {}", e.getMessage());
            throw new RuntimeException("Failed to write product data", e);
        }
    }

    /**
     * 测试MyBatis事务回滚
     */
    @Transactional(transactionManager = "productTransactionManager")
    public void testTransactionRollback() {
        GalaxyLogger.info("----> Testing MyBatis transaction rollback...");
        Product productToRollback = new Product("Rollback Product", new BigDecimal("1.00"), "This should not exist");
        productMapper.save(productToRollback);
        GalaxyLogger.info("----> Product saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger MyBatis rollback");
    }

    /**
     * 验证MyBatis事务回滚
     * 
     * @return 回滚验证结果
     */
    public boolean verifyTransactionRollback() {
        Optional<Product> product = productMapper.findByName("Rollback Product");
        if (product.isPresent()) {
            GalaxyLogger.error("!!!!!! [FAILURE] MyBatis transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] MyBatis transaction rolled back successfully.");
            return true;
        }
    }

    /**
     * 验证产品数据
     * 
     * @return 产品列表
     */
    public List<Product> verifyData() {
        List<Product> products = productMapper.findAll();
        products.forEach(p ->
                GalaxyLogger.info("[MyBatis Product] Found Product: ID={}, Name={}, Price={}",
                    p.getId(), p.getName(), p.getPrice())
        );
        return products;
    }
}
