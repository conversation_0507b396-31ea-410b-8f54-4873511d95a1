package cn.com.chinastock.cnf.mdatasource.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.DataInitializationService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.MixedBusinessService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity.Order;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.service.OrderService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.service.ProductService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.service.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 混合数据源控制器
 * 演示JPA、MyBatis和MyBatisPlus混合使用的API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/test/multidb")
public class MixedDataSourceController {

    private final MixedBusinessService mixedBusinessService;
    private final UserService userService;
    private final ProductService productService;
    private final OrderService orderService;
    private final DataInitializationService dataInitializationService;

    public MixedDataSourceController(MixedBusinessService mixedBusinessService,
                                     UserService userService,
                                     ProductService productService,
                                     OrderService orderService,
                                     DataInitializationService dataInitializationService) {
        this.mixedBusinessService = mixedBusinessService;
        this.userService = userService;
        this.productService = productService;
        this.orderService = orderService;
        this.dataInitializationService = dataInitializationService;
    }

    /**
     * 验证数据
     *
     * @return 验证结果
     */
    @GetMapping("/verify")
    public ResponseEntity<Map<String, Object>> verifyData() {
        try {
            Map<String, Object> result = mixedBusinessService.verifyData();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to verify data", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to verify data: " + e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息响应
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Long>> getStatistics() {
        try {
            Map<String, Long> stats = mixedBusinessService.getStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to get statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查找用户（JPA）
     *
     * @param username 用户名
     * @return 用户信息响应
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<User> findUser(@PathVariable String username) {
        try {
            Optional<User> user = userService.findByUsername(username);
            return user.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find user: {}", username, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查找产品（MyBatis）
     *
     * @param name 产品名称
     * @return 产品信息响应
     */
    @GetMapping("/product/{name}")
    public ResponseEntity<Product> findProduct(@PathVariable String name) {
        try {
            Optional<Product> product = productService.findByName(name);
            return product.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find product: {}", name, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查找订单（MyBatisPlus）
     *
     * @param orderNo 订单号
     * @return 订单信息响应
     */
    @GetMapping("/order/{orderNo}")
    public ResponseEntity<Order> findOrder(@PathVariable String orderNo) {
        try {
            Order order = orderService.findByOrderNo(orderNo);
            return order != null ? ResponseEntity.ok(order) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find order: {}", orderNo, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试JPA事务回滚
     *
     * @return 测试结果响应
     */
    @PostMapping("/jpa-transaction")
    public ResponseEntity<Map<String, Object>> testJpaTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            userService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("JPA transaction failed as expected: {}", e.getMessage());
            boolean rollbackSuccess = userService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess ? "JPA transaction rollback successful" : "JPA transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 测试MyBatis事务回滚
     *
     * @return 测试结果响应
     */
    @PostMapping("/mybatis-transaction")
    public ResponseEntity<Map<String, Object>> testMybatisTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            productService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("MyBatis transaction failed as expected: {}", e.getMessage());
            boolean rollbackSuccess = productService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess ? "MyBatis transaction rollback successful" : "MyBatis transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 测试MyBatisPlus事务回滚
     *
     * @return 测试结果响应
     */
    @PostMapping("/mybatis-plus-transaction")
    public ResponseEntity<Map<String, Object>> testMybatisPlusTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            orderService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("MyBatisPlus transaction failed as expected: {}", e.getMessage());
            boolean rollbackSuccess = orderService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess ? "MyBatisPlus transaction rollback successful" : "MyBatisPlus transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 重新初始化数据
     *
     * @return 初始化结果响应
     */
    @PostMapping("/reinitialize")
    public ResponseEntity<Map<String, Object>> reinitializeData() {
        try {
            dataInitializationService.reinitializeData();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Data reinitialized successfully");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to reinitialize data", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "Failed to reinitialize data: " + e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}
