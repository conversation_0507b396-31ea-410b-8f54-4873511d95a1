package cn.com.chinastock.cnf.mdatasource.examples.domain.user.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户服务类 - JPA实现
 * 处理用户相关的业务逻辑
 *
 * <AUTHOR>
 */
@Service
public class UserService {

    private final UserRepository userRepository;

    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * 创建用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @param age 年龄
     * @return 创建的用户
     */
    @Transactional(transactionManager = "userTransactionManager")
    public User createUser(String username, String email, Integer age) {
        User user = new User(username, email, age);
        return userRepository.save(user);
    }

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据ID查找用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 查找所有用户
     * 
     * @return 所有用户列表
     */
    public List<User> findAll() {
        return userRepository.findAll();
    }

    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 更新后的用户
     */
    @Transactional(transactionManager = "userTransactionManager")
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 删除用户
     * 
     * @param id 用户ID
     */
    @Transactional(transactionManager = "userTransactionManager")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    /**
     * 统计用户数量
     * 
     * @return 用户总数
     */
    public long countUsers() {
        return userRepository.count();
    }

    /**
     * 初始化测试数据
     */
    @Transactional(transactionManager = "userTransactionManager")
    public void initTestData() {
        GalaxyLogger.info("----> Writing to User DataSource (JPA)...");
        try {
            User user = new User("john_doe", "<EMAIL>", 28);
            userRepository.save(user);
            GalaxyLogger.info("----> User write successful.");
        } catch (Exception e) {
            GalaxyLogger.error("----> User write failed: {}", e.getMessage());
            throw new RuntimeException("Failed to write user data", e);
        }
    }

    /**
     * 测试JPA事务回滚
     */
    @Transactional(transactionManager = "userTransactionManager")
    public void testTransactionRollback() {
        GalaxyLogger.info("----> Testing JPA transaction rollback...");
        User userToRollback = new User("rollback_user", "<EMAIL>", 25);
        userRepository.save(userToRollback);
        GalaxyLogger.info("----> User saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger JPA rollback");
    }

    /**
     * 验证JPA事务回滚
     * 
     * @return 回滚验证结果
     */
    public boolean verifyTransactionRollback() {
        Optional<User> user = userRepository.findByUsername("rollback_user");
        if (user.isPresent()) {
            GalaxyLogger.error("!!!!!! [FAILURE] JPA transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] JPA transaction rolled back successfully.");
            return true;
        }
    }

    /**
     * 验证用户数据
     * 
     * @return 用户列表
     */
    public List<User> verifyData() {
        List<User> users = userRepository.findAll();
        users.forEach(u ->
                GalaxyLogger.info("[JPA User] Found User: ID={}, Username={}, Email={}, Age={}",
                    u.getId(), u.getUsername(), u.getEmail(), u.getAge())
        );
        return users;
    }
}
