package cn.com.chinastock.cnf.mdatasource.examples.domain.order.mapper;

import cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity.Order;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订单Mapper - MyBatisPlus示例
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Select("SELECT * FROM `orders` WHERE order_no = #{orderNo} AND deleted = 0")
    Order findByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询指定状态的订单列表
     *
     * @param status 订单状态
     * @return 订单列表
     */
    default List<Order> findByStatus(String status) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }

    /**
     * 统计订单数量
     *
     * @return 订单总数
     */
    default long countOrders() {
        return selectCount(null);
    }

    /**
     * 根据状态统计订单数量
     *
     * @param status 订单状态
     * @return 指定状态的订单数量
     */
    default long countByStatus(String status) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectCount(queryWrapper);
    }
}
