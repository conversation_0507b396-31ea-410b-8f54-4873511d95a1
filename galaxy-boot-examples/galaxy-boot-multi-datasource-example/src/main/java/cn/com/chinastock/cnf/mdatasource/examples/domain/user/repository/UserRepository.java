package cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository;

import cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
} 