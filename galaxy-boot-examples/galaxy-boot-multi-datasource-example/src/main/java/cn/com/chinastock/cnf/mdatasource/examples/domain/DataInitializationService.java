package cn.com.chinastock.cnf.mdatasource.examples.domain;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.examples.domain.order.service.OrderService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.service.ProductService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.service.UserService;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

/**
 * 数据初始化服务
 * 负责初始化所有数据源的数据库结构和测试数据
 *
 * <AUTHOR>
 */
@Service
public class DataInitializationService {

    private final UserService userService;
    private final ProductService productService;
    private final OrderService orderService;

    public DataInitializationService(UserService userService,
                                     ProductService productService,
                                     OrderService orderService) {
        this.userService = userService;
        this.productService = productService;
        this.orderService = orderService;
    }

    /**
     * 应用启动时自动初始化数据
     */
    @PostConstruct
    public void initializeData() {
        initializeDatabases();
        initializeTestData();
    }

    /**
     * 初始化所有数据库结构
     */
    public void initializeDatabases() {
        // JPA数据源由Hibernate自动创建表结构
        // MyBatis数据源需要手动初始化
        productService.initializeDatabase();
        // MyBatisPlus数据源需要手动初始化
        orderService.initializeDatabase();
    }

    /**
     * 初始化所有测试数据
     */
    public void initializeTestData() {
        userService.initTestData();
        productService.initTestData();
        orderService.initTestData();
    }

    /**
     * 重新初始化所有数据
     */
    public void reinitializeData() {
        GalaxyLogger.info("----> Reinitializing all data...");
        initializeTestData();
        GalaxyLogger.info("----> Data reinitialization completed.");
    }
}
