-- 订单表结构 (MyBatisPlus示例)
CREATE TABLE IF NOT EXISTS `orders` (
    id BIGINT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    deleted INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引 (H2兼容语法)
CREATE INDEX IF NOT EXISTS idx_order_status ON `orders`(status);
CREATE INDEX IF NOT EXISTS idx_order_deleted ON `orders`(deleted);
