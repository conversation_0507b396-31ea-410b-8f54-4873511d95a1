server:
  port: 8080

spring:
  galaxy-datasource:
    user:
      primary: true
      type: jpa  # 指定使用JPA
      open-in-view: false # 禁用 OSIV
      # 我们自定义模块所需的注册信息
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity
        repository: cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository
      # 标准的 Spring Boot DataSource 配置
      datasource:
        url: jdbc:h2:mem:userdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      # Hikari 连接池配置
      hikari:
        pool-name: UserPool
        maximum-pool-size: 8
        minimum-idle: 2
        connection-timeout: 20s
        idle-timeout: 5m
        max-lifetime: 15m
        leak-detection-threshold: 60s
      # 标准的 Spring Boot JPA 配置 (将覆盖全局的spring.jpa)
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: create-drop

    product:
      type: mybatis  # 指定使用MyBatis
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity
        mapper: cn.com.chinastock.cnf.mdatasource.examples.domain.product.mapper
      datasource:
        url: jdbc:h2:mem:productdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      # Hikari 连接池配置
      hikari:
        pool-name: ProductPool
        maximum-pool-size: 12
        minimum-idle: 3
        connection-timeout: 25s
        idle-timeout: 8m
        max-lifetime: 20m
        connection-test-query: SELECT 1
      mybatis:
        mapper-locations: classpath:mapper/product/*.xml
        configuration:
          map-underscore-to-camel-case: true

    order:
      type: mybatis-plus  # 指定使用MyBatisPlus
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity
        mapper: cn.com.chinastock.cnf.mdatasource.examples.domain.order.mapper
      datasource:
        url: jdbc:h2:mem:orderdb;DB_CLOSE_DELAY=-1;MODE=MySQL
        username: sa
        password:
        driver-class-name: org.h2.Driver
      # Hikari 连接池配置
      hikari:
        pool-name: OrderPool
        maximum-pool-size: 15
        minimum-idle: 4
        connection-timeout: 30s
        idle-timeout: 10m
        max-lifetime: 25m
        auto-commit: true
        read-only: false
        data-source-properties:
          cachePrepStmts: true
          prepStmtCacheSize: 250
          useServerPrepStmts: true
      mybatis-plus:
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity
        global-config:
          db-config:
            id-type: ASSIGN_ID
            logic-delete-field: deleted

galaxy:
  system:
    code: CNF

  log:
    default-category: APP_LOG
    exception-pretty-print: true

# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.ctrip.framework: OFF
    com.zaxxer.hikari: DEBUG