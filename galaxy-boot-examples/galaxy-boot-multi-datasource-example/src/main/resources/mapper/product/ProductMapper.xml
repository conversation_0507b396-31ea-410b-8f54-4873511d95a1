<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.chinastock.cnf.mdatasource.examples.domain.product.mapper.ProductMapper">

    <!-- 结果映射 -->
    <resultMap id="ProductResultMap" type="cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="price" property="price"/>
        <result column="description" property="description"/>
    </resultMap>

    <!-- 根据ID查找产品 -->
    <select id="findById" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM products
        WHERE id = #{id}
    </select>

    <!-- 根据名称查找产品 -->
    <select id="findByName" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM products
        WHERE name = #{name}
    </select>

    <!-- 查找所有产品 -->
    <select id="findAll" resultMap="ProductResultMap">
        SELECT id, name, price, description
        FROM products
        ORDER BY id
    </select>

    <!-- 保存产品 -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO products (name, price, description)
        VALUES (#{name}, #{price}, #{description})
    </insert>

    <!-- 更新产品 -->
    <update id="update">
        UPDATE products
        SET name = #{name},
            price = #{price},
            description = #{description}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除产品 -->
    <delete id="deleteById">
        DELETE FROM products
        WHERE id = #{id}
    </delete>

    <!-- 统计产品数量 -->
    <select id="count" resultType="long">
        SELECT COUNT(*)
        FROM products
    </select>

</mapper>
