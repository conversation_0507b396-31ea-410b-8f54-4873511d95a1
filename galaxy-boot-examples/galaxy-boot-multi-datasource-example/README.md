# Galaxy Boot Multi Datasource Example

这个示例演示了如何使用 Galaxy Boot Starter Multi Datasource 来配置和管理多个数据源，支持JPA、MyBatis和MyBatisPlus三种ORM框架。

## 功能特性

- **JPA数据源**: 使用Spring Data JPA管理用户数据
- **MyBatis数据源**: 使用MyBatis管理产品数据
- **MyBatisPlus数据源**: 使用MyBatisPlus管理订单数据
- **混合使用**: 在同一个应用中同时使用JPA、MyBatis和MyBatisPlus
- **独立事务管理**: 每个数据源都有独立的事务管理器
- **自动配置**: 通过配置文件自动创建数据源和相关Bean

## 项目结构

```
src/main/java/
├── domain/
│   ├── BusinessService.java           # JPA业务服务
│   ├── MixedBusinessService.java      # 混合业务服务
│   ├── user/                          # 用户模块（JPA）
│   │   ├── entity/User.java
│   │   └── repository/UserRepository.java
│   ├── product/                       # 产品模块
│   │   ├── entity/
│   │   │   ├── Product.java           # JPA实体
│   │   │   └── ProductMybatis.java    # MyBatis实体
│   │   ├── repository/ProductRepository.java  # JPA Repository
│   │   └── mapper/ProductMapper.java  # MyBatis Mapper
│   └── order/                         # 订单模块（MyBatisPlus）
│       ├── entity/Order.java
│       └── mapper/OrderMapper.java
├── controller/
│   ├── TestController.java            # 测试控制器
│   └── MixedDataSourceController.java # 混合数据源控制器
└── MDatabaseExampleApplication.java   # 主启动类

src/main/resources/
├── application.yml                     # 配置文件
├── mapper/product/ProductMapper.xml    # MyBatis映射文件
└── sql/product-schema.sql             # 数据库初始化脚本
```

## 配置说明

### 数据源配置

```yaml
spring:
  galaxy-datasource:
    # JPA数据源配置
    user:
      primary: true
      type: jpa
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity
        repository: cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository
      datasource:
        url: jdbc:h2:mem:userdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: create-drop

    # MyBatis数据源配置
    product:
      type: mybatis
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity
        mapper: cn.com.chinastock.cnf.mdatasource.examples.domain.product.mapper
      datasource:
        url: jdbc:h2:mem:productdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      mybatis:
        mapper-locations: classpath:mapper/product/*.xml
        configuration:
          map-underscore-to-camel-case: true

    # MyBatisPlus数据源配置
    order:
      type: mybatis-plus
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity
        mapper: cn.com.chinastock.cnf.mdatasource.examples.domain.order.mapper
      datasource:
        url: ********************************************************************************************
        username: cnf
        password: userpassword123!
        driver-class-name: com.mysql.cj.jdbc.Driver
      mybatis-plus:
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.examples.domain.order.entity
        global-config:
          db-config:
            id-type: ASSIGN_ID
            logic-delete-field: deleted
```

## 运行示例

### 1. 准备 MySQL 数据库

订单数据源使用 MySQL 数据库，需要先创建数据库和用户：

```bash
# 连接到 MySQL
mysql -u root -p

# 执行初始化脚本
source mysql-setup.sql
```

或者直接执行：
```bash
mysql -u root -p < mysql-setup.sql
```

### 2. 启动应用

```bash
mvn spring-boot:run -pl galaxy-boot-examples/galaxy-boot-multi-datasource-example
```

### 3. 测试API

#### 验证混合数据
```bash
curl http://localhost:8080/api/test/multidb/verify
```

#### 获取统计信息
```bash
curl http://localhost:8080/api/test/multidb/statistics
```

#### 查询特定数据
```bash
# 查询用户（JPA）
curl http://localhost:8080/api/test/multidb/user/john_doe

# 查询产品（MyBatis）
curl http://localhost:8080/api/test/multidb/product/Gaming%20Laptop

# 查询订单（MyBatisPlus）- 需要先运行应用获取实际订单号
curl http://localhost:8080/api/test/multidb/order/ORD-12345678
```

#### 测试事务回滚
```bash
# 测试JPA事务回滚
curl -X POST http://localhost:8080/api/test/multidb/jpa-transaction

# 测试MyBatis事务回滚
curl -X POST http://localhost:8080/api/test/multidb/mybatis-transaction

# 测试MyBatisPlus事务回滚
curl -X POST http://localhost:8080/api/test/multidb/mybatis-plus-transaction
```

#### 重新初始化数据
```bash
curl -X POST http://localhost:8080/api/test/multidb/reinitialize
```
