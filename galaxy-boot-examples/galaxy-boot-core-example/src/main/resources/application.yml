server:
  port: 8088
  compression:
    enabled: true

spring:
  application:
    name: galaxy-boot-core-example-service

  cloud:
    openfeign:
      client:
        config:
          default:
            readTimeout: 1200
            loggerLevel: full
#  security:
#    basic:
#      path: /swagger-ui.html
#      enabled: true
#    user:
#      name: admin
#      password: admin

#springdoc:
#  swagger-ui:
#    path: /swagger-ui.html
#    persistAuthorization: true
#  api-docs:
#    path: /v3/api-docs

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
  tracing:
    sampling:
      probability: 1.0  # 100%采样率，用于演示
  observations:
    key-values:
      application: galaxy-boot-core-example

galaxy:
  swagger:
    auth:
      username: user
      password: password

  feign:
    esb:
      user: user
      password: password

  system:
    code: EXP

  log:
    request-response:
      enabled: true
      request-headers: true
      response-headers: true
      mask-field: true
    performance:
      enabled: true
    default-category: APP_LOG
    exception-pretty-print: true

  fastjson:
    write-map-null-value: true
    write-null-string-as-empty: true

  security:
    user:
      name: user
      password: password
    input-protect: true
    output-protect: true
    actuator:
      protect: true
#      whitelist:
#        - 127.0.0.1
#        - ***********/24    # 表示从 `***********` 到 `*************` 的所有 IP 地址。
#        - 0:0:0:0:0:0:0:1  # 表示 `localhost` 的 IP 地址。
    csrf:
      protect: false
#      whitelist:
#        - /api/**
#        - /swagger-ui/**
#        - /v3/api-docs/**
#      blacklist:
#        - /api/security/csrf/**
    cors:
      protect: true
      whitelist:
        - https://*.chinastock.com.cn

logging:
  level:
    cn.com.chinastock.cnf.examples.client.ESBClient: DEBUG

app:
  id: galaxy-boot
apollo:
  config-service: http://localhost:8080
