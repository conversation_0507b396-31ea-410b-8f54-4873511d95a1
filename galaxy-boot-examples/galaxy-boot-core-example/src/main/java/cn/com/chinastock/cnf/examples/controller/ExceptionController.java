package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.exception.BusinessException;
import cn.com.chinastock.cnf.core.exception.ForbiddenException;
import cn.com.chinastock.cnf.core.exception.ServerErrorException;
import cn.com.chinastock.cnf.core.exception.UnauthorizedException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.examples.dto.ValidateDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/exception")
public class ExceptionController {

    @GetMapping("/business-error")
    public BaseResponse<Object> bizError() {
        throw new BusinessException("EXPBXXXX001", "业务异常");
    }

    @GetMapping("/unauthorized")
    public BaseResponse<Object> unauthorizedError() {
        throw new UnauthorizedException("用户未认证");
    }

    @GetMapping("/forbidden")
    public BaseResponse<Object> forbiddenError() {
        throw new ForbiddenException("用户无权限");
    }

    @GetMapping("/server-error")
    public BaseResponse<Object> serverError() {
        throw new ServerErrorException("服务器异常");
    }

    @GetMapping("/runtime")
    public BaseResponse<Object> runtimeException() {
        throw new RuntimeException("服务器异常");
    }

    @GetMapping("/not-handle-error")
    public BaseResponse<Object> notHandleError() {
        throw new IllegalArgumentException("参数不能为空");
    }

    @GetMapping("/validation")
    public BaseResponse<ValidateDTO> validationError(@Validated @RequestBody ValidateDTO validateDTO) {
        return new BaseResponse<>(new Meta(true, "0", "成功"), validateDTO);
    }
}
