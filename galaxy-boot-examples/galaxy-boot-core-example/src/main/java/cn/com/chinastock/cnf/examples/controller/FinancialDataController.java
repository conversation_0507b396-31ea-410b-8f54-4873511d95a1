package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.examples.entity.FinancialData;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api/financialData")
public class FinancialDataController {
    @GetMapping("/mock")
    public BaseResponse<FinancialData> mock() {
        FinancialData financialData = new FinancialData();
        financialData.setName("mock");
        financialData.setAmount(new BigDecimal("12345678901.23"));
        financialData.setTransientField("transient");
        financialData.setNullableField(null);
        return new BaseResponse<>(new Meta(true, "0000", "成功"), financialData);
    }

    @GetMapping("/null")
    public BaseResponse<FinancialData> nullResponse() {
        return new BaseResponse<>(new Meta(true, "0000", "失败"), null);
    }

    @GetMapping("/")
    public FinancialData normal() {
        FinancialData financialData = new FinancialData();
        financialData.setName("normal");
        financialData.setAmount(new BigDecimal("12345678901.23"));
        financialData.setTransientField("transient");
        financialData.setNullableField(null);
        return financialData;
    }

    @PostMapping("/mock")
    public FinancialData mock(@RequestBody FinancialData financialData) {
        return financialData;
    }


}
