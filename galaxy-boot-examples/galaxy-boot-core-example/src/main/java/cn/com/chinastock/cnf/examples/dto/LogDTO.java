package cn.com.chinastock.cnf.examples.dto;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;

public class LogDTO {

    @MaskedField
    private String maskData;

    private String data;

    public LogDTO() {
    }

    public LogDTO(String data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LogDTO{" +
                "data='" + data + '\'' +
                '}';
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getMaskData() {
        return maskData;
    }

    public void setMaskData(String maskData) {
        this.maskData = maskData;
    }
}
