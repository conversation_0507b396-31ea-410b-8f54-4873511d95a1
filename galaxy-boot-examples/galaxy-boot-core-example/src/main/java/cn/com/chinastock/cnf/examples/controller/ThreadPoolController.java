package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.BatchMessageResponse;
import cn.com.chinastock.cnf.examples.dto.MessageRequest;
import cn.com.chinastock.cnf.examples.dto.MessageResult;
import cn.com.chinastock.cnf.examples.service.MessageProcessingService;
import io.micrometer.context.ContextExecutorService;
import jakarta.annotation.PreDestroy;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 线程池控制器 - 用于测试多线程环境下的链路追踪
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/threadpool")
public class ThreadPoolController {

    @Autowired
    private MessageProcessingService messageProcessingService;

    // 创建线程池，使用ContextExecutorService包装以支持上下文传播
    private final ExecutorService executorService;

    public ThreadPoolController() {
        // 创建基础线程池
        ThreadPoolExecutor baseExecutor = new ThreadPoolExecutor(
                5,  // 核心线程数
                10, // 最大线程数
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(100), // 任务队列
                new ThreadFactory() {
                    private int counter = 0;

                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, "message-processor-" + (++counter));
                    }
                }
        );

        // 使用ContextExecutorService包装，支持上下文传播
        this.executorService = ContextExecutorService.wrap(baseExecutor);
    }

    /**
     * 批量处理消息API
     *
     * @param request 消息处理请求
     * @return 处理结果
     */
    @PostMapping("/process-messages")
    public ResponseEntity<BatchMessageResponse> processMessages(@RequestBody MessageRequest request) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "开始批量处理消息, 消息数量: {}, 延迟: {}ms",
                request.getMessages() != null ? request.getMessages().size() : 0,
                request.getDelayMs());

        List<CompletableFuture<MessageResult>> futures = new ArrayList<>();

        // 为每个消息创建异步任务
        for (String message : request.getMessages()) {
            CompletableFuture<MessageResult> future = CompletableFuture.supplyAsync(() -> {
                // 在子线程中处理消息
                return processMessageWithTracing(message, request.getDelayMs());
            }, executorService);

            futures.add(future);
        }

        return ResponseEntity.ok(getBatchMessageResponse(request, futures));
    }

    @NotNull
    private static BatchMessageResponse getBatchMessageResponse(MessageRequest request, List<CompletableFuture<MessageResult>> futures) {
        // 等待所有任务完成
        List<MessageResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 等待所有任务完成，设置超时时间
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 等待最多5秒
            allFutures.get(5, TimeUnit.SECONDS);

            // 收集结果
            for (CompletableFuture<MessageResult> future : futures) {
                try {
                    MessageResult result = future.get();
                    results.add(result);

                    if ("SUCCESS".equals(result.getStatus())) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "获取任务结果失败", e);
                    failureCount++;
                }
            }

        } catch (TimeoutException e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "批量处理超时", e);
            failureCount = request.getMessages().size() - successCount;
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "批量处理异常", e);
            failureCount = request.getMessages().size() - successCount;
        }


        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "批量处理完成, 总数: {}, 成功: {}, 失败: {}",
                request.getMessages().size(), successCount, failureCount);

        BatchMessageResponse response = new BatchMessageResponse(
                request.getMessages().size(),
                successCount,
                failureCount,
                results
        );
        return response;
    }

    /**
     * 在子线程中处理消息，确保TraceContext正确传播
     *
     * @param message 消息内容
     * @param delayMs 处理延迟时间
     * @return 消息处理结果
     */
    private MessageResult processMessageWithTracing(String message, Long delayMs) {
        // 创建子Span用于追踪
//        if (tracer != null) {
//            Span childSpan = tracer.nextSpan()
//                .name("message-processing")
//                .tag("message", message)
//                .start();
//
//            try (Tracer.SpanInScope ws = tracer.withSpan(childSpan)) {
//                return messageProcessingService.processMessage(message, delayMs);
//            } finally {
//                childSpan.end();
//            }
//        } else {
        // 如果没有Tracer，直接处理
        return messageProcessingService.processMessage(message, delayMs);
//        }
    }

    /**
     * 销毁时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "关闭消息处理线程池");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 获取线程池状态信息
     *
     * @return 线程池状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<String> getThreadPoolStatus() {
        if (executorService instanceof ContextExecutorService) {
            // 尝试获取底层的ThreadPoolExecutor信息
            return ResponseEntity.ok("ThreadPool is running with context propagation support");
        }
        return ResponseEntity.ok("ThreadPool status: " + executorService.toString());
    }
}
