package cn.com.chinastock.cnf.examples.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 线程池配置属性
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "galaxy.threadpool")
public class ThreadPoolProperties {

    /**
     * 消息处理线程池配置
     */
    private MessageProcessor messageProcessor = new MessageProcessor();

    /**
     * 异步任务线程池配置
     */
    private AsyncTask asyncTask = new AsyncTask();

    public MessageProcessor getMessageProcessor() {
        return messageProcessor;
    }

    public void setMessageProcessor(MessageProcessor messageProcessor) {
        this.messageProcessor = messageProcessor;
    }

    public AsyncTask getAsyncTask() {
        return asyncTask;
    }

    public void setAsyncTask(AsyncTask asyncTask) {
        this.asyncTask = asyncTask;
    }

    /**
     * 消息处理线程池配置
     */
    public static class MessageProcessor {
        /**
         * 核心线程数
         */
        private int corePoolSize = 5;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = 10;

        /**
         * 线程空闲时间（秒）
         */
        private long keepAliveTime = 60L;

        /**
         * 队列容量
         */
        private int queueCapacity = 100;

        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "message-processor-";

        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = true;

        public int getCorePoolSize() {
            return corePoolSize;
        }

        public void setCorePoolSize(int corePoolSize) {
            this.corePoolSize = corePoolSize;
        }

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public void setMaximumPoolSize(int maximumPoolSize) {
            this.maximumPoolSize = maximumPoolSize;
        }

        public long getKeepAliveTime() {
            return keepAliveTime;
        }

        public void setKeepAliveTime(long keepAliveTime) {
            this.keepAliveTime = keepAliveTime;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }

        public String getThreadNamePrefix() {
            return threadNamePrefix;
        }

        public void setThreadNamePrefix(String threadNamePrefix) {
            this.threadNamePrefix = threadNamePrefix;
        }

        public boolean isAllowCoreThreadTimeOut() {
            return allowCoreThreadTimeOut;
        }

        public void setAllowCoreThreadTimeOut(boolean allowCoreThreadTimeOut) {
            this.allowCoreThreadTimeOut = allowCoreThreadTimeOut;
        }
    }

    /**
     * 异步任务线程池配置
     */
    public static class AsyncTask {
        /**
         * 核心线程数
         */
        private int corePoolSize = 3;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = 8;

        /**
         * 线程空闲时间（秒）
         */
        private long keepAliveTime = 60L;

        /**
         * 队列容量
         */
        private int queueCapacity = 50;

        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "async-task-";

        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = true;

        public int getCorePoolSize() {
            return corePoolSize;
        }

        public void setCorePoolSize(int corePoolSize) {
            this.corePoolSize = corePoolSize;
        }

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public void setMaximumPoolSize(int maximumPoolSize) {
            this.maximumPoolSize = maximumPoolSize;
        }

        public long getKeepAliveTime() {
            return keepAliveTime;
        }

        public void setKeepAliveTime(long keepAliveTime) {
            this.keepAliveTime = keepAliveTime;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }

        public String getThreadNamePrefix() {
            return threadNamePrefix;
        }

        public void setThreadNamePrefix(String threadNamePrefix) {
            this.threadNamePrefix = threadNamePrefix;
        }

        public boolean isAllowCoreThreadTimeOut() {
            return allowCoreThreadTimeOut;
        }

        public void setAllowCoreThreadTimeOut(boolean allowCoreThreadTimeOut) {
            this.allowCoreThreadTimeOut = allowCoreThreadTimeOut;
        }
    }
}
