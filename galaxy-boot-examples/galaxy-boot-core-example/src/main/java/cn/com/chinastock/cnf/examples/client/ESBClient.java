package cn.com.chinastock.cnf.examples.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "esb-service", url = "http://localhost:8088")
public interface ESBClient {

    @GetMapping(value = "/api/test/esb/headers", headers = {"Function-No=1234567890"})
    String headers();

    @GetMapping("/api/test/esb/timeout")
    String timeout();
}
