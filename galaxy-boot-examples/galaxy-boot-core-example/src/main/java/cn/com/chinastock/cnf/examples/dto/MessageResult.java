package cn.com.chinastock.cnf.examples.dto;

/**
 * 消息处理结果DTO
 *
 * <AUTHOR>
 */
public class MessageResult {
    
    /**
     * 原始消息
     */
    private String originalMessage;
    
    /**
     * 处理后的消息
     */
    private String processedMessage;
    
    /**
     * 处理状态
     */
    private String status;

    public MessageResult() {
    }

    public MessageResult(String originalMessage, String processedMessage, String status) {
        this.originalMessage = originalMessage;
        this.processedMessage = processedMessage;
        this.status = status;
    }

    public String getOriginalMessage() {
        return originalMessage;
    }

    public void setOriginalMessage(String originalMessage) {
        this.originalMessage = originalMessage;
    }

    public String getProcessedMessage() {
        return processedMessage;
    }

    public void setProcessedMessage(String processedMessage) {
        this.processedMessage = processedMessage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "MessageResult{" +
                "originalMessage='" + originalMessage + '\'' +
                ", processedMessage='" + processedMessage + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
