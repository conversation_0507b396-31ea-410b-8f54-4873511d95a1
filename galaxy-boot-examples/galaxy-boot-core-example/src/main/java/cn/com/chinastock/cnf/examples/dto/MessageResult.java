package cn.com.chinastock.cnf.examples.dto;

/**
 * 消息处理结果DTO
 *
 * <AUTHOR>
 */
public class MessageResult {
    
    /**
     * 原始消息
     */
    private String originalMessage;
    
    /**
     * 处理后的消息
     */
    private String processedMessage;
    
    /**
     * 处理状态
     */
    private String status;
    
    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;
    
    /**
     * 处理线程名称
     */
    private String threadName;
    
    /**
     * TraceId
     */
    private String traceId;
    
    /**
     * SpanId
     */
    private String spanId;

    public MessageResult() {
    }

    public MessageResult(String originalMessage, String processedMessage, String status, 
                        Long processingTime, String threadName, String traceId, String spanId) {
        this.originalMessage = originalMessage;
        this.processedMessage = processedMessage;
        this.status = status;
        this.processingTime = processingTime;
        this.threadName = threadName;
        this.traceId = traceId;
        this.spanId = spanId;
    }

    public String getOriginalMessage() {
        return originalMessage;
    }

    public void setOriginalMessage(String originalMessage) {
        this.originalMessage = originalMessage;
    }

    public String getProcessedMessage() {
        return processedMessage;
    }

    public void setProcessedMessage(String processedMessage) {
        this.processedMessage = processedMessage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getThreadName() {
        return threadName;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    @Override
    public String toString() {
        return "MessageResult{" +
                "originalMessage='" + originalMessage + '\'' +
                ", processedMessage='" + processedMessage + '\'' +
                ", status='" + status + '\'' +
                ", processingTime=" + processingTime +
                ", threadName='" + threadName + '\'' +
                ", traceId='" + traceId + '\'' +
                ", spanId='" + spanId + '\'' +
                '}';
    }
}
