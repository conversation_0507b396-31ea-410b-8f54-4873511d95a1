package cn.com.chinastock.cnf.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.MessageResult;
import org.springframework.stereotype.Service;

/**
 * 消息处理服务
 *
 * <AUTHOR>
 */
@Service
public class MessageProcessingService {

    /**
     * 处理单个消息
     *
     * @param message 消息内容
     * @param delayMs 处理延迟时间
     * @return 处理结果
     */
    public MessageResult processMessage(String message, Long delayMs) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "开始处理消息: {}", message);

        try {
            // 模拟处理时间
            if (delayMs != null && delayMs > 0) {
                Thread.sleep(delayMs);
            }

            // 模拟消息处理逻辑
            String processedMessage = "PROCESSED_" + message.toUpperCase() + "_" + System.currentTimeMillis();
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "消息处理完成: {} -> {}", message, processedMessage);
            return new MessageResult(message, processedMessage, "SUCCESS");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "消息处理被中断: {}", message);
            return new MessageResult(message, null, "INTERRUPTED");
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "消息处理失败: {}", message);
            return new MessageResult(message, null, "FAILED");
        }
    }

}
