package cn.com.chinastock.cnf.examples.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/esb")
public class ESBController {

    @GetMapping("/headers")
    public String headers() {
        return "headers test";
    }


    @GetMapping("/timeout")
    public String timeout() throws InterruptedException {
        Thread.sleep(5000);
        return "timeout test";
    }
}
