package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.IdDTO;
import cn.com.chinastock.cnf.utils.id.ID_LEN;
import cn.com.chinastock.cnf.utils.id.IdUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/id")
public class IdController {

    @PostMapping(value = "", params = {"length"})
    public ResponseEntity<IdDTO> generateId(@RequestParam(value = "length", required = false) int length) {
        GalaxyLogger.debug(LogCategory.BUSINESS_LOG, "请求ID长度: {}", length);
        ID_LEN idLen = ID_LEN.getLen(length);
        if (idLen == null) {
            GalaxyLogger.error(LogCategory.BUSINESS_LOG, "请求ID长度不支持, length={}, 默认生成8位ID", length);
            idLen = ID_LEN.LEN8;
        }
        String id = IdUtils.getId(idLen);
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "生成8位ID: {}", id);

        return ResponseEntity.ok(new IdDTO(id));
    }

    @PostMapping(value = "/refresh", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<IdDTO> createId(@RequestBody IdDTO idDTO) {
        try {
            ID_LEN idLen = ID_LEN.getLen(idDTO.getId().length());
            if (idLen == null) {
                GalaxyLogger.error(LogCategory.BUSINESS_LOG, "请求ID长度不支持, id={}, length={}", idDTO.getId(), idDTO.getId().length());
                return ResponseEntity.badRequest().body(null);
            }

            String id = IdUtils.getId(idLen);
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "原ID: {}, 生成8位新ID: {}", idDTO.getId(), id);
            idDTO.setId(id);

            return ResponseEntity.ok(idDTO);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "请求参数异常", e);
            return ResponseEntity.badRequest().body(null);
        }
    }
}
