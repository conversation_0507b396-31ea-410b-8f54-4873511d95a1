package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.examples.entity.FinancialData;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api/security")
public class SecurityController {
    @PostMapping("/test")
    public ResponseEntity<String> test(@RequestBody(required = false) String content) {
        return ResponseEntity.ok(content);
    }

    @PostMapping("/csrf/disable")
    public ResponseEntity<String> apiTest(@RequestBody(required = false) String content) {
        return ResponseEntity.ok(content);
    }

    @GetMapping("/unsafe-response")
    public FinancialData unsafeResponse() {
        return mock();
    }

    @GetMapping("/safe-response")
    public ResponseEntity<String> safeResponse() {
        return ResponseEntity.ok("{\"content\":\"Hello World\"}");
    }

    public FinancialData mock() {
        FinancialData financialData = new FinancialData();
        financialData.setName("<script>alert('xss')</script>");
        financialData.setAmount(new BigDecimal("12345678901.23"));
        financialData.setTransientField("transient");
        return financialData;
    }
}
