package cn.com.chinastock.cnf.examples.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import io.micrometer.context.ContextExecutorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * 线程池配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Autowired
    private ThreadPoolProperties threadPoolProperties;

    /**
     * 消息处理线程池配置
     */
    @Bean(name = "messageProcessorExecutor")
    public ExecutorService messageProcessorExecutor() {
        ThreadPoolProperties.MessageProcessor config = threadPoolProperties.getMessageProcessor();

        // 创建基础线程池
        ThreadPoolExecutor baseExecutor = new ThreadPoolExecutor(
            config.getCorePoolSize(),
            config.getMaximumPoolSize(),
            config.getKeepAliveTime(), TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(config.getQueueCapacity()),
            new ConfigurableThreadFactory(config.getThreadNamePrefix()),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 设置核心线程超时
        baseExecutor.allowCoreThreadTimeOut(config.isAllowCoreThreadTimeOut());

        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG,
            "创建消息处理线程池: coreSize={}, maxSize={}, queueCapacity={}, keepAliveTime={}s",
            config.getCorePoolSize(), config.getMaximumPoolSize(),
            config.getQueueCapacity(), config.getKeepAliveTime());

        // 使用ContextExecutorService包装，支持上下文传播
        return ContextExecutorService.wrap(baseExecutor);
    }

    /**
     * 通用异步任务线程池配置
     */
    @Bean(name = "asyncTaskExecutor")
    public ExecutorService asyncTaskExecutor() {
        ThreadPoolProperties.AsyncTask config = threadPoolProperties.getAsyncTask();

        // 创建基础线程池
        ThreadPoolExecutor baseExecutor = new ThreadPoolExecutor(
            config.getCorePoolSize(),
            config.getMaximumPoolSize(),
            config.getKeepAliveTime(), TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(config.getQueueCapacity()),
            new ConfigurableThreadFactory(config.getThreadNamePrefix()),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 设置核心线程超时
        baseExecutor.allowCoreThreadTimeOut(config.isAllowCoreThreadTimeOut());

        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG,
            "创建异步任务线程池: coreSize={}, maxSize={}, queueCapacity={}, keepAliveTime={}s",
            config.getCorePoolSize(), config.getMaximumPoolSize(),
            config.getQueueCapacity(), config.getKeepAliveTime());

        // 使用ContextExecutorService包装，支持上下文传播
        return ContextExecutorService.wrap(baseExecutor);
    }

    /**
     * 可配置的线程工厂
     */
    private static class ConfigurableThreadFactory implements ThreadFactory {
        private int counter = 0;
        private final String namePrefix;

        public ConfigurableThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + (++counter));
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }

    /**
     * 线程池管理器，用于统一管理和关闭线程池
     */
    @Configuration
    public static class ThreadPoolManager {

        private final ExecutorService messageProcessorExecutor;
        private final ExecutorService asyncTaskExecutor;

        public ThreadPoolManager(ExecutorService messageProcessorExecutor, 
                                ExecutorService asyncTaskExecutor) {
            this.messageProcessorExecutor = messageProcessorExecutor;
            this.asyncTaskExecutor = asyncTaskExecutor;
        }

        /**
         * 应用关闭时优雅关闭线程池
         */
        @PreDestroy
        public void shutdown() {
            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "开始关闭线程池...");
            
            shutdownExecutor("messageProcessorExecutor", messageProcessorExecutor);
            shutdownExecutor("asyncTaskExecutor", asyncTaskExecutor);
            
            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "所有线程池已关闭");
        }

        /**
         * 优雅关闭单个线程池
         *
         * @param name 线程池名称
         * @param executor 线程池实例
         */
        private void shutdownExecutor(String name, ExecutorService executor) {
            if (executor != null && !executor.isShutdown()) {
                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "关闭线程池: {}", name);
                
                executor.shutdown();
                try {
                    // 等待10秒让任务完成
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG, 
                            "线程池 {} 在10秒内未能正常关闭，强制关闭", name);
                        executor.shutdownNow();
                        
                        // 再等待5秒
                        if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, 
                                "线程池 {} 强制关闭失败", name);
                        }
                    } else {
                        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, 
                            "线程池 {} 已正常关闭", name);
                    }
                } catch (InterruptedException e) {
                    GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG, 
                        "关闭线程池 {} 时被中断", name, e);
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
