package cn.com.chinastock.cnf.examples.dto;

public class IdDTO {

    private String id;

    public IdDTO(String id) {
        this.id = id;
    }

    // 默认构造函数，Jackson反序列化需要
    public IdDTO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "IdDTO{" +
                "id='" + id + '\'' +
                '}';
    }
}
