# Galaxy Boot Kafka Example

这是一个基于Galaxy Boot Starter Kafka的完整示例项目，演示了Kafka消息的生产和消费的各种模式。

## 项目结构

```
galaxy-boot-kafka-example/
├── src/main/java/cn/com/chinastock/cnf/kafka/examples/
│   ├── KafkaExampleApplication.java          # 主应用类
│   ├── controller/
│   │   └── KafkaController.java              # REST API控制器
│   ├── service/
│   │   └── KafkaProducerService.java         # 消息生产者服务
│   ├── consumer/
│   │   ├── SingleMessageConsumer.java       # 单消息消费者
│   │   ├── BatchMessageConsumer.java        # 批量消息消费者
│   │   └── MixedModeConsumer.java           # 混合模式消费者
│   └── dto/
│       ├── MessageRequest.java              # 单消息请求DTO
│       └── BatchMessageRequest.java         # 批量消息请求DTO
└── src/main/resources/
    └── application.yml                       # 应用配置
```

## 功能特性

### Producer功能
- 单个消息发送（String key）
- 单个消息发送（Integer key）
- 批量消息发送
- 指定分区发送
- 异步发送回调处理

### Consumer功能
- 单个消息消费
- 批量消息消费
- 手动确认模式
- 多主题消费
- 指定分区消费
- 指定偏移量消费
- 消息过滤
- 错误处理

## 使用说明

### 1. 启动应用

```bash
mvn spring-boot:run
```

应用将在端口8089启动。

### 2. 发送消息API

#### 发送单个消息
```bash
curl -X POST http://localhost:8089/api/kafka/send \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "single-message-topic",
    "key": "test-key-1",
    "message": "Hello Kafka!"
  }'
```

#### 快速发送消息到指定topic
```bash
curl -X POST "http://localhost:8089/api/kafka/quick-send/single-message-topic?message=Quick%20Test%20Message"
```

#### 发送消息到指定分区
```bash
curl -X POST "http://localhost:8089/api/kafka/send-to-partition?topic=single-message-topic&partition=0&key=partition-key&message=Partition%20Message"
```

#### 发送消息（Integer key）
```bash
curl -X POST "http://localhost:8089/api/kafka/send-with-int-key?topic=single-message-topic&key=123&message=Integer%20Key%20Message"
```

#### 批量发送消息
```bash
curl -X POST http://localhost:8089/api/kafka/send-batch \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "batch-message-topic",
    "messages": [
      {"key": "batch-key-1", "message": "Batch message 1"},
      {"key": "batch-key-2", "message": "Batch message 2"},
      {"key": "batch-key-3", "message": "Batch message 3"}
    ]
  }'
```

#### 单个消息消费模式专用API

| API路径 | 对应Consumer方法 | Topic | 说明 |
|---------|-----------------|-------|------|
| `/api/kafka/send-single-basic` | `consumeSingleMessage()` | `single-message-topic` | 基础单消息消费 |
| `/api/kafka/send-single-metadata` | `consumeSingleMessageWithMetadata()` | `single-message-with-metadata-topic` | 带元数据单消息消费 |
| `/api/kafka/send-single-headers` | `consumeSingleMessageWithHeaders()` | `single-message-with-headers-topic` | 带Header单消息消费 |
| `/api/kafka/send-single-manual-ack` | `consumeSingleMessageWithManualAck()` | `single-message-manual-ack-topic` | 手动确认单消息消费 |
| `/api/kafka/send-single-error` | `consumeSingleMessageWithErrorHandling()` | `single-message-error-topic` | 错误处理单消息消费 |

#### 批量消息消费模式专用API

| API路径 | 对应Consumer方法 | Topic | 说明 |
|---------|-----------------|-------|------|
| `/api/kafka/batch/send-batch-basic` | `consumeBatchMessages()` | `batch-message-topic` | 基础批量消费 |
| `/api/kafka/batch/send-batch-metadata` | `consumeBatchMessagesWithMetadata()` | `batch-message-with-metadata-topic` | 带元数据批量消费 |
| `/api/kafka/batch/send-high-throughput` | `consumeHighThroughputMessages()` | `high-throughput-topic` | 高性能批量消费 |
| `/api/kafka/batch/quick-send/{type}` | 对应批量消费者 | 根据type确定 | 快速测试批量消费 |
| `/api/kafka/batch/generate-and-send/{type}` | 对应批量消费者 | 根据type确定 | 生成并发送批量消息 |

### 3. 消费模式演示

#### 单个消息消费模式

| Topic | Consumer Group | 消费模式 | 说明 |
|-------|----------------|----------|------|
| `single-message-topic` | `single-message-group` | 基础单消息 | 最简单的消费模式 |
| `single-message-with-metadata-topic` | `single-metadata-group` | 带元数据 | 获取消息的详细元数据 |
| `single-message-with-headers-topic` | `single-headers-group` | 带Header | 使用@Header注解获取消息信息 |
| `single-message-manual-ack-topic` | `single-manual-ack-group` | 手动确认 | 手动控制消息确认 |
| `single-message-error-topic` | `single-error-group` | 错误处理 | 演示错误处理机制 |

#### 批量消息消费模式

| Topic | Consumer Group | 消费模式 | 说明 |
|-------|----------------|----------|------|
| `batch-message-topic` | `batch-message-group` | 基础批量 | 批量处理消息 |
| `batch-message-with-metadata-topic` | `batch-metadata-group` | 批量带元数据 | 批量处理带元数据的消息 |
| `batch-message-with-headers-topic` | `batch-headers-group` | 批量带Header | 批量处理带Header的消息 |
| `batch-message-manual-ack-topic` | `batch-manual-ack-group` | 批量手动确认 | 批量手动确认消息 |
| `batch-message-error-topic` | `batch-error-group` | 批量错误处理 | 批量消息错误处理 |
| `high-throughput-topic` | `high-throughput-group` | 高性能批量 | 高吞吐量批量处理 |

#### 混合消费模式

| Topic | Consumer Group | 消费模式 | 说明 |
|-------|----------------|----------|------|
| `order-topic`, `payment-topic`, `notification-topic` | `multi-topic-group` | 多主题 | 同时监听多个主题 |
| `partition-specific-topic` | `partition-specific-group` | 指定分区 | 只消费特定分区 |
| `offset-specific-topic` | `offset-specific-group` | 指定偏移量 | 从特定偏移量开始消费 |
| `filtered-topic` | `filtered-group` | 消息过滤 | 根据条件过滤消息 |
| `batch-order-topic`, `batch-payment-topic` | `batch-multi-topic-group` | 批量多主题 | 批量处理多主题消息 |
| `dynamic-topic-*` | `dynamic-topic-group` | 动态主题 | 动态确定监听主题 |

### 4. 测试不同消费模式

#### 测试单个消息消费（使用专用API）

##### 基础单消息消费
```bash
# 发送到单个消息基础消费模式
curl -X POST "http://localhost:8089/api/kafka/send-single-basic?message=Basic%20Single%20Message"
```

##### 带元数据单消息消费
```bash
# 发送到单个消息带元数据消费模式
curl -X POST "http://localhost:8089/api/kafka/send-single-metadata?message=Metadata%20Message&key=custom-key"
```

##### 带Header单消息消费
```bash
# 发送到单个消息带Header消费模式
curl -X POST "http://localhost:8089/api/kafka/send-single-headers?message=Headers%20Message&key=header-key"
```

##### 手动确认单消息消费
```bash
# 发送到单个消息手动确认消费模式
curl -X POST "http://localhost:8089/api/kafka/send-single-manual-ack?message=Manual%20Ack%20Message"
```

##### 错误处理单消息消费
```bash
# 发送到单个消息错误处理消费模式（包含"error"触发错误处理）
curl -X POST "http://localhost:8089/api/kafka/send-single-error?message=This%20is%20an%20error%20message"

# 发送正常消息
curl -X POST "http://localhost:8089/api/kafka/send-single-error?message=Normal%20message%20for%20error%20handler"
```

#### 测试批量消息消费（使用专用API）

##### 基础批量消费
```bash
# 发送多条消息到批量基础消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/send-batch-basic" \
  -H "Content-Type: application/json" \
  -d '["Batch message 1", "Batch message 2", "Batch message 3"]'
```

##### 带元数据批量消费
```bash
# 发送多条消息到批量带元数据消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/send-batch-metadata" \
  -H "Content-Type: application/json" \
  -d '["Metadata batch 1", "Metadata batch 2", "Metadata batch 3"]'
```

##### 高性能批量消费
```bash
# 发送多条消息到高性能批量消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/send-high-throughput" \
  -H "Content-Type: application/json" \
  -d '["High throughput 1", "High throughput 2", "High throughput 3", "High throughput 4", "High throughput 5"]'
```

##### 快速测试批量消费
```bash
# 快速发送单条消息到不同批量消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/quick-send/basic?message=Quick%20batch%20basic"
curl -X POST "http://localhost:8089/api/kafka/batch/quick-send/metadata?message=Quick%20batch%20metadata"
curl -X POST "http://localhost:8089/api/kafka/batch/quick-send/throughput?message=Quick%20batch%20throughput"
```

##### 自动生成批量消息
```bash
# 生成并发送10条消息到基础批量消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/generate-and-send/basic?count=10&messagePrefix=Generated%20basic"

# 生成并发送20条消息到高性能批量消费模式
curl -X POST "http://localhost:8089/api/kafka/batch/generate-and-send/throughput?count=20&messagePrefix=Generated%20throughput"
```

#### 测试单个消息消费（使用通用API）
```bash
# 发送到单消息topic
curl -X POST "http://localhost:8089/api/kafka/quick-send/single-message-topic?message=Single%20Message%20Test"

# 发送到带元数据的topic
curl -X POST "http://localhost:8089/api/kafka/quick-send/single-message-with-metadata-topic?message=Metadata%20Test"

# 发送到错误处理topic（包含"error"触发错误处理）
curl -X POST "http://localhost:8089/api/kafka/quick-send/single-message-error-topic?message=This%20is%20an%20error%20message"
```

#### 测试批量消息消费（使用通用API）
```bash
# 发送多条消息到批量topic
for i in {1..5}; do
  curl -X POST "http://localhost:8089/api/kafka/quick-send/batch-message-topic?message=Batch%20Message%20$i"
done

# 发送到高吞吐量topic
for i in {1..20}; do
  curl -X POST "http://localhost:8089/api/kafka/quick-send/high-throughput-topic?message=High%20Throughput%20Message%20$i"
done
```

#### 测试多主题消费
```bash
# 发送到不同业务主题
curl -X POST "http://localhost:8089/api/kafka/quick-send/order-topic?message=Order%20Created"
curl -X POST "http://localhost:8089/api/kafka/quick-send/payment-topic?message=Payment%20Processed"
curl -X POST "http://localhost:8089/api/kafka/quick-send/notification-topic?message=Notification%20Sent"
```

#### 测试消息过滤
```bash
# 发送会被过滤的消息
curl -X POST "http://localhost:8089/api/kafka/quick-send/filtered-topic?message=This%20will%20be%20filtered"

# 发送会被处理的消息（包含"process"）
curl -X POST "http://localhost:8089/api/kafka/quick-send/filtered-topic?message=Please%20process%20this%20message"
```

## 配置说明

### Kafka配置
```yaml
galaxy:
  kafka:
    enable: true                              # 启用Kafka
    server:
      nodes: localhost:9092                   # Kafka服务器地址
    log:
      enabled: true                           # 启用日志拦截器
      max-batch-detail-count: 10            # 详细日志记录数
    producer:
      acks: all                               # 生产者确认配置
      retries: 3                              # 重试次数
      batch.size: 16384                       # 批次大小
    consumer:
      group.id: kafka-example-group           # 默认消费者组
      auto.offset.reset: earliest             # 偏移量重置策略
      concurrency: 3                          # 并发级别
      batch.listener: true                    # 启用批量监听器
```

## 监控和日志

应用启动后，可以通过日志观察：
1. 消息发送日志
2. 消息消费日志
3. 错误处理日志
4. 性能统计日志

日志级别可以通过配置调整：
```yaml
logging:
  level:
    cn.com.chinastock.cnf.kafka.examples: DEBUG
    org.apache.kafka: INFO
```

## 注意事项

1. 确保Kafka服务器已启动并可访问
2. 根据实际环境调整Kafka服务器地址
3. 不同的消费者组会独立消费消息
4. 批量消费可以提高吞吐量，但会增加延迟
5. 手动确认模式需要谨慎处理，避免消息丢失或重复消费
