package cn.com.chinastock.cnf.kafka.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.examples.dto.BatchMessageRequest;
import cn.com.chinastock.cnf.kafka.examples.service.KafkaProducerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.SendResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * Kafka批量消息发送控制器
 * 对应BatchMessageConsumer的各种消费模式
 */
@RestController
@RequestMapping("/api/kafka/batch")
public class KafkaBatchController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaBatchController.class);

    private final KafkaProducerService kafkaProducerService;

    public KafkaBatchController(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
    }

    /**
     * 发送消息到批量消息基础消费模式
     * 对应 BatchMessageConsumer.consumeBatchMessages()
     * @param messages 消息列表
     * @param keyPrefix 消息键前缀（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-batch-basic")
    public BaseResponse<Map<String, Object>> sendToBatchBasic(
            @RequestBody List<String> messages,
            @RequestParam(required = false, defaultValue = "batch-basic") String keyPrefix) {
        try {
            String topic = "batch-message-topic";
            logger.info(LogCategory.APP_LOG, "Sending {} messages to batch basic consumer", messages.size());

            sendBatchMessage(messages, keyPrefix, topic);

            Map<String, Object> responseData = buildResponseData(messages, topic, "Batch Basic Message Consumer", "All messages sent successfully");

            return new BaseResponse<>(new Meta(true, "0", "Messages sent to batch basic consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send messages to batch basic consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send messages: " + e.getMessage()), null);
        }
    }

    /**
     * 发送消息到批量消息带元数据消费模式
     * 对应 BatchMessageConsumer.consumeBatchMessagesWithMetadata()
     * @param messages 消息列表
     * @param keyPrefix 消息键前缀（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-batch-metadata")
    public BaseResponse<Map<String, Object>> sendToBatchMetadata(
            @RequestBody List<String> messages,
            @RequestParam(required = false, defaultValue = "batch-metadata") String keyPrefix) {
        try {
            String topic = "batch-message-with-metadata-topic";
            logger.info(LogCategory.APP_LOG, "Sending {} messages to batch metadata consumer", messages.size());

            sendBatchMessage(messages, keyPrefix, topic);

            Map<String, Object> responseData = buildResponseData(messages, topic, "Batch Message With Metadata Consumer", "All messages sent successfully");

            return new BaseResponse<>(new Meta(true, "0", "Messages sent to batch metadata consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send messages to batch metadata consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send messages: " + e.getMessage()), null);
        }
    }

    private void sendBatchMessage(List<String> messages, String keyPrefix, String topic) throws InterruptedException, ExecutionException {
        List<BatchMessageRequest.MessageItem> messageItems = new ArrayList<>();
        for (int i = 0; i < messages.size(); i++) {
            String key = keyPrefix + "-" + System.currentTimeMillis() + "-" + i;
            messageItems.add(new BatchMessageRequest.MessageItem(key, messages.get(i)));
        }

        BatchMessageRequest request = new BatchMessageRequest(topic, messageItems);
        List<CompletableFuture<SendResult<String, String>>> futures = kafkaProducerService.sendBatchMessages(request);

        // 等待所有消息发送完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        allFutures.get();
    }

    private static Map<String, Object> buildResponseData(List<String> messages, String topic, String consumerMode, String status) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("topic", topic);
        responseData.put("messageCount", messages.size());
        responseData.put("consumerMode", consumerMode);
        responseData.put("status", status);
        return responseData;
    }
}
