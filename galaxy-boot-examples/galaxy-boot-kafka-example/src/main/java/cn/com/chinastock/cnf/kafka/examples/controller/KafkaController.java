package cn.com.chinastock.cnf.kafka.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.examples.dto.MessageRequest;
import cn.com.chinastock.cnf.kafka.examples.service.KafkaProducerService;
import org.springframework.kafka.support.SendResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Kafka消息发送控制器
 */
@RestController
@RequestMapping("/api/kafka")
public class KafkaController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaController.class);

    private final KafkaProducerService kafkaProducerService;

    public KafkaController(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
    }

    /**
     * 发送消息到单个消息基础消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessage()
     * @param message 消息内容
     * @return 发送结果响应
     */
    @PostMapping("/send-single-message")
    public BaseResponse<Map<String, Object>> sendToSingleBasic(@RequestParam String message) {
        try {
            String topic = "single-message-topic";
            MessageRequest request = new MessageRequest(topic, "single-basic-" + System.currentTimeMillis(), message);

            CompletableFuture<SendResult<String, String>> future = kafkaProducerService.sendMessage(request);

            Map<String, Object> responseData = buildResponseData(future.get(), "", "Single Basic Message Consumer");

            return new BaseResponse<>(new Meta(true, "0", "Message sent to single basic consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single basic consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + e.getMessage()), null);
        }
    }

    /**
     * 发送消息到单个消息带元数据消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessageWithMetadata()
     * @param message 消息内容
     * @param key 消息键（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-single-metadata")
    public BaseResponse<Map<String, Object>> sendToSingleMetadata(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        try {
            String topic = "single-message-with-metadata-topic";
            String messageKey = key.isEmpty() ? "metadata-" + System.currentTimeMillis() : key;
            MessageRequest request = new MessageRequest(topic, messageKey, message);

            CompletableFuture<SendResult<String, String>> future = kafkaProducerService.sendMessage(request);

            Map<String, Object> responseData = buildResponseData(future.get(), messageKey, "Single Message With Metadata Consumer");

            return new BaseResponse<>(new Meta(true, "0", "Message sent to single metadata consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single metadata consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + e.getMessage()), null);
        }
    }

    /**
     * 发送消息到单个消息带Header消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessageWithHeaders()
     * @param message 消息内容
     * @param key 消息键（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-single-headers")
    public BaseResponse<Map<String, Object>> sendToSingleHeaders(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        try {
            String topic = "single-message-with-headers-topic";
            String messageKey = key.isEmpty() ? "headers-" + System.currentTimeMillis() : key;
            MessageRequest request = new MessageRequest(topic, messageKey, message);

            CompletableFuture<SendResult<String, String>> future = kafkaProducerService.sendMessage(request);

            Map<String, Object> responseData = buildResponseData(future.get(), messageKey, "Single Message With Headers Consumer");

            return new BaseResponse<>(new Meta(true, "0", "Message sent to single headers consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single headers consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + e.getMessage()), null);
        }
    }

    /**
     * 发送消息到单个消息手动确认消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessageWithManualAck()
     * @param message 消息内容
     * @param key 消息键（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-single-manual-ack")
    public BaseResponse<Map<String, Object>> sendToSingleManualAck(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        try {
            String topic = "single-message-manual-ack-topic";
            String messageKey = key.isEmpty() ? "manual-ack-" + System.currentTimeMillis() : key;
            MessageRequest request = new MessageRequest(topic, messageKey, message);

            CompletableFuture<SendResult<String, String>> future = kafkaProducerService.sendMessage(request);

            Map<String, Object> responseData = buildResponseData(future.get(), messageKey, "Single Message Manual Ack Consumer");

            return new BaseResponse<>(new Meta(true, "0", "Message sent to single manual ack consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single manual ack consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + e.getMessage()), null);
        }
    }

    /**
     * 发送消息到单个消息错误处理消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessageWithErrorHandling()
     * @param message 消息内容（包含"error"会触发错误处理）
     * @param key 消息键（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-single-error")
    public BaseResponse<Map<String, Object>> sendToSingleError(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        try {
            String topic = "single-message-error-topic";
            String messageKey = key.isEmpty() ? "error-" + System.currentTimeMillis() : key;
            MessageRequest request = new MessageRequest(topic, messageKey, message);

            CompletableFuture<SendResult<String, String>> future = kafkaProducerService.sendMessage(request);

            Map<String, Object> responseData = buildResponseData(future.get(), messageKey, "Single Message Error Consumer");

            return new BaseResponse<>(new Meta(true, "0", "Message sent to single error consumer successfully"), responseData);
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single error consumer", e);
            return new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + e.getMessage()), null);
        }
    }

    private static Map<String, Object> buildResponseData(SendResult<String, String> result, String key, String consumerMode) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("topic", result.getRecordMetadata().topic());
        responseData.put("partition", result.getRecordMetadata().partition());
        responseData.put("offset", result.getRecordMetadata().offset());
        responseData.put("key", key);
        responseData.put("consumerMode", consumerMode);
        return responseData;
    }
}
