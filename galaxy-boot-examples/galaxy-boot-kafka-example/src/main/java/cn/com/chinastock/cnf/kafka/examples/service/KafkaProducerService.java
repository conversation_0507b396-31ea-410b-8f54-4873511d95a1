package cn.com.chinastock.cnf.kafka.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.examples.dto.BatchMessageRequest;
import cn.com.chinastock.cnf.kafka.examples.dto.MessageRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Kafka消息生产者服务
 */
@Service
public class KafkaProducerService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplateForString;

    private final KafkaTemplate<Integer, String> kafkaTemplate;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplateForString, KafkaTemplate<Integer, String> kafkaTemplate) {
        this.kafkaTemplateForString = kafkaTemplateForString;
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * 发送单个消息（使用String key）
     * @param request 消息发送请求
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, String>> sendMessage(MessageRequest request) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                   request.getTopic(), request.getKey(), request.getMessage());

        CompletableFuture<SendResult<String, String>> kafkaFuture =
            kafkaTemplateForString.send(request.getTopic(), request.getKey(), request.getMessage());

        return kafkaFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + request.getTopic(), ex);
            } else {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }
        });
    }

    /**
     * 发送单个消息（使用Integer key）
     * @param topic 主题名称
     * @param key 消息键（整数类型）
     * @param message 消息内容
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<Integer, String>> sendMessageWithIntegerKey(String topic, Integer key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                   topic, key, message);

        CompletableFuture<SendResult<Integer, String>> kafkaFuture =
            kafkaTemplate.send(topic, key, message);

        return kafkaFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, ex);
            } else {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }
        });
    }

    /**
     * 批量发送消息
     * @param request 批量消息发送请求
     * @return 发送结果的CompletableFuture列表
     */
    public List<CompletableFuture<SendResult<String, String>>> sendBatchMessages(BatchMessageRequest request) {
        logger.info(LogCategory.APP_LOG, "Sending batch messages to topic: {}, count: {}", 
                   request.getTopic(), request.getMessages().size());
        
        List<CompletableFuture<SendResult<String, String>>> futures = new ArrayList<>();
        
        for (BatchMessageRequest.MessageItem item : request.getMessages()) {
            MessageRequest messageRequest = new MessageRequest(request.getTopic(), item.getKey(), item.getMessage());
            CompletableFuture<SendResult<String, String>> future = sendMessage(messageRequest);
            futures.add(future);
        }
        
        return futures;
    }

    /**
     * 发送消息到指定分区
     * @param topic 主题名称
     * @param partition 分区号
     * @param key 消息键
     * @param message 消息内容
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, String>> sendMessageToPartition(String topic, Integer partition, String key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, partition: {}, key: {}, message: {}",
                   topic, partition, key, message);

        CompletableFuture<SendResult<String, String>> kafkaFuture =
            kafkaTemplateForString.send(topic, partition, key, message);

        return kafkaFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, ex);
            } else {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }
        });
    }
}
