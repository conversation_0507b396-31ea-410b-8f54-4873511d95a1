package cn.com.chinastock.cnf.kafka.examples.dto;

/**
 * 消息发送请求DTO
 */
public class MessageRequest {
    
    private String topic;
    private String key;
    private String message;
    
    public MessageRequest() {
    }
    
    public MessageRequest(String topic, String key, String message) {
        this.topic = topic;
        this.key = key;
        this.message = message;
    }
    
    public String getTopic() {
        return topic;
    }
    
    public void setTopic(String topic) {
        this.topic = topic;
    }
    
    public String getKey() {
        return key;
    }
    
    public void setKey(String key) {
        this.key = key;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "MessageRequest{" +
                "topic='" + topic + '\'' +
                ", key='" + key + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
