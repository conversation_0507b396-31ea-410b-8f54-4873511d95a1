package cn.com.chinastock.cnf.webclient.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webclient.examples.client.LogClient;
import cn.com.chinastock.cnf.webclient.exception.GalaxyWebClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/test/webclient")
public class WebClientController {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    @Autowired
    private LogClient logClient;

    @GetMapping("/log-exception")
    public Mono callLogException() {
        logger.info("callLogException");
        return logClient.logException()
                .map(result -> new BaseResponse<>(new Meta(true, "0", "success"), "Log exception called"))
                .onErrorResume(throwable -> {
                    if (throwable instanceof GalaxyWebClientException e) {
                        return Mono.just(new BaseResponse<>(e.getMeta(), "Log exception succeed"));
                    }
                    return Mono.error(throwable);
                });
    }

    @GetMapping("/not-found")
    public Mono callNotFoundAPI() {
        logger.info("callLogException");

        return logClient.notFound();
    }

    @GetMapping("/multi-line")
    public Flux callMultiLine() {
        logger.info("callLogException");

        return logClient.multiLine();
    }

    @GetMapping("/esb")
    public Mono callESB() {
        logger.info("callESB");

        return logClient.esbCheck("1234567890");
    }

    @GetMapping("/echo")
    public Mono echo(@RequestParam String input) {
        return Mono.just("echo: " + input);
    }

    @GetMapping("/echo-body")
    public Mono echoBody(@RequestBody Mono<String> input) {
        return input.map(s -> "echo: " + s);
    }
}