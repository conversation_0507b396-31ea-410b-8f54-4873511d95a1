server:
  port: 8089

spring:
  application:
    name: galaxy-boot-webclient-example-service
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            log-service:
              - uri: http://localhost:8087
              - uri: http://localhost:8087

galaxy:
  webclient:
    esb:
      user: user
      password: password
    log-service:
      url: http://localhost:8087
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: true
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: false
    exception-pretty-print: true

logging:
  level:
    cn.com.chinastock.cnf.webclient.filter: INFO