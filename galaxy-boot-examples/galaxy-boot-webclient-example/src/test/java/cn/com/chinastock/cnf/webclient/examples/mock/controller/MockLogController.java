package cn.com.chinastock.cnf.webclient.examples.mock.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webclient.examples.mock.dto.LogDTO;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/test/log")
public class MockLogController {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    @GetMapping("/exception")
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Mono<BaseResponse> logException() {
        logger.info("Mock: Log exception called");
        return Mono.just(new BaseResponse(new Meta(Boolean.FALSE, "1001", "exception"), "Log exception"));
    }

    @GetMapping("/not-found")
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Mono<Void> notFound() {
        logger.info("Mock: Not found called - returning 404");
        return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Resource not found"));
    }

    @GetMapping("/multi-line")
    public Flux<LogDTO> multiLine() {
        logger.info("Mock: Multi-line called");
        return Flux.just(new LogDTO("Line 1: Application started"), new LogDTO("Line 2: Processing request"), new LogDTO("Line 3: Operation completed"));
    }

    @GetMapping("/esb-check")
    public Mono<String> esbCheck(@RequestHeader(value = "Function-No", required=false) String functionNo) {
        logger.info("Mock: esb called, {}", functionNo);
        return Mono.just("Caller-System-Code" + functionNo);
    }
}