package cn.com.chinastock.cnf.examples.cache.controller;

import cn.com.chinastock.cnf.examples.cache.domain.Product;
import cn.com.chinastock.cnf.examples.cache.domain.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
public class ProductController {

    @Autowired
    private ProductService productService;

    // MVC 端点
    @GetMapping("/products/{id}")
    public Product getProduct(@PathVariable String id) {
        return productService.getProductById(id);
    }

    // WebFlux 端点
    @GetMapping("/reactive/products/{id}")
    public Mono<Product> getProductReactive(@PathVariable String id) {
        return productService.getProductByIdReactive(id);
    }
}