package cn.com.chinastock.cnf.examples.cache.domain;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;

@Service
public class ProductService {
    private static final Logger log = LoggerFactory.getLogger(ProductService.class);

    /**
     * MVC模式：返回普通对象，使用短期缓存
     * 
     * @param id 产品ID
     * @return 产品信息对象
     */
    @Cacheable(cacheNames = "shortTermCache", key = "#id")
    public Product getProductById(String id) {
        log.info("【MVC】正在从数据库查询产品，ID: {}", id);
        // 模拟耗时操作
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return new Product(id, "Product " + id, "Description for MVC product.");
    }

    /**
     * WebFlux模式：返回 Mono<Product>，使用长期缓存
     * 
     * @param id 产品ID
     * @return 包含产品信息的Mono对象
     */
    @Cacheable(cacheNames = "longTermCache", key = "#id")
    public Mono<Product> getProductByIdReactive(String id) {
        log.info("【WebFlux】正在从数据库查询产品 (reactive)，ID: {}", id);
        // 模拟异步耗时操作
        return Mono.fromSupplier(() -> {
                    log.info("【WebFlux】Mono's supplier is executing for ID: {}", id);
                    return new Product(id, "Reactive Product " + id, "Description for WebFlux product.");
                })
                .delayElement(Duration.ofSeconds(1));
    }
}