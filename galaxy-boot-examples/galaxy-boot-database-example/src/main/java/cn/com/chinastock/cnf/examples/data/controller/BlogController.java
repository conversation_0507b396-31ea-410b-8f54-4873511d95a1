package cn.com.chinastock.cnf.examples.data.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.datasource.GalaxyDynamicDataSource;
import cn.com.chinastock.cnf.examples.data.entity.Blog;
import cn.com.chinastock.cnf.examples.data.mapper.BlogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/blog")
public class BlogController {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BlogMapper blogMapper;

    @PostMapping("/")
    public Blog autoCreate() throws SQLException {
        debugPool();
        Blog blog = new Blog();
        blog.setTitle("title-" + System.currentTimeMillis());
        blog.setContent("content-" + System.currentTimeMillis());
        blog.setAuthor("author-" + System.currentTimeMillis());
        blogMapper.save(blog);
        return blog;
    }

    @PostMapping("/batch")
    public List<Blog> batchSave(@RequestParam(name = "batchSize", defaultValue = "10000") int batchSize,
                                @RequestParam(name = "batchCount", defaultValue = "10") int batchCount) throws SQLException {
        debugPool();

        List<Blog> createdBlogs = new ArrayList<>();
        List<CompletableFuture<List<Blog>>> futures = new ArrayList<>();

        for (int batch = 0; batch < batchCount; batch++) {
            int finalBatch = batch;
            CompletableFuture<List<Blog>> future = CompletableFuture.supplyAsync(() -> {
                List<Blog> batchBlogs = new ArrayList<>();
                try {
                    for (int i = 0; i < batchSize; i++) {
                        // 先执行查询操作
                        jdbcTemplate.queryForList("SELECT COUNT(*) FROM blog");

                        // 创建并插入新博客
                        Blog blog = new Blog();
                        blog.setTitle("title-" + finalBatch + "-" + i);
                        blog.setContent("content-" + finalBatch + "-" + i);
                        blog.setAuthor("author-" + finalBatch);

                        jdbcTemplate.update(
                                "INSERT INTO blog (title, content, author) VALUES (?, ?, ?)",
                                blog.getTitle(), blog.getContent(), blog.getAuthor()
                        );

                        batchBlogs.add(blog);

                        // 模拟一些处理时间
                        Thread.sleep(10);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return batchBlogs;
            });
            futures.add(future);
        }

        // 等待所有批次完成并收集结果
        futures.forEach(future -> {
            try {
                createdBlogs.addAll(future.get());
            } catch (Exception e) {
                throw new RuntimeException("批量保存失败", e);
            }
        });

        return createdBlogs;
    }

    private void debugPool() {
        try {
            DataSource dataSource = Objects.requireNonNull(jdbcTemplate.getDataSource());
            GalaxyDynamicDataSource dynamicDataSource = (GalaxyDynamicDataSource) dataSource;
            String currentPoolName = dynamicDataSource.getCurrentPoolName();
            GalaxyLogger.info("当前数据源连接池名称: " + currentPoolName);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to get current pool name: " + e.getMessage(), e);
        }
    }
}
