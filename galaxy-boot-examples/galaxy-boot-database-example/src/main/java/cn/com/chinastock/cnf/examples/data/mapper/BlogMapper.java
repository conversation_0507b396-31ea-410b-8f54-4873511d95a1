package cn.com.chinastock.cnf.examples.data.mapper;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BlogMapper extends JpaRepository<Blog, Long> {
    List<Blog> findByTitleContaining(String title);
    List<Blog> findByAuthor(String author);
    Page<Blog> findByAuthor(String author, Pageable pageable);
    
    @Query("SELECT b FROM Blog b WHERE b.author = :author AND b.title LIKE %:keyword%")
    List<Blog> findByAuthorAndTitleKeyword(@Param("author") String author, @Param("keyword") String keyword);
    
    @Query("SELECT COUNT(b) FROM Blog b GROUP BY b.author")
    List<Long> countBlogsGroupByAuthor();
    
    @Modifying
    @Query("UPDATE Blog b SET b.title = :newTitle WHERE b.author = :author")
    int updateTitlesByAuthor(@Param("author") String author, @Param("newTitle") String newTitle);
    
    @Modifying
    @Query("DELETE FROM Blog b WHERE b.author = :author")
    int deleteByAuthor(@Param("author") String author);
}