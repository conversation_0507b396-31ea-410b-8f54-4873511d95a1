package cn.com.chinastock.cnf.feign.examples.client;

import cn.com.chinastock.cnf.feign.examples.dto.LogDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "log-service")
public interface LogClient {

    @GetMapping("/api/test/log/exception")
    void logException();

    @GetMapping("/api/test/log/not-found")
    void notFound();

    @GetMapping("/api/test/log/multi-line")
    LogDTO multiLine();

    @GetMapping(value = "/api/test/log/gzip", params = {"length"})
    String gzipResponse(@RequestParam(value = "length") int length);
}
