server:
  port: 8080

spring:
  application:
    name: galaxy-boot-webflux-example
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
    consumer:
      group-id: webflux-kafka-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: true
      auto-commit-interval: 1000
      session-timeout-ms: 30000
      heartbeat-interval-ms: 3000
      max-poll-records: 500
      max-poll-interval-ms: 300000

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

galaxy:
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: true
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

  kafka:
    enabled: true
    log:
      enabled: true
      max-batch-detail-count: 10

  swagger:
    auth:
      username: admin     # Swagger UI 访问用户名
      password: P@5swOrd  # Swagger UI 访问密码

  security:
    user:
      name: user
      password: password
    input-protect: true
    output-protect: true
    actuator:
      protect: false
    #      whitelist:
    #        - 127.0.0.1
    #        - ***********/24    # 表示从 `***********` 到 `*************` 的所有 IP 地址。
    #        - 0:0:0:0:0:0:0:1  # 表示 `localhost` 的 IP 地址。
    csrf:
      protect: false
    #      whitelist:
    #        - /api/**
    #        - /swagger-ui/**
    #        - /v3/api-docs/**
    #      blacklist:
    #        - /api/security/csrf/**
    cors:
      protect: true
      whitelist:
        - https://*.chinastock.com.cn

logging:
  level:
    com.ctrip.framework: OFF