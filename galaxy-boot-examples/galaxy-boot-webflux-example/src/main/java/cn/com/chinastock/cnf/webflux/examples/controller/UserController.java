package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.model.User;
import cn.com.chinastock.cnf.webflux.examples.service.UserService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 用户管理 WebFlux 控制器
 * 
 * <p>简化的响应式用户管理接口</p>
 * 
 * <AUTHOR> Boot Team
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UserController.class);
    
    private final UserService userService;
    
    public UserController(UserService userService) {
        this.userService = userService;
    }
    
    /**
     * 获取所有用户（流式返回）
     * 
     * @return 用户列表的Flux
     */
    @GetMapping
    public Flux<User> getAllUsers() {
        logger.info("获取所有用户列表");
        return userService.getAllUsers()
                .doOnNext(user -> logger.debug("返回用户: {}", user.getUsername()));
    }
    
    /**
     * 根据ID获取用户
     * 
     * @param id 用户ID
     * @return 用户信息的Mono
     */
    @GetMapping("/{id}")
    public Mono<User> getUserById(@PathVariable Long id) {
        logger.info("获取用户: {}", id);
        return userService.getUserById(id);
    }
    
    /**
     * 创建新用户
     * 
     * @param userMono 用户信息
     * @return 创建成功的用户信息Mono
     */
    @PostMapping
    public Mono<User> createUser(@RequestBody Mono<User> userMono) {
        logger.info("创建新用户");
        return userMono
                .doOnNext(user -> logger.debug("接收到用户数据: {}", user.getUsername()))
                .flatMap(userService::createUser)
                .doOnNext(user -> logger.info("用户创建成功: {}", user.getUsername()));
    }
    
    /**
     * 更新用户信息
     * 
     * @param id 用户ID
     * @param userMono 更新的用户信息
     * @return 更新后的用户信息Mono
     */
    @PutMapping("/{id}")
    public Mono<User> updateUser(@PathVariable Long id, @RequestBody Mono<User> userMono) {
        logger.info("更新用户: {}", id);
        return userMono.flatMap(user -> userService.updateUser(id, user))
                .doOnNext(user -> logger.info("用户更新成功: {}", user.getUsername()));
    }
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 删除操作的Mono
     */
    @DeleteMapping("/{id}")
    public Mono<Void> deleteUser(@PathVariable Long id) {
        logger.info("删除用户: {}", id);
        return userService.deleteUser(id)
                .doOnSuccess(aVoid -> logger.info("用户删除成功: {}", id));
    }
    
    /**
     * 搜索用户（演示查询参数和分页）
     * 
     * @param username 用户名关键字，可选
     * @param page 页码，默认为0
     * @param size 页大小，默认为10
     * @return 搜索结果的Flux
     */
    @GetMapping("/search")
    public Flux<User> searchUsers(@RequestParam(required = false) String username,
                                  @RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "10") int size) {
        logger.info("搜索用户: username={}, page={}, size={}", username, page, size);
        return userService.searchUsers(username, page, size)
                .doOnNext(user -> logger.debug("搜索结果: {}", user.getUsername()));
    }
} 