package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.exception.BusinessException;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 异常处理示例控制器
 * 
 * <p>展示 WebFlux 错误处理机制</p>
 * 
 * <AUTHOR> Boot Team
 */
@RestController
@RequestMapping("/api/exception")
public class ExceptionController {
    
    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(ExceptionController.class);
    
    /**
     * 抛出运行时异常
     * 
     * @return 错误响应的Mono
     */
    @GetMapping("/runtime-error")
    public Mono<String> runtimeError() {
        logger.info("准备抛出运行时异常");
        return Mono.error(new RuntimeException("这是一个运行时异常示例"))
                .cast(String.class);
    }
    
    /**
     * 抛出自定义异常
     * 
     * @return 错误响应的Mono
     */
    @GetMapping("/custom-error")
    public Mono<String> customError() {
        logger.info("准备抛出业务异常");
        return Mono.error(new BusinessException("1000", "这是一个业务异常"))
                .cast(String.class);
    }
    
}