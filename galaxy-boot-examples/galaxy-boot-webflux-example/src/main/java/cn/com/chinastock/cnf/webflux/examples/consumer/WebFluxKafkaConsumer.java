package cn.com.chinastock.cnf.webflux.examples.consumer;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.utils.IdempotencyUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * WebFlux环境下的Kafka消息消费者
 * 
 * <p>演示在WebFlux环境下单条消息的消费处理，重点测试日志拦截和TraceId传递</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
@Component
public class WebFluxKafkaConsumer {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(WebFluxKafkaConsumer.class);

    /**
     * 消费单个消息 - 带元数据模式（WebFlux版本）
     * 监听 webflux-single-message-with-metadata-topic 主题
     * 
     * @param record 消费记录
     */
    @KafkaListener(topics = "webflux-single-message-with-metadata-topic", groupId = "webflux-single-metadata-group")
    public void consumeSingleMessageWithMetadata(ConsumerRecord<String, String> record) {
        String idempotencyId = IdempotencyUtils.extractIdempotencyId(record).orElse(null);

        logger.info(LogCategory.APP_LOG, 
                   "WebFlux - Single message with metadata - Topic: {}, Partition: {}, Offset: {}, Key: {}, Value: {}, IdempotencyId: {}",
                   record.topic(), record.partition(), record.offset(), record.key(), record.value(), idempotencyId);
        
        try {
            // 模拟业务处理
            processMessage(record.value(), "webflux-single-metadata");
            
            logger.info(LogCategory.APP_LOG, "WebFlux - Message processed successfully for offset: {}", record.offset());
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "WebFlux - Failed to process message at offset: " + record.offset(), e);
        }
    }

    /**
     * 模拟消息处理逻辑
     * 
     * @param message 消息内容
     * @param consumerType 消费者类型
     */
    private void processMessage(String message, String consumerType) {
        logger.debug(LogCategory.APP_LOG, "WebFlux - Processing message in {}: {}", consumerType, message);
        
        // 模拟处理时间
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn(LogCategory.APP_LOG, "WebFlux - Message processing interrupted");
        }
        
        logger.debug(LogCategory.APP_LOG, "WebFlux - Message processing completed in {}", consumerType);
    }
}
