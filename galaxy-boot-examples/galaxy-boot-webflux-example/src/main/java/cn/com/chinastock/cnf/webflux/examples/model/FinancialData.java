package cn.com.chinastock.cnf.webflux.examples.model;

import javax.annotation.Nullable;
import java.math.BigDecimal;

public class FinancialData {
    private String name;
    private BigDecimal amount;
    private transient String transientField;
    @Nullable
    private String nullableField;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTransientField() {
        return transientField;
    }

    public void setTransientField(String transientField) {
        this.transientField = transientField;
    }

    @Nullable
    public String getNullableField() {
        return nullableField;
    }

    public void setNullableField(@Nullable String nullableField) {
        this.nullableField = nullableField;
    }

}
