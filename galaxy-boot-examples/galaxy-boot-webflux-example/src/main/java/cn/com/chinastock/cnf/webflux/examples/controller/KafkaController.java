package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webflux.examples.dto.MessageRequest;
import cn.com.chinastock.cnf.webflux.examples.service.KafkaProducerService;
import org.springframework.kafka.support.SendResult;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * WebFlux环境下的Kafka消息处理控制器
 * 
 * <p>提供响应式的Kafka消息发送API，用于测试在WebFlux模式下Producer和Consumer在日志拦截和TraceId传递上的功能</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/api/kafka")
public class KafkaController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaController.class);

    private final KafkaProducerService kafkaProducerService;

    public KafkaController(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
    }

    /**
     * 发送消息到单个消息带元数据消费模式（WebFlux版本）
     * 对应 WebFluxKafkaConsumer.consumeSingleMessageWithMetadata()
     * 
     * @param message 消息内容
     * @param key 消息键（可选）
     * @return 发送结果响应的Mono
     */
    @PostMapping("/webflux/send-single-metadata")
    public Mono<BaseResponse<Map<String, Object>>> sendToSingleMetadata(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        
        logger.info(LogCategory.APP_LOG, "WebFlux - Received request to send message with metadata: {}", message);
        
        String topic = "webflux-single-message-with-metadata-topic";
        String messageKey = key.isEmpty() ? "webflux-metadata-" + System.currentTimeMillis() : key;
        MessageRequest request = new MessageRequest(topic, messageKey, message);

        return kafkaProducerService.sendMessage(request)
                .map(result -> {
                    Map<String, Object> responseData = buildResponseData(result, messageKey, "WebFlux Single Message With Metadata Consumer");
                    logger.info(LogCategory.APP_LOG, "WebFlux - Message sent successfully to single metadata consumer");
                    return new BaseResponse<>(new Meta(true, "0", "WebFlux - Message sent to single metadata consumer successfully"), responseData);
                })
                .onErrorResume(e -> {
                    logger.error(LogCategory.EXCEPTION_LOG, "WebFlux - Failed to send message to single metadata consumer", e);
                    return Mono.just(new BaseResponse<>(new Meta(false, "500", "WebFlux - Failed to send message: " + e.getMessage()), null));
                })
                .doOnSubscribe(subscription -> logger.debug(LogCategory.APP_LOG, "WebFlux - Starting to send message to metadata consumer"))
                .doOnSuccess(response -> logger.debug(LogCategory.APP_LOG, "WebFlux - Message send operation completed for metadata consumer"));
    }

    /**
     * 构建响应数据
     * 
     * @param result Kafka发送结果
     * @param key 消息键
     * @param consumerMode 消费者模式
     * @return 响应数据Map
     */
    private static Map<String, Object> buildResponseData(SendResult<String, String> result, String key, String consumerMode) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("topic", result.getRecordMetadata().topic());
        responseData.put("partition", result.getRecordMetadata().partition());
        responseData.put("offset", result.getRecordMetadata().offset());
        responseData.put("key", key);
        responseData.put("consumerMode", consumerMode);
        responseData.put("environment", "WebFlux");
        return responseData;
    }
}
