package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webflux.examples.dto.MessageRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.CompletableFuture;

/**
 * Kafka消息生产者服务
 * 
 * <p>在WebFlux环境下提供响应式的Kafka消息发送功能</p>
 * 
 * <AUTHOR> Boot Team
 */
@Service
public class KafkaProducerService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplate;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * 响应式发送单个消息
     * 
     * @param request 消息发送请求
     * @return 发送结果的Mono
     */
    public Mono<SendResult<String, String>> sendMessage(MessageRequest request) {
        logger.info(LogCategory.APP_LOG, "WebFlux - Sending message to topic: {}, key: {}, message: {}",
                   request.getTopic(), request.getKey(), request.getMessage());

        return Mono.fromFuture(() -> {
            CompletableFuture<SendResult<String, String>> kafkaFuture =
                kafkaTemplate.send(request.getTopic(), request.getKey(), request.getMessage());

            return kafkaFuture.whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error(LogCategory.EXCEPTION_LOG, "WebFlux - Failed to send message to topic: " + request.getTopic(), ex);
                } else {
                    logger.info(LogCategory.APP_LOG, "WebFlux - Message sent successfully to topic: {}, partition: {}, offset: {}",
                               result.getRecordMetadata().topic(),
                               result.getRecordMetadata().partition(),
                               result.getRecordMetadata().offset());
                }
            });
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSubscribe(subscription -> logger.debug(LogCategory.APP_LOG, "WebFlux - Starting to send message to topic: {}", request.getTopic()))
        .doOnSuccess(result -> logger.debug(LogCategory.APP_LOG, "WebFlux - Message send operation completed for topic: {}", request.getTopic()))
        .doOnError(error -> logger.error(LogCategory.EXCEPTION_LOG, "WebFlux - Message send operation failed for topic: " + request.getTopic(), error));
    }

}
