# 支付订单API使用文档

## 概述

支付订单API提供了完整的订单生命周期管理功能，包括创建、确认、支付、取消、查询等操作。所有数据都存储在内存中，适合演示和测试使用。

## API接口列表

### 1. 创建支付订单

**接口地址：** `POST /api/payment-orders`

**请求体：**
```json
{
  "amount": 99.99,
  "currency": "CNY",
  "description": "iPhone 15 Pro 手机",
  "customer_id": "CUST001",
  "customer_name": "张三",
  "payment_method": "支付宝"
}
```

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "订单创建成功"
  },
  "data": {
    "id": "6",
    "order_no": "1000006",
    "amount": 99.99,
    "currency": "CNY",
    "status": "PENDING_CONFIRMATION",
    "description": "iPhone 15 Pro 手机",
    "customer_id": "CUST001",
    "customer_name": "张三",
    "payment_method": "支付宝",
    "created_time": "2024-01-15 10:30:00",
    "updated_time": "2024-01-15 10:30:00",
    "paid_time": null
  }
}
```

### 2. 确认订单

**接口地址：** `POST /api/payment-orders/{orderId}/confirm`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "订单确认成功"
  },
  "data": {
    "id": "6",
    "order_no": "1000006",
    "amount": 99.99,
    "currency": "CNY",
    "status": "PENDING_PAYMENT",
    "description": "iPhone 15 Pro 手机",
    "customer_id": "CUST001",
    "customer_name": "张三",
    "payment_method": "支付宝",
    "created_time": "2024-01-15 10:30:00",
    "updated_time": "2024-01-15 10:35:00",
    "paid_time": null
  }
}
```

### 3. 支付订单

**接口地址：** `POST /api/payment-orders/{orderId}/pay`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "订单支付成功"
  },
  "data": {
    "id": "6",
    "order_no": "1000006",
    "amount": 99.99,
    "currency": "CNY",
    "status": "PAID",
    "description": "iPhone 15 Pro 手机",
    "customer_id": "CUST001",
    "customer_name": "张三",
    "payment_method": "支付宝",
    "created_time": "2024-01-15 10:30:00",
    "updated_time": "2024-01-15 10:40:00",
    "paid_time": "2024-01-15 10:40:00"
  }
}
```

### 4. 取消订单

**接口地址：** `POST /api/payment-orders/{orderId}/cancel`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "订单取消成功"
  },
  "data": {
    "id": "6",
    "order_no": "1000006",
    "amount": 99.99,
    "currency": "CNY",
    "status": "CANCELLED",
    "description": "iPhone 15 Pro 手机",
    "customer_id": "CUST001",
    "customer_name": "张三",
    "payment_method": "支付宝",
    "created_time": "2024-01-15 10:30:00",
    "updated_time": "2024-01-15 10:45:00",
    "paid_time": null
  }
}
```

### 5. 根据ID查询订单

**接口地址：** `GET /api/payment-orders/{orderId}`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "操作成功"
  },
  "data": {
    "id": "1",
    "order_no": "1000001",
    "amount": 99.99,
    "currency": "CNY",
    "status": "PAID",
    "description": "iPhone 15 Pro 手机",
    "customer_id": "CUST001",
    "customer_name": "张三",
    "payment_method": "支付宝",
    "created_time": "2024-01-15 08:00:00",
    "updated_time": "2024-01-15 08:00:00",
    "paid_time": "2024-01-15 08:30:00"
  }
}
```

### 6. 根据订单号查询订单

**接口地址：** `GET /api/payment-orders/by-order-no/{orderNo}`

### 7. 查询所有订单

**接口地址：** `GET /api/payment-orders`

### 8. 根据状态查询订单

**接口地址：** `GET /api/payment-orders/by-status?status=PENDING_PAYMENT`

### 9. 根据客户ID查询订单

**接口地址：** `GET /api/payment-orders/by-customer?customerId=CUST001`

### 10. 获取订单统计信息

**接口地址：** `GET /api/payment-orders/statistics`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "操作成功"
  },
  "data": {
    "totalOrders": 5,
    "paidOrders": 1,
    "pendingOrders": 1,
    "cancelledOrders": 1,
    "totalAmount": 99.99
  }
}
```

### 11. 获取所有订单状态

**接口地址：** `GET /api/payment-orders/statuses`

**响应示例：**
```json
{
  "meta": {
    "success": true,
    "code": "0000",
    "message": "操作成功"
  },
  "data": [
    {
      "code": "PENDING_CONFIRMATION",
      "description": "待确认"
    },
    {
      "code": "PENDING_PAYMENT",
      "description": "待支付"
    },
    {
      "code": "PAYING",
      "description": "支付中"
    },
    {
      "code": "PAID",
      "description": "已支付"
    },
    {
      "code": "CANCELLED",
      "description": "已取消"
    },
    {
      "code": "PAYMENT_FAILED",
      "description": "支付失败"
    },
    {
      "code": "REFUNDED",
      "description": "已退款"
    }
  ]
}
```

### 12. 删除订单（仅用于测试）

**接口地址：** `DELETE /api/payment-orders/{orderId}`

## 订单状态说明

- **PENDING_CONFIRMATION**: 待确认 - 订单刚创建时的状态
- **PENDING_PAYMENT**: 待支付 - 订单确认后的状态
- **PAYING**: 支付中 - 正在处理支付的状态
- **PAID**: 已支付 - 支付成功的状态
- **CANCELLED**: 已取消 - 订单被取消的状态
- **PAYMENT_FAILED**: 支付失败 - 支付处理失败的状态
- **REFUNDED**: 已退款 - 订单已退款的状态

## 订单状态流转

```
PENDING_CONFIRMATION → PENDING_PAYMENT → PAYING → PAID
         ↓                    ↓
    CANCELLED           PAYMENT_FAILED
```

## 示例数据

系统启动时会自动创建以下示例订单：

1. **订单ID: 1** - iPhone 15 Pro 手机 (已支付)
2. **订单ID: 2** - MacBook Air 笔记本电脑 (待支付)
3. **订单ID: 3** - AirPods Pro 耳机 (待确认)
4. **订单ID: 4** - iPad Air 平板电脑 (已取消)
5. **订单ID: 5** - Apple Watch Series 9 (支付失败)

## 错误处理

所有API都会返回统一的错误格式：

```json
{
  "meta": {
    "success": false,
    "code": "400",
    "message": "错误信息"
  },
  "data": null
}
```

常见错误：
- 订单不存在
- 订单状态不允许执行当前操作
- 已支付的订单不能取消
- 请求参数错误

## 使用示例

### 使用curl创建订单

```bash
curl -X POST http://localhost:8080/api/payment-orders \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 199.99,
    "currency": "CNY",
    "description": "AirPods Pro 耳机",
    "customer_id": "CUST006",
    "customer_name": "孙八",
    "payment_method": "微信支付"
  }'
```

### 使用curl确认订单

```bash
curl -X POST http://localhost:8080/api/payment-orders/6/confirm
```

### 使用curl支付订单

```bash
curl -X POST http://localhost:8080/api/payment-orders/6/pay
```

### 使用curl查询订单

```bash
curl -X GET http://localhost:8080/api/payment-orders/6
```

## MCP工具集成

所有API都使用`@Tool`注解进行了标记，可以通过MCP协议被AI助手调用，实现智能的订单管理功能。 