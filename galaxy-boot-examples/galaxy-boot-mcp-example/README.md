# Galaxy Boot MCP Example

> Galaxy Boot MCP Example 展示了如何使用 Galaxy Boot Starter MCP 来创建 MCP (Model Context Protocol) Server，提供天气信息查询、预报、比较和建议等功能。

## 功能特性

### 天气服务功能
- **当前天气查询**: 获取指定城市的当前天气信息
- **天气预报**: 获取指定城市的1-7天天气预报
- **天气比较**: 比较多个城市的天气情况
- **天气建议**: 根据天气情况提供出行和穿衣建议
- **支持城市列表**: 获取所有支持的城市列表

### MCP 协议支持
- **Tool 功能**: 支持工具调用和发现
- **SSE 传输**: 完整的 Server-Sent Events 接口
- **客户端兼容**: 与 Cursor 等 MCP 客户端完全兼容

## 快速开始

### 1. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

应用将在 `http://localhost:8089` 启动。

### 2. 配置 Cursor

在 Cursor 中添加 MCP Server 配置：

```json
{
  "mcpServers": {
    "galaxy-weather-server": {
      "command": "curl",
      "args": [
        "-N",
        "-H", "Accept: text/event-stream",
        "http://localhost:8089/sse"
      ],
      "env": {}
    }
  }
}
```

### 3. 测试 MCP 连接

使用 curl 测试 SSE 连接：

```bash
curl -N -H "Accept: text/event-stream" http://localhost:8089/sse
```

### 4. 测试 REST API

```bash
# 获取当前天气
curl "http://localhost:8089/weather/current?city=北京"

# 获取天气预报
curl "http://localhost:8089/weather/forecast?city=上海&days=3"

# 比较天气
curl "http://localhost:8089/weather/compare?cities=北京,上海,广州"

# 获取天气建议
curl "http://localhost:8089/weather/advice?city=深圳"

# 获取支持的城市列表
curl "http://localhost:8089/weather/cities"
```

## 项目结构

```
src/main/java/cn/com/chinastock/cnf/mcp/example/
├── controller/
│   └── WeatherController.java          # 天气控制器，包含 @Tool 注解
├── service/
│   └── WeatherService.java             # 天气服务实现
└── model/
    ├── WeatherInfo.java                # 天气信息模型
    ├── WeatherForecast.java            # 天气预报模型
    └── WeatherAdvice.java              # 天气建议模型
```

## 配置说明

### 极简配置方式

**只需一个启用开关**：

```yaml
# Galaxy MCP 配置 - 只需一个启用开关
galaxy:
  mcp:
    enabled: true

# Spring AI MCP Server 配置 - 使用 Spring AI 原生配置
spring:
  ai:
    mcp:
      server:
        enabled: true
        name: galaxy-weather-server
        version: 1.0.0
        instructions: "Galaxy Weather MCP Server - 提供天气信息查询、预报、比较和建议等功能"
        type: SYNC
        sse-endpoint: /sse
        sse-message-endpoint: /mcp/message
        base-url: ""
        request-timeout: 30
        capabilities:
          tool: true
          resource: false
          prompt: false
          completion: false
        tool-change-notification: true
        resource-change-notification: false
        prompt-change-notification: false
```

### 配置说明

| 配置项 | 说明 |
|--------|------|
| `galaxy.mcp.enabled` | 是否启用 Galaxy MCP Starter |
| `spring.ai.mcp.server.name` | MCP Server 名称 |
| `spring.ai.mcp.server.sse-endpoint` | SSE 端点路径 |
| `spring.ai.mcp.server.capabilities.tool` | 是否启用工具功能 |

## 使用方式

### 1. 自动注册（推荐）

使用 Spring AI 的 `@Tool` 注解，自动扫描和注册工具：

```java
@RestController
public class WeatherController {
    
    @GetMapping("/weather/current")
    @Tool(
        name = "getCurrentWeather",
        description = "获取指定城市的当前天气信息，包括温度、湿度、风速、气压等详细信息"
    )
    public WeatherInfo getCurrentWeather(@RequestParam String city) {
        return weatherService.getCurrentWeather(city);
    }
    
    @GetMapping("/weather/forecast")
    @Tool(
        name = "getWeatherForecast",
        description = "获取指定城市的天气预报，支持1-7天的预报"
    )
    public List<WeatherForecast> getWeatherForecast(
            @RequestParam String city,
            @RequestParam int days) {
        return weatherService.getWeatherForecast(city, days);
    }
}
```

### 2. 手动注册

如果需要更精细的控制，也可以手动创建 `ToolCallbackProvider`：

```java
@Configuration
public class MCPToolConfiguration {
    
    @Bean
    public ToolCallbackProvider weatherTools(WeatherService weatherService) {
        return MethodToolCallbackProvider.builder()
            .toolObjects(weatherService)
            .build();
    }
}
```

## API 接口

### MCP 工具接口

| 工具名称 | 描述 | 参数 |
|----------|------|------|
| `getCurrentWeather` | 获取指定城市的当前天气信息 | `city` (String) |
| `getWeatherForecast` | 获取指定城市的天气预报 | `city` (String), `days` (int) |
| `compareWeather` | 比较多个城市的天气情况 | `cities` (String) |
| `getWeatherAdvice` | 根据天气情况提供建议 | `city` (String) |
| `getSupportedCities` | 获取所有支持的城市列表 | 无 |

### REST API 接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/weather/current` | GET | 获取当前天气 |
| `/weather/forecast` | GET | 获取天气预报 |
| `/weather/compare` | GET | 比较天气 |
| `/weather/advice` | GET | 获取天气建议 |
| `/weather/cities` | GET | 获取支持的城市列表 |

## 示例请求

### MCP 工具调用示例

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "getCurrentWeather",
    "arguments": {
      "city": "北京"
    }
  }
}
```

### REST API 调用示例

```bash
# 获取北京天气
curl "http://localhost:8089/weather/current?city=北京"

# 获取上海3天预报
curl "http://localhost:8089/weather/forecast?city=上海&days=3"

# 比较三个城市天气
curl "http://localhost:8089/weather/compare?cities=北京,上海,广州"
```

## 最佳实践

### 1. 使用 Spring AI @Tool 注解

```java
// ✅ 推荐：使用 Spring AI 原生注解
@Tool(
    name = "getCurrentWeather",
    description = "获取指定城市的当前天气信息"
)
public WeatherInfo getCurrentWeather(@RequestParam String city) {
    return weatherService.getCurrentWeather(city);
}
```

### 2. 使用 Spring AI 原生配置

```yaml
# ✅ 推荐：使用 Spring AI 原生配置
spring:
  ai:
    mcp:
      server:
        enabled: true
        name: my-server
        # ... 其他配置

# ✅ 极简：只需一个启用开关
galaxy:
  mcp:
    enabled: true
```

### 3. 工具命名规范

- 使用清晰、描述性的名称
- 遵循 camelCase 命名约定
- 避免使用特殊字符

### 4. 参数设计

- 使用简单的基本类型参数
- 提供合理的默认值
- 进行参数验证

### 5. 错误处理

- 使用标准的 HTTP 状态码
- 返回有意义的错误信息
- 记录详细的错误日志

## 故障排除

### 常见问题

1. **应用启动失败**
   - 检查端口 8089 是否被占用
   - 确认依赖是否正确引入
   - 查看启动日志

2. **MCP 工具未注册**
   - 检查 `@Tool` 注解是否正确添加
   - 确认 Controller 被 Spring 扫描
   - 查看启动日志中的注册信息

3. **客户端连接失败**
   - 检查 SSE 端点是否可访问
   - 确认网络配置
   - 验证 MCP 配置格式

### 调试技巧

1. 启用详细日志：
```yaml
logging:
  level:
    cn.com.chinastock.cnf.mcp: DEBUG
    org.springframework.ai.mcp: DEBUG
    org.springframework.ai.tool: DEBUG
```

2. 检查 SSE 连接：
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8089/sse
```

3. 验证工具注册：
查看启动日志中的工具注册信息

## 开发指南

### 添加新的天气工具

1. 在 `WeatherController` 中添加新的方法：

```java
@GetMapping("/weather/new-feature")
@Tool(
    name = "newWeatherFeature",
    description = "新的天气功能描述"
)
public Object newWeatherFeature(@RequestParam String param) {
    return weatherService.newFeature(param);
}
```

2. 在 `WeatherService` 中实现业务逻辑：

```java
public Object newFeature(String param) {
    // 实现业务逻辑
    return result;
}
```

3. 重启应用，新工具将自动注册。

### 自定义配置

可以通过修改 `application.yml` 来自定义配置：

```yaml
spring:
  ai:
    mcp:
      server:
        name: my-custom-server
        instructions: "自定义的 MCP Server 描述"
        sse-endpoint: /custom-sse
```

## 版本信息

- Spring Boot: 3.3.7
- Spring AI: 1.0.0
- Galaxy Boot: 1.0.0
- Java: 21
