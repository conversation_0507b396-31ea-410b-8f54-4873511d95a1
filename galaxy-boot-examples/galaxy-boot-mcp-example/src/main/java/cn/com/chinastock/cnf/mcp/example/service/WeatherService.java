package cn.com.chinastock.cnf.mcp.example.service;

import cn.com.chinastock.cnf.mcp.example.model.WeatherForecast;
import cn.com.chinastock.cnf.mcp.example.model.WeatherInfo;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 天气服务
 * <p>
 * 模拟天气数据服务，提供天气信息和预报功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WeatherService {

    private final Random random = new Random();

    // 模拟城市天气数据
    private final Map<String, String[]> cityWeatherData = Map.of(
        "北京", new String[]{"晴朗", "多云", "小雨", "雾霾"},
        "上海", new String[]{"多云", "小雨", "晴朗", "阴天"},
        "广州", new String[]{"晴朗", "多云", "雷阵雨", "小雨"},
        "深圳", new String[]{"晴朗", "多云", "雷阵雨", "小雨"},
        "杭州", new String[]{"多云", "小雨", "晴朗", "阴天"},
        "成都", new String[]{"多云", "小雨", "阴天", "雾"},
        "重庆", new String[]{"多云", "小雨", "阴天", "雾"},
        "西安", new String[]{"晴朗", "多云", "小雨", "沙尘"},
        "南京", new String[]{"多云", "小雨", "晴朗", "阴天"},
        "武汉", new String[]{"多云", "小雨", "晴朗", "阴天"}
    );

    /**
     * 获取指定城市的当前天气信息
     *
     * @param city 城市名称
     * @return 天气信息
     */
    public WeatherInfo getWeather(String city) {
        if (city == null || city.trim().isEmpty()) {
            throw new IllegalArgumentException("城市名称不能为空");
        }

        String[] weatherOptions = cityWeatherData.getOrDefault(city, 
            new String[]{"晴朗", "多云", "小雨", "阴天"});
        
        String description = weatherOptions[random.nextInt(weatherOptions.length)];
        
        // 根据天气描述生成相应的温度范围
        double baseTemp = getBaseTemperature(description);
        double temperature = baseTemp + (random.nextDouble() - 0.5) * 10;
        
        int humidity = 30 + random.nextInt(50); // 30-80%
        double windSpeed = 1 + random.nextDouble() * 15; // 1-16 m/s
        double pressure = 1000 + random.nextDouble() * 50; // 1000-1050 hPa

        return new WeatherInfo(city, 
            Math.round(temperature * 10.0) / 10.0, 
            humidity, 
            description, 
            Math.round(windSpeed * 10.0) / 10.0, 
            Math.round(pressure * 10.0) / 10.0);
    }

    /**
     * 获取指定城市的天气预报
     *
     * @param city 城市名称
     * @param days 预报天数
     * @return 天气预报列表
     */
    public List<WeatherForecast> getForecast(String city, int days) {
        if (city == null || city.trim().isEmpty()) {
            throw new IllegalArgumentException("城市名称不能为空");
        }
        
        if (days <= 0 || days > 7) {
            throw new IllegalArgumentException("预报天数必须在1-7天之间");
        }

        List<WeatherForecast> forecasts = new ArrayList<>();
        String[] weatherOptions = cityWeatherData.getOrDefault(city, 
            new String[]{"晴朗", "多云", "小雨", "阴天"});

        for (int i = 0; i < days; i++) {
            LocalDate date = LocalDate.now().plusDays(i);
            String description = weatherOptions[random.nextInt(weatherOptions.length)];
            
            double baseTemp = getBaseTemperature(description);
            double maxTemp = baseTemp + random.nextDouble() * 5;
            double minTemp = baseTemp - random.nextDouble() * 5;
            
            int humidity = 30 + random.nextInt(50);
            double windSpeed = 1 + random.nextDouble() * 15;
            int precipitationProb = getPrecipitationProbability(description);

            forecasts.add(new WeatherForecast(
                date,
                city,
                Math.round(maxTemp * 10.0) / 10.0,
                Math.round(minTemp * 10.0) / 10.0,
                humidity,
                description,
                Math.round(windSpeed * 10.0) / 10.0,
                precipitationProb
            ));
        }

        return forecasts;
    }

    /**
     * 获取支持的城市列表
     *
     * @return 城市列表
     */
    public List<String> getSupportedCities() {
        return new ArrayList<>(cityWeatherData.keySet());
    }

    /**
     * 根据天气描述获取基础温度
     *
     * @param description 天气描述
     * @return 基础温度值
     */
    private double getBaseTemperature(String description) {
        return switch (description) {
            case "晴朗" -> 25.0;
            case "多云" -> 22.0;
            case "小雨", "雷阵雨" -> 18.0;
            case "阴天" -> 20.0;
            case "雾霾", "雾", "沙尘" -> 15.0;
            default -> 20.0;
        };
    }

    /**
     * 根据天气描述获取降水概率
     *
     * @param description 天气描述
     * @return 降水概率百分比
     */
    private int getPrecipitationProbability(String description) {
        return switch (description) {
            case "晴朗" -> 5 + random.nextInt(10);
            case "多云" -> 20 + random.nextInt(20);
            case "小雨" -> 70 + random.nextInt(20);
            case "雷阵雨" -> 80 + random.nextInt(15);
            case "阴天" -> 30 + random.nextInt(30);
            default -> 20 + random.nextInt(30);
        };
    }
}
