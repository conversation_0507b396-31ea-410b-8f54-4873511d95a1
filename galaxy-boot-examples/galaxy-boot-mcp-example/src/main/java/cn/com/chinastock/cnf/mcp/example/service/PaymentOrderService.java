package cn.com.chinastock.cnf.mcp.example.service;

import cn.com.chinastock.cnf.mcp.example.model.CreateOrderRequest;
import cn.com.chinastock.cnf.mcp.example.model.OrderStatus;
import cn.com.chinastock.cnf.mcp.example.model.PaymentOrder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 支付订单服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class PaymentOrderService {

    private final Map<String, PaymentOrder> orderMap = new ConcurrentHashMap<>();
    private final AtomicLong orderIdCounter = new AtomicLong(6);
    private final AtomicLong orderNoCounter = new AtomicLong(1000006);

    /**
     * 初始化一些示例订单数据
     */
    @PostConstruct
    public void initSampleData() {
        // 创建一些示例订单
        createSampleOrder("1", "1000001", new BigDecimal("99.99"), "CNY", 
                OrderStatus.PAID, "iPhone 15 Pro 手机", "CUST001", "张三", "支付宝");
        
        createSampleOrder("2", "1000002", new BigDecimal("299.99"), "CNY", 
                OrderStatus.PENDING_PAYMENT, "MacBook Air 笔记本电脑", "CUST002", "李四", "微信支付");
        
        createSampleOrder("3", "1000003", new BigDecimal("199.99"), "CNY", 
                OrderStatus.PENDING_CONFIRMATION, "AirPods Pro 耳机", "CUST003", "王五", "银行卡");
        
        createSampleOrder("4", "1000004", new BigDecimal("599.99"), "CNY", 
                OrderStatus.CANCELLED, "iPad Air 平板电脑", "CUST004", "赵六", "支付宝");
        
        createSampleOrder("5", "1000005", new BigDecimal("899.99"), "CNY", 
                OrderStatus.PAYMENT_FAILED, "Apple Watch Series 9", "CUST005", "钱七", "微信支付");
    }

    private void createSampleOrder(String id, String orderNo, BigDecimal amount, String currency,
                                 OrderStatus status, String description, String customerId,
                                 String customerName, String paymentMethod) {
        PaymentOrder order = new PaymentOrder(id, orderNo, amount, currency, status, description,
                customerId, customerName, paymentMethod);
        
        if (status == OrderStatus.PAID) {
            order.setPaidTime(LocalDateTime.now().minusHours(2));
        }
        
        orderMap.put(id, order);
    }

    /**
     * 创建新订单
     *
     * @param request 创建订单请求
     * @return 创建的订单
     */
    public PaymentOrder createOrder(CreateOrderRequest request) {
        String id = String.valueOf(orderIdCounter.getAndIncrement());
        String orderNo = String.valueOf(orderNoCounter.getAndIncrement());
        
        PaymentOrder order = new PaymentOrder(id, orderNo, request.getAmount(), 
                request.getCurrency(), OrderStatus.PENDING_CONFIRMATION, 
                request.getDescription(), request.getCustomerId(), 
                request.getCustomerName(), request.getPaymentMethod());
        
        orderMap.put(id, order);
        return order;
    }

    /**
     * 确认订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单
     */
    public PaymentOrder confirmOrder(String orderId) {
        PaymentOrder order = orderMap.get(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在: " + orderId);
        }
        
        if (order.getStatus() != OrderStatus.PENDING_CONFIRMATION) {
            throw new IllegalStateException("订单状态不允许确认: " + order.getStatus());
        }
        
        order.setStatus(OrderStatus.PENDING_PAYMENT);
        return order;
    }

    /**
     * 支付订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单
     */
    public PaymentOrder payOrder(String orderId) {
        PaymentOrder order = orderMap.get(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在: " + orderId);
        }
        
        if (order.getStatus() != OrderStatus.PENDING_PAYMENT) {
            throw new IllegalStateException("订单状态不允许支付: " + order.getStatus());
        }
        
        // 模拟支付过程
        order.setStatus(OrderStatus.PAYING);
        
        // 模拟支付成功
        order.setStatus(OrderStatus.PAID);
        order.setPaidTime(LocalDateTime.now());
        
        return order;
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单
     */
    public PaymentOrder cancelOrder(String orderId) {
        PaymentOrder order = orderMap.get(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在: " + orderId);
        }
        
        if (order.getStatus() == OrderStatus.PAID) {
            throw new IllegalStateException("已支付的订单不能取消");
        }
        
        order.setStatus(OrderStatus.CANCELLED);
        return order;
    }

    /**
     * 根据ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    public PaymentOrder getOrderById(String orderId) {
        PaymentOrder order = orderMap.get(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在: " + orderId);
        }
        return order;
    }

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    public PaymentOrder getOrderByOrderNo(String orderNo) {
        return orderMap.values().stream()
                .filter(order -> order.getOrderNo().equals(orderNo))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("订单不存在: " + orderNo));
    }

    /**
     * 查询所有订单
     *
     * @return 所有订单列表
     */
    public List<PaymentOrder> getAllOrders() {
        return new ArrayList<>(orderMap.values());
    }

    /**
     * 根据状态查询订单
     *
     * @param status 订单状态
     * @return 订单列表
     */
    public List<PaymentOrder> getOrdersByStatus(OrderStatus status) {
        return orderMap.values().stream()
                .filter(order -> order.getStatus() == status)
                .toList();
    }

    /**
     * 根据客户ID查询订单
     *
     * @param customerId 客户ID
     * @return 订单列表
     */
    public List<PaymentOrder> getOrdersByCustomerId(String customerId) {
        return orderMap.values().stream()
                .filter(order -> order.getCustomerId().equals(customerId))
                .toList();
    }

    /**
     * 删除订单（仅用于测试）
     *
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    public boolean deleteOrder(String orderId) {
        PaymentOrder order = orderMap.remove(orderId);
        return order != null;
    }

    /**
     * 获取订单统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        long totalOrders = orderMap.size();
        long paidOrders = orderMap.values().stream()
                .filter(order -> order.getStatus() == OrderStatus.PAID)
                .count();
        long pendingOrders = orderMap.values().stream()
                .filter(order -> order.getStatus() == OrderStatus.PENDING_PAYMENT)
                .count();
        long cancelledOrders = orderMap.values().stream()
                .filter(order -> order.getStatus() == OrderStatus.CANCELLED)
                .count();
        
        BigDecimal totalAmount = orderMap.values().stream()
                .filter(order -> order.getStatus() == OrderStatus.PAID)
                .map(PaymentOrder::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        statistics.put("totalOrders", totalOrders);
        statistics.put("paidOrders", paidOrders);
        statistics.put("pendingOrders", pendingOrders);
        statistics.put("cancelledOrders", cancelledOrders);
        statistics.put("totalAmount", totalAmount);
        
        return statistics;
    }
} 