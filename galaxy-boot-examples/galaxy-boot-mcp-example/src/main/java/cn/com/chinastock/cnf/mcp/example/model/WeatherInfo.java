package cn.com.chinastock.cnf.mcp.example.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 天气信息模型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class WeatherInfo {

    @JsonProperty("city")
    private String city;

    @JsonProperty("temperature")
    private double temperature;

    @JsonProperty("humidity")
    private int humidity;

    @JsonProperty("description")
    private String description;

    @JsonProperty("wind_speed")
    private double windSpeed;

    @JsonProperty("pressure")
    private double pressure;

    public WeatherInfo() {
    }

    public WeatherInfo(String city, double temperature, int humidity, String description, double windSpeed, double pressure) {
        this.city = city;
        this.temperature = temperature;
        this.humidity = humidity;
        this.description = description;
        this.windSpeed = windSpeed;
        this.pressure = pressure;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public double getTemperature() {
        return temperature;
    }

    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    public int getHumidity() {
        return humidity;
    }

    public void setHumidity(int humidity) {
        this.humidity = humidity;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public double getPressure() {
        return pressure;
    }

    public void setPressure(double pressure) {
        this.pressure = pressure;
    }

    @Override
    public String toString() {
        return "WeatherInfo{" +
                "city='" + city + '\'' +
                ", temperature=" + temperature +
                ", humidity=" + humidity +
                ", description='" + description + '\'' +
                ", windSpeed=" + windSpeed +
                ", pressure=" + pressure +
                '}';
    }
}
