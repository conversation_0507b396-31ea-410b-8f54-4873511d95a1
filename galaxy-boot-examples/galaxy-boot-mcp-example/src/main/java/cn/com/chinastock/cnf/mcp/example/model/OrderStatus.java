package cn.com.chinastock.cnf.mcp.example.model;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderStatus {
    
    /**
     * 待确认
     */
    PENDING_CONFIRMATION("PENDING_CONFIRMATION", "待确认"),
    
    /**
     * 待支付
     */
    PENDING_PAYMENT("PENDING_PAYMENT", "待支付"),
    
    /**
     * 支付中
     */
    PAYING("PAYING", "支付中"),
    
    /**
     * 已支付
     */
    PAID("PAID", "已支付"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 支付失败
     */
    PAYMENT_FAILED("PAYMENT_FAILED", "支付失败"),
    
    /**
     * 已退款
     */
    REFUNDED("REFUNDED", "已退款");

    private final String code;
    private final String description;

    OrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code;
    }
} 