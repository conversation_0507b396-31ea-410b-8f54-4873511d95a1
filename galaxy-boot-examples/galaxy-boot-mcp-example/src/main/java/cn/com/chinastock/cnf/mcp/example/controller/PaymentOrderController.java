package cn.com.chinastock.cnf.mcp.example.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.mcp.example.model.*;
import cn.com.chinastock.cnf.mcp.example.service.PaymentOrderService;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 支付订单控制器
 * <p>
 * 演示支付订单的完整生命周期管理，包括创建、确认、支付、取消、查询等操作
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/payment-orders")
public class PaymentOrderController {

    @Autowired
    private PaymentOrderService paymentOrderService;

    /**
     * 创建支付订单
     *
     * @param request 创建订单请求
     * @return 创建的订单信息
     */
    @PostMapping
    @Tool(
            name = "createPaymentOrder",
            description = "创建新的支付订单，包含订单金额、客户信息、支付方式等"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> createOrder(@RequestBody CreateOrderRequest request) {
        try {
            PaymentOrder order = paymentOrderService.createOrder(request);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单创建成功"), order));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", "创建订单失败: " + e.getMessage()), null));
        }
    }

    /**
     * 确认订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单信息
     */
    @PostMapping("/{orderId}/confirm")
    @Tool(
            name = "confirmPaymentOrder",
            description = "确认支付订单，将订单状态从待确认改为待支付"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> confirmOrder(@PathVariable String orderId) {
        try {
            PaymentOrder order = paymentOrderService.confirmOrder(orderId);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单确认成功"), order));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        }
    }

    /**
     * 支付订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单信息
     */
    @PostMapping("/{orderId}/pay")
    @Tool(
            name = "payPaymentOrder",
            description = "支付订单，将订单状态从待支付改为已支付"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> payOrder(@PathVariable String orderId) {
        try {
            PaymentOrder order = paymentOrderService.payOrder(orderId);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单支付成功"), order));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        }
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @return 更新后的订单信息
     */
    @PostMapping("/{orderId}/cancel")
    @Tool(
            name = "cancelPaymentOrder",
            description = "取消支付订单，将订单状态改为已取消"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> cancelOrder(@PathVariable String orderId) {
        try {
            PaymentOrder order = paymentOrderService.cancelOrder(orderId);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单取消成功"), order));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        }
    }

    /**
     * 根据ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    @GetMapping("/{orderId}")
    @Tool(
            name = "getPaymentOrderById",
            description = "根据订单ID查询订单详细信息"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> getOrderById(@PathVariable String orderId) {
        try {
            PaymentOrder order = paymentOrderService.getOrderById(orderId);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), order));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        }
    }

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    @GetMapping("/by-order-no/{orderNo}")
    @Tool(
            name = "getPaymentOrderByOrderNo",
            description = "根据订单号查询订单详细信息"
    )
    public ResponseEntity<BaseResponse<PaymentOrder>> getOrderByOrderNo(@PathVariable String orderNo) {
        try {
            PaymentOrder order = paymentOrderService.getOrderByOrderNo(orderNo);
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), order));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", e.getMessage()), null));
        }
    }

    /**
     * 查询所有订单
     *
     * @return 所有订单列表
     */
    @GetMapping
    @Tool(
            name = "getAllPaymentOrders",
            description = "查询所有支付订单列表"
    )
    public ResponseEntity<BaseResponse<List<PaymentOrder>>> getAllOrders() {
        List<PaymentOrder> orders = paymentOrderService.getAllOrders();
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), orders));
    }

    /**
     * 根据状态查询订单
     *
     * @param status 订单状态
     * @return 订单列表
     */
    @GetMapping("/by-status")
    @Tool(
            name = "getPaymentOrdersByStatus",
            description = "根据订单状态查询订单列表"
    )
    public ResponseEntity<BaseResponse<List<PaymentOrder>>> getOrdersByStatus(@RequestParam OrderStatus status) {
        List<PaymentOrder> orders = paymentOrderService.getOrdersByStatus(status);
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), orders));
    }

    /**
     * 根据客户ID查询订单
     *
     * @param customerId 客户ID
     * @return 订单列表
     */
    @GetMapping("/by-customer")
    @Tool(
            name = "getPaymentOrdersByCustomerId",
            description = "根据客户ID查询该客户的所有订单"
    )
    public ResponseEntity<BaseResponse<List<PaymentOrder>>> getOrdersByCustomerId(@RequestParam String customerId) {
        List<PaymentOrder> orders = paymentOrderService.getOrdersByCustomerId(customerId);
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), orders));
    }

    /**
     * 获取订单统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @Tool(
            name = "getPaymentOrderStatistics",
            description = "获取支付订单的统计信息，包括总订单数、已支付订单数、待支付订单数等"
    )
    public ResponseEntity<BaseResponse<Map<String, Object>>> getOrderStatistics() {
        Map<String, Object> statistics = paymentOrderService.getOrderStatistics();
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), statistics));
    }

    /**
     * 删除订单（仅用于测试）
     *
     * @param orderId 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/{orderId}")
    @Tool(
            name = "deletePaymentOrder",
            description = "删除指定的支付订单（仅用于测试）"
    )
    public ResponseEntity<BaseResponse<Boolean>> deleteOrder(@PathVariable String orderId) {
        boolean deleted = paymentOrderService.deleteOrder(orderId);
        if (deleted) {
            return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "订单删除成功"), true));
        } else {
            return ResponseEntity.badRequest()
                    .body(new BaseResponse<>(new Meta(false, "400", "订单不存在或删除失败"), null));
        }
    }

    /**
     * 获取所有订单状态
     *
     * @return 订单状态列表
     */
    @GetMapping("/statuses")
    @Tool(
            name = "getPaymentOrderStatuses",
            description = "获取所有可用的订单状态"
    )
    public ResponseEntity<BaseResponse<OrderStatus[]>> getOrderStatuses() {
        OrderStatus[] statuses = OrderStatus.values();
        return ResponseEntity.ok(new BaseResponse<>(new Meta(true, "0000", "操作成功"), statuses));
    }
} 