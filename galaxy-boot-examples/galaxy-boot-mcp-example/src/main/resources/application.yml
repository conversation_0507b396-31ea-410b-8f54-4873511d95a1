server:
  port: 8089

spring:
  application:
    name: galaxy-mcp-example
  
  # Spring AI MCP Server 配置
  # 所有 MCP Server 相关配置都使用 Spring AI 原生配置
  ai:
    mcp:
      server:
        enabled: true
        name: galaxy-weather-server
        version: 1.0.0
        instructions: "Galaxy Weather MCP Server - 提供天气信息查询、预报、比较和建议等功能"
        type: SYNC
        sse-endpoint: /sse
        sse-message-endpoint: /mcp/message
        base-url: ""
        request-timeout: 30
        capabilities:
          tool: true
          resource: false
          prompt: false
          completion: false
        tool-change-notification: true
        resource-change-notification: false
        prompt-change-notification: false

# Galaxy MCP 配置 - 只保留必要的启用开关
galaxy:
  mcp:
    enabled: true

  log:
    exception-pretty-print: true
    request-response:
      enabled: true
      request-headers: true
      response-headers: true
    performance:
      enabled: true
    # SSE响应特殊处理配置
    sse:
      enabled: true
      endpoint-patterns: /sse,/mcp/sse,/mcp/message

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,mcp
      base-path: /actuator
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com:
      ctrip:
        framework:
          apollo: ERROR
