package cn.com.chinastock.cnf;

import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

public class WebfluxTongWebConcurrencyTest {
    public static void main(String[] args) throws InterruptedException {
        String url = "http://localhost:8080/api/users"; // 目标接口
        int threadCount = 500; // 并发线程数
        int requestPerThread = 100; // 每线程请求数

        WebClient webClient = WebClient.create();
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger success = new AtomicInteger();
        AtomicInteger fail = new AtomicInteger();

        StopWatch stopwatch = new StopWatch();
        stopwatch.start();
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < requestPerThread; j++) {
                    try {
                        Mono<String> resp = webClient.get().uri(url)
                                .retrieve()
                                .bodyToMono(String.class);
                        resp.block(); // 阻塞等待响应
                        success.incrementAndGet();
                    } catch (Exception e) {
                        fail.incrementAndGet();
                    }
                }
                latch.countDown();
            }).start();
        }

        latch.await();
        stopwatch.stop();
        System.out.println("耗时:" + stopwatch.prettyPrint());
        System.out.println("成功请求数: " + success.get());
        System.out.println("失败请求数: " + fail.get());
    }
}