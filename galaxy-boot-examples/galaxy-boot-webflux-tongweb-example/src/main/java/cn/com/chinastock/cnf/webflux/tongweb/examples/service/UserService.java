package cn.com.chinastock.cnf.webflux.tongweb.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.tongweb.examples.model.User;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户服务类
 * 
 * <p>简化的响应式用户服务</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
@Service
public class UserService {
    
    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UserService.class);
    
    // 模拟数据存储
    private final ConcurrentHashMap<Long, User> userStorage = new ConcurrentHashMap<>();

    /**
     * 构造函数，初始化测试数据
     */
    public UserService() {
        initTestData();
    }
    
    /**
     * 获取所有用户
     * 
     * @return 用户流
     */
    public Flux<User> getAllUsers() {
        logger.info("从存储中获取所有用户");
        return Flux.fromIterable(userStorage.values())
                .delayElements(Duration.ofMillis(10)) // 模拟数据库延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> logger.debug("开始获取用户列表"))
                .doOnComplete(() -> logger.debug("用户列表获取完成"));
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        logger.info("初始化测试数据");
        
        User user1 = new User("admin", "admin123", "<EMAIL>");
        user1.setId(1L);
        user1.setRealName("管理员");
        user1.setPhoneNumber("13800138000");
        user1.setStatus(User.UserStatus.ACTIVE);
        
        User user2 = new User("john", "password123", "<EMAIL>");
        user2.setId(2L);
        user2.setRealName("约翰");
        user2.setPhoneNumber("13900139000");
        user2.setStatus(User.UserStatus.ACTIVE);
        
        userStorage.put(1L, user1);
        userStorage.put(2L, user2);
        
        logger.info("测试数据初始化完成，共 {} 个用户", userStorage.size());
    }
} 