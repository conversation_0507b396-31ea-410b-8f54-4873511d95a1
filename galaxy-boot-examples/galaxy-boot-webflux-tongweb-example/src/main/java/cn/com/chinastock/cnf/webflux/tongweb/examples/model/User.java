package cn.com.chinastock.cnf.webflux.tongweb.examples.model;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <p>演示敏感字段掩码功能，使用 @MaskedField 注解保护敏感数据</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
public class User {
    
    private Long id;
    private String username;
    
    /**
     * 密码字段使用 @MaskedField 注解，在日志中会被掩码处理
     */
    @MaskedField
    private String password;
    
    private String email;
    
    /**
     * 手机号码也是敏感信息，需要掩码处理
     */
    @MaskedField
    private String phoneNumber;
    
    private String realName;
    private UserStatus status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    public User() {
    }
    
    /**
     * 构造函数
     * 
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱地址
     */
    public User(String username, String password, String email) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.status = UserStatus.ACTIVE;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     * 
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * 获取密码
     * 
     * @return 密码
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * 设置密码
     * 
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password;
    }
    
    /**
     * 获取邮箱地址
     * 
     * @return 邮箱地址
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * 设置邮箱地址
     * 
     * @param email 邮箱地址
     */
    public void setEmail(String email) {
        this.email = email;
    }
    
    /**
     * 获取手机号码
     * 
     * @return 手机号码
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }
    
    /**
     * 设置手机号码
     * 
     * @param phoneNumber 手机号码
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    
    /**
     * 获取真实姓名
     * 
     * @return 真实姓名
     */
    public String getRealName() {
        return realName;
    }
    
    /**
     * 设置真实姓名
     * 
     * @param realName 真实姓名
     */
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    /**
     * 获取用户状态
     * 
     * @return 用户状态
     */
    public UserStatus getStatus() {
        return status;
    }
    
    /**
     * 设置用户状态
     * 
     * @param status 用户状态
     */
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    /**
     * 获取创建时间
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    /**
     * 设置创建时间
     * 
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 获取更新时间
     * 
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    /**
     * 设置更新时间
     * 
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    /**
     * 返回对象的字符串表示
     * 
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
    
    public enum UserStatus {
        ACTIVE, 
        INACTIVE, 
        SUSPENDED
    }
} 