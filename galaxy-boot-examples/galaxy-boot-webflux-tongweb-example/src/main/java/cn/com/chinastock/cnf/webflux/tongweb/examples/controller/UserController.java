package cn.com.chinastock.cnf.webflux.tongweb.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.tongweb.examples.model.User;
import cn.com.chinastock.cnf.webflux.tongweb.examples.service.UserService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 用户管理 WebFlux 控制器
 * 
 * <p>简化的响应式用户管理接口</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UserController.class);
    
    private final UserService userService;
    
    public UserController(UserService userService) {
        this.userService = userService;
    }
    
    /**
     * 获取所有用户（流式返回）
     * 
     * @return 用户列表的Flux
     */
    @GetMapping
    public Flux<User> getAllUsers() {
        logger.info("获取所有用户列表");
        return userService.getAllUsers()
                .doOnNext(user -> logger.debug("返回用户: {}", user.getUsername()));
    }
}