<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.chinastock.cnf.examples.data.mapper.BlogMapper">

    <select id="findByTitleContaining" resultType="cn.com.chinastock.cnf.examples.data.entity.Blog">
        SELECT * FROM blog WHERE title LIKE CONCAT('%',#{title},'%')
    </select>

    <select id="findByAuthor" resultType="cn.com.chinastock.cnf.examples.data.entity.Blog">
        SELECT * FROM blog WHERE author = #{author}
    </select>

    <select id="findByAuthorWithPaging" resultType="cn.com.chinastock.cnf.examples.data.entity.Blog">
        SELECT * FROM blog WHERE author = #{author} LIMIT #{offset}, #{pageSize}
    </select>

    <select id="findByAuthorAndTitleKeyword" resultType="cn.com.chinastock.cnf.examples.data.entity.Blog">
        SELECT * FROM blog WHERE author = #{author} AND title LIKE CONCAT('%',#{keyword},'%')
    </select>

    <select id="countBlogsGroupByAuthor" resultType="long">
        SELECT COUNT(*) FROM blog GROUP BY author
    </select>

    <update id="updateTitlesByAuthor">
        UPDATE blog SET title = #{newTitle} WHERE author = #{author}
    </update>

    <delete id="deleteByAuthor">
        DELETE FROM blog WHERE author = #{author}
    </delete>

    <select id="countByAuthor" resultType="long">
        SELECT COUNT(*) FROM blog WHERE author = #{author}
    </select>

    <insert id="save" parameterType="cn.com.chinastock.cnf.examples.data.entity.Blog">
        INSERT INTO blog (title, author, content) 
        VALUES (#{title}, #{author}, #{content})
    </insert>
</mapper>
