app:
  id: datasource
apollo:
  meta: http://localhost:8080
server:
  port: 8088

spring:
  application:
    name: galaxy-boot-database-example-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: sun
    password: f5GGWuGwy,!f.s#5+n5

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region
  prometheus:
    metrics:
      export:
        enabled: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  config-location: classpath:mybatis-config.xml

galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
    dynamic-refresh-username-password: true # 动态修改数据库密码（未来支持）

  metrics:
    datasource:
      prometheus:
        enable: false
      logging: # 日志监控，通过日志输出
        enable: true
        interval: 300

  system:
    code: DXP

  log:
    request-response:
      enabled: true
      request-headers: false
    performance:
      enabled: true
    default-category: BUSINESS_LOG
    exception-pretty-print: true
