package cn.com.chinastock.cnf.examples.data.mapper;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BlogMapper {
    List<Blog> findByTitleContaining(String title);

    List<Blog> findByAuthor(String author);

    List<Blog> findByAuthorWithPaging(@Param("author") String author,
                                      @Param("offset") int offset,
                                      @Param("pageSize") int pageSize);

    List<Blog> findByAuthorAndTitleKeyword(@Param("author") String author, @Param("keyword") String keyword);

    List<Long> countBlogsGroupByAuthor();

    int updateTitlesByAuthor(@Param("author") String author, @Param("newTitle") String newTitle);

    int deleteByAuthor(@Param("author") String author);

    long countByAuthor(String author);

    int save(Blog blog);
}