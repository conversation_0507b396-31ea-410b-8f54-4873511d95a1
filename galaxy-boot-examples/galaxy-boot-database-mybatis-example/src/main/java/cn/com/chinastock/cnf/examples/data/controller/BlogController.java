package cn.com.chinastock.cnf.examples.data.controller;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import cn.com.chinastock.cnf.examples.data.mapper.BlogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/blog")
public class BlogController {
    @Autowired
    private BlogMapper blogMapper;

    @PostMapping("/")
    public Blog autoCreate() {
        Blog blog = new Blog();
        blog.setTitle("title-" + System.currentTimeMillis());
        blog.setContent("content-" + System.currentTimeMillis());
        blog.setAuthor("author-" + System.currentTimeMillis());
        blogMapper.save(blog);
        return blog;
    }
}
