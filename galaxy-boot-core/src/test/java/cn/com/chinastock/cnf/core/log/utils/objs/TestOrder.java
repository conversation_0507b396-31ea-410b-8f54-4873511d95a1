package cn.com.chinastock.cnf.core.log.utils.objs;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;

public class TestOrder {
    private Long orderId;
    private TestUser user;
    @MaskedField
    private String cardNumber;

    // getters and setters
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public TestUser getUser() {
        return user;
    }

    public void setUser(TestUser user) {
        this.user = user;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }
}
