package cn.com.chinastock.cnf.core.log.utils;

import cn.com.chinastock.cnf.core.log.utils.objs.TestDateHolder;
import cn.com.chinastock.cnf.core.log.utils.objs.TestEnum;
import cn.com.chinastock.cnf.core.log.utils.objs.TestOrder;
import cn.com.chinastock.cnf.core.log.utils.objs.TestUser;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.*;

class HttpLogUtilsTest {

    @Test
    void testMaskSensitiveFields_SimpleTypes() {
        // 测试基本类型和简单对象
        assertNull(HttpLogUtils.maskSensitiveFields(null));
        assertEquals("\"test\"", HttpLogUtils.maskSensitiveFields("test"));
        assertEquals("123", HttpLogUtils.maskSensitiveFields(123));
        assertEquals("123.45", HttpLogUtils.maskSensitiveFields(123.45));
        assertEquals("true", HttpLogUtils.maskSensitiveFields(true));
        assertEquals("true", HttpLogUtils.maskSensitiveFields(Boolean.TRUE));
        assertEquals("\"Enum1\"", HttpLogUtils.maskSensitiveFields(TestEnum.Enum1));
    }

    @Test
    void testMaskSensitiveFields_DateTypes() {
        // 测试各种日期时间类型
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        LocalDateTime localDateTime = LocalDateTime.now();
        ZonedDateTime zonedDateTime = ZonedDateTime.now();
        Instant instant = Instant.now();

        // 测试单个日期类型
        String result = HttpLogUtils.maskSensitiveFields(date);
        assertNotNull(result);
        
        // 测试包含日期的对象
        TestDateHolder dateHolder = new TestDateHolder();
        dateHolder.setId(1L);
        dateHolder.setDate(date);
        dateHolder.setCalendar(calendar);
        dateHolder.setLocalDateTime(localDateTime);
        dateHolder.setZonedDateTime(zonedDateTime);
        dateHolder.setInstant(instant);

        result = HttpLogUtils.maskSensitiveFields(dateHolder);
        assertNotNull(result);
        assertTrue(result.contains("\"id\":1"));
        assertTrue(result.contains("\"date\":"));
        assertTrue(result.contains("\"calendar\":"));
        assertTrue(result.contains("\"localDateTime\":"));
        assertTrue(result.contains("\"zonedDateTime\":"));
        assertTrue(result.contains("\"instant\":"));
    }

    @Test
    void testMaskSensitiveFields_Collections() {
        // 测试集合类型
        List<String> list = asList("test1", "test2");
        String result = HttpLogUtils.maskSensitiveFields(list);
        assertNotNull(result);
        assertTrue(result.contains("test1"));
        assertTrue(result.contains("test2"));

        // 测试Map
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        map.put("key2", "value2");
        result = HttpLogUtils.maskSensitiveFields(map);
        assertNotNull(result);
        assertTrue(result.contains("\"key1\":\"value1\""));
        assertTrue(result.contains("\"key2\":\"value2\""));
    }

    @Test
    void testMaskSensitiveFields_Array() {
        // 测试数组
        String[] array = {"test1", "test2"};

        String result = HttpLogUtils.maskSensitiveFields(array);

        assertNotNull(result);
        assertTrue(result.contains("test1"));
        assertTrue(result.contains("test2"));
    }

    @Test
    void testMaskSensitiveFields_MaskedFields() {
        // 测试带有@MaskedField注解的对象
        TestUser user = new TestUser();
        user.setId(1L);
        user.setUsername("testUser");
        user.setPassword("secretPassword");
        user.setEmail("<EMAIL>");

        String result = HttpLogUtils.maskSensitiveFields(user);

        assertNotNull(result);
        assertTrue(result.contains("\"id\":1"));
        assertTrue(result.contains("\"username\":\"testUser\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));
    }

    @Test
    void testMaskSensitiveFields_NestedObjects() {
        // 测试嵌套对象
        TestUser user = new TestUser();
        user.setId(1L);
        user.setUsername("testUser");
        user.setPassword("secretPassword");
        user.setEmail("<EMAIL>");

        TestOrder order = new TestOrder();
        order.setOrderId(100L);
        order.setUser(user);
        order.setCardNumber("1234-5678-9012-3456");

        String result = HttpLogUtils.maskSensitiveFields(order);

        assertNotNull(result);
        assertTrue(result.contains("\"orderId\":100"));
        assertTrue(result.contains("\"cardNumber\":\"***\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));
    }

    @Test
    void testMaskSensitiveFields_CircularReference() {
        // 测试循环引用
        TestUser user = new TestUser();
        user.setId(1L);
        TestOrder order = new TestOrder();
        order.setOrderId(100L);
        
        user.setLastOrder(order);
        order.setUser(user);

        String result = HttpLogUtils.maskSensitiveFields(user);
        assertNotNull(result);
        assertTrue(result.contains("\"[CIRCULAR REFERENCE]\""));
    }

    @Test
    void testMaskSensitiveFields_Numbers() {
        // 测试各种数值类型
        assertEquals("123", HttpLogUtils.maskSensitiveFields(123));
        assertEquals("123", HttpLogUtils.maskSensitiveFields(123L));
        assertEquals("123.45", HttpLogUtils.maskSensitiveFields(123.45));
        assertEquals("123.45", HttpLogUtils.maskSensitiveFields(123.45f));
        assertEquals("123", HttpLogUtils.maskSensitiveFields(Integer.valueOf(123)));
        assertEquals("123", HttpLogUtils.maskSensitiveFields(Long.valueOf(123)));
        assertEquals("123.45", HttpLogUtils.maskSensitiveFields(Double.valueOf(123.45)));
    }

    @Test
    void testMaskSensitiveFields_ObjectInCollections() {
        // 创建测试用户
        TestUser user1 = new TestUser();
        user1.setId(1L);
        user1.setUsername("user1");
        user1.setPassword("password1");
        user1.setEmail("<EMAIL>");

        TestUser user2 = new TestUser();
        user2.setId(2L);
        user2.setUsername("user2");
        user2.setPassword("password2");
        user2.setEmail("<EMAIL>");

        // 测试List中的对象
        List<TestUser> userList = asList(user1, user2);
        String result = HttpLogUtils.maskSensitiveFields(userList);

        assertNotNull(result);
        assertTrue(result.contains("\"username\":\"user1\""));
        assertTrue(result.contains("\"username\":\"user2\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));
        assertFalse(result.contains("password1"));
        assertFalse(result.contains("password2"));
        assertFalse(result.contains("<EMAIL>"));
        assertFalse(result.contains("<EMAIL>"));

        // 测试Set中的对象
        Set<TestUser> userSet = new HashSet<>(userList);
        result = HttpLogUtils.maskSensitiveFields(userSet);

        assertNotNull(result);
        assertTrue(result.contains("\"username\":\"user1\""));
        assertTrue(result.contains("\"username\":\"user2\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));

        // 测试Map中的对象
        Map<String, TestUser> userMap = new HashMap<>();
        userMap.put("user1", user1);
        userMap.put("user2", user2);
        result = HttpLogUtils.maskSensitiveFields(userMap);

        assertNotNull(result);
        assertTrue(result.contains("\"user1\":{"));
        assertTrue(result.contains("\"user2\":{"));
        assertTrue(result.contains("\"username\":\"user1\""));
        assertTrue(result.contains("\"username\":\"user2\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));
        
        // 测试嵌套集合
        List<List<TestUser>> nestedList = asList(
                List.of(user1),
                List.of(user2)
        );
        result = HttpLogUtils.maskSensitiveFields(nestedList);

        assertNotNull(result);
        assertTrue(result.contains("\"username\":\"user1\""));
        assertTrue(result.contains("\"username\":\"user2\""));
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"email\":\"***\""));
        assertFalse(result.contains("password1"));
        assertFalse(result.contains("password2"));
    }

} 