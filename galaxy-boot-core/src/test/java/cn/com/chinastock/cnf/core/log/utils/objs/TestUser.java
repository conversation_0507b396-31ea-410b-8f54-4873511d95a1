package cn.com.chinastock.cnf.core.log.utils.objs;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;

public class TestUser {
    private Long id;
    private String username;
    @MaskedField
    private String password;
    @MaskedField
    private String email;

    private TestOrder lastOrder;

    // getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public TestOrder getLastOrder() {
        return lastOrder;
    }

    public void setLastOrder(TestOrder lastOrder) {
        this.lastOrder = lastOrder;
    }

}