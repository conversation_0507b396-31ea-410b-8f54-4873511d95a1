package cn.com.chinastock.cnf.core.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.ServiceLoader;

public final class GalaxyLoggerFactory {

    private static final IGalaxyLoggerProvider PROVIDER;

    private static final Logger logger = LoggerFactory.getLogger(GalaxyLoggerFactory.class);

    static {
        ServiceLoader<IGalaxyLoggerProvider> loader = ServiceLoader.load(IGalaxyLoggerProvider.class);
        Iterator<IGalaxyLoggerProvider> iterator = loader.iterator();

        if (iterator.hasNext()) {
            PROVIDER = iterator.next();
            logger.info("IGalaxyLoggerProvider initialized using provider: " + PROVIDER.getClass().getName());
        } else {
            throw new IllegalStateException("No implementation for IGalaxyLoggerProvider found on classpath. " +
                    "Please include a provider module.");
        }

        if (iterator.hasNext()) {
            logger.warn("Multiple IGalaxyLoggerProvider found. Using the first one.");
        }
    }

    private GalaxyLoggerFactory() {}

    public static IGalaxyLogger getLogger(String name) {
        return PROVIDER.getLogger(name);
    }

    public static IGalaxyLogger getLogger(Class<?> clazz) {
        return getLogger(clazz.getName());
    }
}
