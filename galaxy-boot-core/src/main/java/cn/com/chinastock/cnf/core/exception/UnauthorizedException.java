package cn.com.chinastock.cnf.core.exception;

/**
 * UnauthorizedException 类表示未经授权的异常情况。
 *
 * <p>此异常一般在校验API访问权限失败时抛出。</p>
 * <p>对于UnauthorizedException，框架会返回HttpStatusCode=401，并将错误码和错误信息填写到Meta中返回给前端。</p>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #UnauthorizedException(String)}：构造一个带有指定错误消息的 UnauthorizedException 实例。</li>
 *     <li>{@link #UnauthorizedException(String, String)}：构造一个带有指定错误码和错误消息的 UnauthorizedException 实例。</li>
 *     <li>{@link #getCode()}：获取异常的错误码。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class UnauthorizedException extends GalaxyBaseException {

    public UnauthorizedException(String message) {
        super(message);
    }

    public UnauthorizedException(String code, String message) {
        super(code, message);
    }

    public UnauthorizedException(String code, String message, Exception exception) {
        super(code, message, exception);
    }
}
