package cn.com.chinastock.cnf.core.log.filter;

import cn.com.chinastock.cnf.core.log.config.PropertyConstants;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;
import org.apache.logging.log4j.core.filter.AbstractFilter;

/**
 * ThrowablePrettyPrintFilter 是一个用于日志处理的过滤器，用于根据配置决定是否对异常信息进行美化输出。
 * 在 log4j2.xml 中配置该过滤器，可以根据系统配置和日志事件决定是否对异常信息进行美化输出。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #createFilter(Result, Result)}：工厂方法，用于创建 ThrowablePrettyPrintFilter 实例。</li>
 *     <li>{@link #filter(LogEvent)}：根据系统配置和日志事件决定是否对异常信息进行美化输出。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Plugin(name = "ThrowablePrettyPrintFilter", category = "Core", elementType = "filter", printObject = true)
public final class ThrowablePrettyPrintFilter extends AbstractFilter {

    private ThrowablePrettyPrintFilter(Result onMatch, Result onMismatch) {
        super(onMatch, onMismatch);
    }

    @PluginFactory
    public static ThrowablePrettyPrintFilter createFilter(
            @PluginAttribute(value = "onMatch", defaultString = "ACCEPT") Result onMatch,
            @PluginAttribute(value = "onMismatch", defaultString = "DENY") Result onMismatch) {
        return new ThrowablePrettyPrintFilter(onMatch, onMismatch);
    }

    @Override
    public Result filter(LogEvent event) {
        boolean prettyPrintEnabled = Boolean.parseBoolean(
                System.getProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "false"));
        if (prettyPrintEnabled && event.getThrown() != null) {
            return onMatch;
        }
        return onMismatch;
    }
}