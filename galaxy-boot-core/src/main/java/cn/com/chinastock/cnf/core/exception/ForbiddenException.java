package cn.com.chinastock.cnf.core.exception;

/**
 * ForbiddenException 类是 RuntimeException 的子类，用于表示禁止访问的异常情况。
 *
 * <p>此异常一般在校验API访问权限失败时抛出。</p>
 * <p>对于ForbiddenException，框架会返回HttpStatusCode=403，并将错误码和错误信息填写到Meta中返回给前端。</p>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #ForbiddenException(String)}：构造一个带有指定错误消息的 ForbiddenException 实例。</li>
 *     <li>{@link #ForbiddenException(String, String)}：构造一个带有指定错误码和错误消息的 ForbiddenException 实例。</li>
 *     <li>{@link #ForbiddenException(String, String, Exception)}：构造一个带有指定错误码、错误消息和异常原因的 ForbiddenException 实例。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class ForbiddenException extends GalaxyBaseException {

    public ForbiddenException(String message) {
        super(message);
    }

    public ForbiddenException(String code, String message) {
        super(code, message);
    }

    public ForbiddenException(String code, String message, Exception exception) {
        super(code, message, exception);
    }
}
