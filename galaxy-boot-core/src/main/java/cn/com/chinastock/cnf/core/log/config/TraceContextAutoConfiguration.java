package cn.com.chinastock.cnf.core.log.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.MicrometerIntegratedTraceContext;
import cn.com.chinastock.cnf.core.log.context.W3CTraceContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TraceContext 自动配置类
 * 
 * <p>该配置类负责配置链路追踪上下文实现，支持以下策略：</p>
 * <ul>
 *     <li>优先使用 MicrometerIntegratedTraceContext（当 Micrometer Tracing 可用时）</li>
 *     <li>回退到 W3CTraceContext（当 Micrometer Tracing 不可用时）</li>
 * </ul>
 * 
 * <p>该配置类被 Web 和 WebFlux 两个 starter 共享使用，确保链路追踪的一致性。</p>
 * 
 * <AUTHOR> Boot Team
 */
@Configuration
public class TraceContextAutoConfiguration {

    /**
     * 优先使用 Micrometer 集成的 TraceContext 实现
     * 当 Micrometer Tracing 可用时，使用 MicrometerIntegratedTraceContext
     *
     * @return MicrometerIntegratedTraceContext 实例
     */
    @Bean
    @ConditionalOnClass(name = "io.micrometer.tracing.Tracer")
    @ConditionalOnMissingBean(ITraceContext.class)
    public ITraceContext micrometerIntegratedTraceContext() {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot Core: Using MicrometerIntegratedTraceContext with Micrometer Tracing");
        try {
            // 创建 MicrometerIntegratedTraceContext，它会通过 ApplicationContextAware 获取 Spring 容器
            // 然后从容器中动态获取 Tracer Bean
            return new MicrometerIntegratedTraceContext(null);
        } catch (Exception e) {
            GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG, "Failed to create MicrometerIntegratedTraceContext, falling back to W3CTraceContext: " + e.getMessage());
            return new W3CTraceContext();
        }
    }

    /**
     * 回退的 TraceContext 实现（当 Micrometer Tracing 不可用时使用）
     *
     * @return W3CTraceContext 实例
     */
    @Bean
    @ConditionalOnMissingClass("io.micrometer.tracing.Tracer")
    @ConditionalOnMissingBean(ITraceContext.class)
    public ITraceContext w3cTraceContext() {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot Core: Using W3CTraceContext as fallback TraceContext implementation (Micrometer Tracing not available)");
        return new W3CTraceContext();
    }
}
