package cn.com.chinastock.cnf.core.log.utils;

import cn.com.chinastock.cnf.core.log.context.TraceConstants;

import java.util.UUID;

public class TraceUtils {

    /**
     * 生成符合 W3C Trace Context 标准的 traceparent 头值
     *
     * <pre>
     *    {@code
     *        String traceParent = generateTraceParent("0af7651916cd43dd8448eb211c80319c", "00f067aa0ba902b7");
     *        // traceParent = "00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01"
     *    }
     *    </pre>
     *
     * @param traceId 128位的 trace ID
     * @param spanId  64位的 span ID
     * @return 格式化的 traceparent 字符串，格式为: version-traceId-spanId-flags
     */
    public static String generateTraceParent(String traceId, String spanId) {
        return String.format("%s-%s-%s-%s", TraceConstants.VERSION, traceId, spanId, TraceConstants.TRACE_FLAGS);
    }

    /**
     * 生成追踪ID
     *
     * @return 追踪ID
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成Span ID
     *
     * @return Span ID
     */
    public static String generateSpanId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}
