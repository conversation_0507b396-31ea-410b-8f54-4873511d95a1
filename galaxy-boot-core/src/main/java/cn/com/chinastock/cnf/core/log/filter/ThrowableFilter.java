package cn.com.chinastock.cnf.core.log.filter;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;
import org.apache.logging.log4j.core.filter.AbstractFilter;

/**
 * ThrowableFilter 是一个用于过滤日志事件中是否包含 Throwable 的过滤器。
 * 用来识别在日志事件中是否包含 Throwable，如果包含则采用不同的日志格式进行输出
 * 在 log4j2.xml 中配置该过滤器，可以根据日志事件中是否包含 Throwable 来决定是否输出日志。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #createFilter(Result, Result)}：创建并返回一个 ThrowableFilter 实例。</li>
 *     <li>{@link #filter(LogEvent)}：根据日志事件中是否包含 Throwable 返回相应的过滤结果。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Plugin(name = "ThrowableFilter", category = "Core", elementType = "filter", printObject = true)
public final class ThrowableFilter extends AbstractFilter {

    private ThrowableFilter(Result onMatch, Result onMismatch) {
        super(onMatch, onMismatch);
    }

    @PluginFactory
    public static ThrowableFilter createFilter(
            @PluginAttribute("onMatch") final Result onMatch,
            @PluginAttribute("onMismatch") final Result onMismatch) {
        return new ThrowableFilter(onMatch == null ? Result.NEUTRAL : onMatch,
                onMismatch == null ? Result.DENY : onMismatch);
    }

    @Override
    public Result filter(LogEvent event) {
        if (event.getThrown() != null) {
            return onMatch;
        }
        return onMismatch;
    }
}