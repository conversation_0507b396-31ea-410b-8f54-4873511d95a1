package cn.com.chinastock.cnf.core.http.annotations;

import cn.com.chinastock.cnf.core.http.Meta;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * ApiMetaCode 注解用于swagger文档中的接口，描述{@link Meta}的code和message。
 *
 * <p>该注解需嵌套在 {@link ApiMetaCodes}中使用。</p>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Repeatable(ApiMetaCodes.class)
public @interface ApiMetaCode {

    String code() default "";
    String description() default "";
}
