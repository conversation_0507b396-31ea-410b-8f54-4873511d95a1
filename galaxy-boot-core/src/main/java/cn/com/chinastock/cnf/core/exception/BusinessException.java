package cn.com.chinastock.cnf.core.exception;

import java.text.MessageFormat;

/**
 * BusinessException 类是自定义的业务异常类，继承自 RuntimeException。
 *
 * <p>业务代码中，如果业务处理失败，可以抛出此异常，并传递错误码和错误信息。</p>
 * <p>对于BusinessException，框架会返回HttpStatusCode=200，并将错误码和错误信息填写到Meta中返回给前端。</p>
 *
 * <p>该类中包含以下公共方法：</p>
 * <ul>
 *     <li>{@link #BusinessException(String, String)}：构造函数，用于创建带有指定错误码的业务异常。</li>
 *     <li>{@link #BusinessException(String, String, Exception)}：构造函数，用于创建带有指定错误码、错误信息和异常的业务异常。</li>
 *     <li>{@link #BusinessException(ErrorDescriptor, Object...)}：构造函数，用于通过接口创建带有指定错误码的业务异常。</li>
 *     <li>{@link #BusinessException(ErrorDescriptor, Exception, Object...)}：构造函数，用于通过接口创建带有指定错误码、错误信息和异常的业务异常。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class BusinessException extends GalaxyBaseException {

    public BusinessException(String code, String message) {
        super(code, message);
    }

    public BusinessException(String code, String message, Exception exception) {
        super(code, message, exception);
    }

    public BusinessException(ErrorDescriptor errorDescriptor, Object... args) {
        super(errorDescriptor.getCode(),
                (args == null || args.length == 0)
                        ? errorDescriptor.getMessage()
                        : MessageFormat.format(errorDescriptor.getMessage(), args)
        );
    }

    public BusinessException(ErrorDescriptor errorDescriptor, Exception exception, Object... args) {
        super(errorDescriptor.getCode(),
                (args == null || args.length == 0)
                        ? errorDescriptor.getMessage()
                        : MessageFormat.format(errorDescriptor.getMessage(), args),
                exception
        );
    }
}
