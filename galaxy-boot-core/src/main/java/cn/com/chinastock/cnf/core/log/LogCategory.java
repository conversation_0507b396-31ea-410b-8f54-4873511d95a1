package cn.com.chinastock.cnf.core.log;

/**
 * 日志类别枚举，定义了不同类型的日志分类。
 *
 * <p>包含以下公开的枚举值：</p>
 * <ul>
 *     <li>{@link LogCategory#TRANSACTION_LOG}：交易流水日志。记录字段包括用户标识、交易业务参数、交易状态及结果等信息，安全级别（公司数据分类分级）为4级的数据（一般指投资者的账户认证信息，包含投资者用于身份认证的账户编码、密码等）敏感信息应进行脱敏处理。</li>
 *     <li>{@link LogCategory#BUSINESS_LOG}：业务行为日志。记录字段包括业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。</li>
 *     <li>{@link LogCategory#USER_ACTION_LOG}：应用系统用户操作行为日志。记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。</li>
 *     <li>{@link LogCategory#APP_LOG}：应用运行日志。</li>
 *     <li>{@link LogCategory#FRAMEWORK_LOG}：框架日志。</li>
 *     <li>{@link LogCategory#EXCEPTION_LOG}：异常处理相关日志。</li>
 *     <li>{@link LogCategory#SQL_LOG}：SQL语句日志。</li>
 *     <li>{@link LogCategory#REQUEST_LOG}：请求日志。</li>
 *     <li>{@link LogCategory#RESPONSE_LOG}：响应日志。</li>
 *     <li>{@link LogCategory#PERFORMANCE_LOG}：性能相关日志。</li>
 * </ul>
 *
 */
public enum LogCategory {
    /**
     * 交易流水日志。记录字段包括用户标识、交易业务参数、交易状态及结果等信息，安全级别（公司数据分类分级）为4级的数据（一般指投资者的账户认证信息，包含投资者用于身份认证的账户编码、密码等）敏感信息应进行脱敏处理。
     */
    TRANSACTION_LOG,
    /**
     * 业务行为日志。记录字段包括业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。
     */
    BUSINESS_LOG,
    /**
     * 应用系统用户操作行为日志。记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。
     */
    USER_ACTION_LOG,
    /**
     * 应用运行日志
     */
    APP_LOG,
    /**
     * 框架日志
     */
    FRAMEWORK_LOG,
    /**
     * 异常处理相关日志
     */
    EXCEPTION_LOG,
    /**
     * SQL语句日志
     */
    SQL_LOG,
    /**
     * 请求日志
     */
    REQUEST_LOG,
    /**
     * 响应日志
     */
    RESPONSE_LOG,
    /**
     * 性能相关日志
     */
    PERFORMANCE_LOG
}