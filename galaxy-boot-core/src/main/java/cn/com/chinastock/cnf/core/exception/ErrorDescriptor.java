package cn.com.chinastock.cnf.core.exception;

/**
 * ErrorDescriptor 类是用于扩展错误信息的接口。
 *
 * <p>业务代码中，如果定义了错误码类，可继承此接口，并传递给BusinessException使用。</p>
 *
 * <p>该类中包含以下公共方法：</p>
 * <ul>
 *     <li>{@link #getCode()}：获取错误码。</li>
 *     <li>{@link #getMessage()}：获取错误信息。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public interface ErrorDescriptor {

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    String getCode();

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    String getMessage();
}
