package cn.com.chinastock.cnf.core.http;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * BasePageInfo 类用于封装分页查询的相关信息，包括当前页码、每页大小、总记录数、排序字段和总页数。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #BasePageInfo()}：无参构造函数。</li>
 *     <li>{@link #BasePageInfo(int, int, long, String, int)}：带参构造函数，用于初始化分页信息。</li>
 *     <li>{@link #toString()}：返回分页信息的字符串表示。</li>
 *     <li>{@link #getPageNum()}：获取当前页码。</li>
 *     <li>{@link #setPageNum(int)}：设置当前页码。</li>
 *     <li>{@link #getPageSize()}：获取每页大小。</li>
 *     <li>{@link #setPageSize(int)}：设置每页大小。</li>
 *     <li>{@link #getTotal()}：获取总记录数。</li>
 *     <li>{@link #setTotal(long)}：设置总记录数。</li>
 *     <li>{@link #getOrderBy()}：获取排序字段。</li>
 *     <li>{@link #setOrderBy(String)}：设置排序字段。</li>
 *     <li>{@link #getPages()}：获取总页数。</li>
 *     <li>{@link #setPages(int)}：设置总页数。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class BasePageInfo {
    /**
     * 查询页
     */
    @JSONField(name = "PageNum")
    private int pageNum;

    /**
     * 每页查询大小
     */
    @JSONField(name = "PageSize")
    private int pageSize;

    /**
     * 总记录数
     */
    @JSONField(name = "Total")
    private long total;

    /**
     * 排序
     */
    @JSONField(name = "OrderBy")
    private String orderBy;

    /**
     * 总页数
     */
    @JSONField(name = "Pages")
    private int pages;

    public BasePageInfo() {
    }

    public BasePageInfo(int pageNum, int pageSize, long total, String orderBy, int pages) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.orderBy = orderBy;
        this.pages = pages;
    }

    @Override
    public String toString() {
        return "BasePageInfo [pageNum=" + pageNum + ", pageSize=" + pageSize + ", total=" + total + ", orderBy="
                + orderBy + ", pages=" + pages + "]";
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }
}
