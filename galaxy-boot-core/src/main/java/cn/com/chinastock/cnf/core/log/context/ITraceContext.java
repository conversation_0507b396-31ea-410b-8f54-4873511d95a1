package cn.com.chinastock.cnf.core.log.context;

import java.util.List;
import java.util.Map;

/**
 * 追踪上下文接口，用于获取和存储追踪信息
 */
public interface ITraceContext {
    /**
     * 获取追踪ID
     *
     * @return 追踪ID
     */
    String getTraceId();

    /**
     * 获取当前节点的Span ID
     *
     * @return Span ID
     */
    String getSpanId();

    /**
     * 获取父节点的Span ID
     *
     * @return Parent Span ID
     */
    String getParentSpanId();

    /**
     * 从请求头中提取追踪信息
     * <p>
     * 1. 如果请求头中有追踪信息，则提取其中的追踪ID和Parent Span ID
     * 2. 如果请求头中没有追踪信息，则生成新的追踪ID和Span ID
     * 3. 生成新的Span ID
     * </p>
     *
     * @param headers 请求头值
     */
    void extractTraceContext(Map<String, String> headers);

    /**
     * 生成用于跟踪的 HTTP 请求头信息，在调用其他服务时将该请求头信息添加到请求头中
     *
     * @param traceId 追踪ID
     * @param spanId  Span ID
     *
     * @return 包含跟踪信息的请求头 Map
     */
    Map<String, String> generateTraceHeaders(String traceId, String spanId);

    /**
     * 清理上下文
     * <p>
     *     1. 清理追踪ID
     *     2. 清理Span ID
     *     3. 清理Parent Span ID
     * </p>
     */
    void clear();

    /**
     * 获取追踪信息的请求头
     * <p>
     *     不同的APM工具会通过不同的请求头传递追踪信息，例如：
     *     1. Zipkin：X-B3-TraceId、X-B3-ParentSpanId、X-B3-SpanId
     *     2. SkyWalking：sw8
     *     3. Jaeger：uber-trace-id
     *     4. ...
     *     为了兼容不同的APM工具，需要根据不同的APM工具设置不同的请求头
     * </p>
     *
     * @return 存储追踪信息的请求头，有些APM工具可能需要多个请求头
     */
    List<String> getTraceHeaders();

    /**
     * 生成追踪ID
     *
     * @return 追踪ID
     */
    String generateTraceId();

    /**
     * 生成Span ID
     *
     * @return Span ID
     */
    String generateSpanId();
}
