package cn.com.chinastock.cnf.core.http;

import com.alibaba.fastjson2.annotation.JSONField;

import static com.alibaba.fastjson2.JSONWriter.Feature.WriteMapNullValue;


/**
 * BaseResponseDTO 类用于封装 API 响应数据。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #BaseResponse()}：默认构造函数。</li>
 *     <li>{@link #BaseResponse(Meta, T)}：带参数的构造函数，用于初始化 meta 和 data。</li>
 *     <li>{@link #getMeta()}：获取响应的元数据。</li>
 *     <li>{@link #setMeta(Meta)}：设置响应的元数据。</li>
 *     <li>{@link #getData()}：获取响应的数据。</li>
 *     <li>{@link #setData(T)}：设置响应的数据。</li>
 * </ul>
 *
 * <p>该类还包含一个嵌套的静态类 {@link Meta}，用于封装响应的元数据。</p>
 *
 * @param <T> 数据类型
 * <AUTHOR>
 */
public class BaseResponse<T> {
    /**
     * 响应码
     */
    @JSONField(name = "meta", serializeFeatures = {WriteMapNullValue})
    private Meta meta;

    /**
     * 响应数据(银河接口规范要求此对象为数组。使用者可以忽略)
     */
    @JSONField(name = "data", serializeFeatures = {WriteMapNullValue})
    private T data;

    /**
     * 分页信息
     */
    @JSONField(name = "pageInfo")
    private BasePageInfo pageInfo;

    public BaseResponse() {
        this.meta = new Meta();
        this.data = null;
        this.pageInfo = null;
    }

    public BaseResponse(Meta meta, T data) {
        this.meta = meta;
        this.data = data;
        this.pageInfo = null;
    }

    public BaseResponse(Meta meta, T data, BasePageInfo pageInfo) {
        this.meta = meta;
        this.data = data;
        this.pageInfo = pageInfo;
    }

    public BaseResponse(Boolean success, String code, String message) {
        this.meta = new Meta();
        this.meta.setSuccess(success);
        this.meta.setCode(code);
        this.meta.setMessage(message);
        this.data = null;
        this.pageInfo = null;
    }

    @Override
    public String toString() {
        return "BaseResponse [meta=" + meta + ", data=" + data + ", pageInfo=" + pageInfo + "]";
    }

    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Boolean success, String code, String message) {
        if (this.meta == null) {
            this.meta = new Meta();
        }
        this.meta.setSuccess(success);
        this.meta.setCode(code);
        this.meta.setMessage(message);
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public BasePageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(BasePageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
