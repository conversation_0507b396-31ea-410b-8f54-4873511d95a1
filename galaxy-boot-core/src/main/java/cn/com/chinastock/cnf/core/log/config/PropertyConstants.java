package cn.com.chinastock.cnf.core.log.config;

public final class PropertyConstants {

    private PropertyConstants() {
        // Private constructor to prevent instantiation
    }

    public static final String GALAXY_LOG_CONFIG_PREFIX = "galaxy.log";

    public static final String GALAXY_SYSTEM_CODE = "galaxy.system.code";
    public static final String GALAXY_SERVICE_NAME = "spring.application.name";

    public static final String SYS_PROPERTY_IP_ADDR = "ip-addr";
    public static final String SYS_PROPERTY_SYSTEM_CODE = "system-code";
    public static final String SYS_PROPERTY_SERVICE_NAME = "service-name";
    public static final String SYS_PROPERTY_EXCEPTION_PRETTY_PRINT = "exception-pretty-print";

    public static final String GALAXY_LOG_EXCEPTION_PRETTY_PRINT = "galaxy.log.exception-pretty-print";
    public static final String GALAXY_LOG_REQUEST_RESPONSE_MASK_FIELD = "galaxy.log.request-response.mask-field";
    public static final String GALAXY_BOOT_PROPERTIES = "galaxy-boot.properties";
}