package cn.com.chinastock.cnf.core.log.config;

import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * LoggingEnvironmentPostProcessor 类是一个 Spring 环境后处理器，用于在应用程序启动时设置系统属性。
 * 这里设置的系统属性会在日志记录时使用，具体配置参见 log4j2.xml 文件。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #postProcessEnvironment(ConfigurableEnvironment, SpringApplication)}：在环境准备完成后，设置与日志记录相关的系统属性。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Configuration
public class LoggingEnvironmentPostProcessor implements EnvironmentPostProcessor {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 提前设置系统属性
        System.setProperty(PropertyConstants.SYS_PROPERTY_IP_ADDR, HttpLogUtils.getLocalIPAddress());
        System.setProperty(PropertyConstants.SYS_PROPERTY_SYSTEM_CODE, environment.getProperty(PropertyConstants.GALAXY_SYSTEM_CODE, "-"));
        System.setProperty(PropertyConstants.SYS_PROPERTY_SERVICE_NAME, environment.getProperty(PropertyConstants.GALAXY_SERVICE_NAME, "-"));
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, environment.getProperty(PropertyConstants.GALAXY_LOG_EXCEPTION_PRETTY_PRINT, "false"));
    }
}