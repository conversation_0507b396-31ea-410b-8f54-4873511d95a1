package cn.com.chinastock.cnf.core.log.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.dto.ApolloConfig;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import jakarta.annotation.PostConstruct;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

import java.util.Set;


/**
 * LoggingDynamicAutoConfiguration 类用于动态配置日志级别，基于 Apollo 配置中心的配置进行日志级别的动态更新。
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(ApolloConfig.class)
public class LoggingDynamicAutoConfiguration {

    private static final String LOGGER_LEVEL_PREFIX = "logging.level.";
    private static final Level DEFAULT_LOG_LEVEL = Level.INFO;

    private static final Logger logger = LoggerFactory.getLogger(LoggingDynamicAutoConfiguration.class);

    @PostConstruct
    public void initialize() {
        logger.info("LoggingDynamicAutoConfiguration initialized");
        Config config = ConfigService.getAppConfig();
        initializeLogConfig(config);
        config.addChangeListener(this::onChange);
    }

    private void initializeLogConfig(Config config) {
        // 获取所有日志级别配置并初始化
        Set<String> propertyNames = config.getPropertyNames();
        propertyNames.stream()
                .filter(name -> name.startsWith(LOGGER_LEVEL_PREFIX))
                .forEach(name -> {
                    String loggerName = name.substring(LOGGER_LEVEL_PREFIX.length());
                    String level = config.getProperty(name, DEFAULT_LOG_LEVEL.name());
                    updateLoggerLevel(loggerName, level);
                });
        // 初始化异常日志打印方式，如果配置了异常日志打印方式，则设置系统属性
        if (propertyNames.contains(PropertyConstants.GALAXY_LOG_EXCEPTION_PRETTY_PRINT)) {
            System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT,
                    config.getProperty(PropertyConstants.GALAXY_LOG_EXCEPTION_PRETTY_PRINT, "false"));
        }
    }

    private void onChange(ConfigChangeEvent changeEvent) {
        // 处理日志配置变更
        changeEvent.changedKeys().stream()
                .filter(key -> key.startsWith(LOGGER_LEVEL_PREFIX))
                .forEach(key -> {
                    String loggerName = key.substring(LOGGER_LEVEL_PREFIX.length());
                    String newLevel = changeEvent.getChange(key).getNewValue();
                    updateLoggerLevel(loggerName, newLevel);
                });
        // 处理异常日志打印方式变更
        if (changeEvent.isChanged(PropertyConstants.GALAXY_LOG_EXCEPTION_PRETTY_PRINT)) {
            System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT,
                    changeEvent.getChange(PropertyConstants.GALAXY_LOG_EXCEPTION_PRETTY_PRINT).getNewValue());
        }
    }

    private void updateLoggerLevel(String loggerName, String levelName) {
        logger.info("Updating logger level: {}={}", loggerName, levelName);
        Level level = getLevel(levelName);

        LoggerContext ctx = LoggerContext.getContext(false);
        org.apache.logging.log4j.core.config.Configuration config = ctx.getConfiguration();

        // 更新日志级别
        if (LoggerConfig.ROOT.equals(loggerName)) {
            config.getRootLogger().setLevel(level);
        } else if (config.getLoggers().containsKey(loggerName)) {
            config.getLoggerConfig(loggerName).setLevel(level);
        } else {
            logger.info("Logger {} not found, no update", loggerName);
        }

        ctx.updateLoggers();
    }

    private static Level getLevel(String levelName) {
        try {
            return Level.valueOf(levelName.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid log level: {}, using default level: {}", levelName, DEFAULT_LOG_LEVEL);
            return DEFAULT_LOG_LEVEL;
        }
    }
} 