package cn.com.chinastock.cnf.core.log.config;

import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * LogProperties 类用于配置日志相关的属性，包括系统三字码、默认日志类别、异常堆栈信息打印方式、日志长度限制、请求响应日志的启用状态等。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #getSystemCode()}：获取系统三字码。</li>
 *     <li>{@link #setSystemCode(String)}：设置系统三字码。</li>
 *     <li>{@link #getDefaultCategory()}：获取默认日志类别。</li>
 *     <li>{@link #setDefaultCategory(LogCategory)}：设置默认日志类别。</li>
 *     <li>{@link #isExceptionPrettyPrint()}：检查是否以可读方式打印异常堆栈信息。</li>
 *     <li>{@link #setExceptionPrettyPrint(boolean)}：设置是否以可读方式打印异常堆栈信息。</li>
 *     <li>{@link #getMaxLength()}：获取最大日志长度。</li>
 *     <li>{@link #setMaxLength(int)}：设置最大日志长度。</li>
 *     <li>{@link #isRequestResponseEnabled()}：检查是否启用请求响应日志。</li>
 *     <li>{@link #setRequestResponseEnabled(boolean)}：设置是否启用请求响应日志。</li>
 *     <li>{@link #isRequestHeadersEnabled()}：检查是否启用请求头日志。</li>
 *     <li>{@link #setRequestHeadersEnabled(boolean)}：设置是否启用请求头日志。</li>
 *     <li>{@link #isResponseHeadersEnabled()}：检查是否启用响应头日志。</li>
 *     <li>{@link #setResponseHeadersEnabled(boolean)}：设置是否启用响应头日志。</li>
 *     <li>{@link #isRequestResponseMaskFieldEnabled()}：检查是否启用请求响应日志的字段掩码。</li>
 *     <li>{@link #setRequestResponseMaskFieldEnabled(boolean)}：设置是否启用请求响应日志的字段掩码。</li>
 *     <li>{@link #isPerformanceLogEnabled()}：检查是否启用性能日志。</li>
 *     <li>{@link #setPerformanceLogEnabled(boolean)}：设置是否启用性能日志。</li>
 *     <li>{@link #isReqResLogEnabledButNotControllerLog()}：检查是否启用了请求响应日志但未启用控制器日志。</li>
 *     <li>{@link #isControllerLogEnabled()}：检查是否启用了控制器日志。</li>
 * </ul>
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = PropertyConstants.GALAXY_LOG_CONFIG_PREFIX)
public class LogProperties {

    public static final int DEFAULT_MAX_LENGTH = 5000;

    public LogProperties() {
    }

    /**
     * 系统三字码
     */
    @Value("${galaxy.system.code:}")
    private String systemCode = "";

    /**
     * 默认日志类别
     *
     * @see LogCategory
     */
    @Value("${galaxy.log.default-category:FRAMEWORK_LOG}")
    private LogCategory defaultCategory = LogCategory.FRAMEWORK_LOG;

    /**
     * 是否以可读方式打印异常堆栈信息
     * （默认为false，如果配置为true，会在正常日志输出后额外输出一条异常日志）
     */
    @Value("${galaxy.log.exception-pretty-print:false}")
    private boolean exceptionPrettyPrint = false;

    /**
     * 最大日志长度，默认值为5000
     */
    @Value("${galaxy.log.max-length:5000}")
    private int maxLength = DEFAULT_MAX_LENGTH;

    /**
     * 是否启用请求响应日志
     */
    @Value("${galaxy.log.request-response.enabled:true}")
    private boolean requestResponseEnabled = true;

    /**
     * 是否启用webflux请求时序日志
     */
    @Value("${galaxy.log.request-response.webflux-series:false}")
    private boolean webfluxRequestSeriesLogEnabled = false;

    /**
     * 是否启用请求头日志
     */
    @Value("${galaxy.log.request-response.request-headers:false}")
    private boolean requestHeadersEnabled = false;

    /**
     * 是否启用响应头日志
     */
    @Value("${galaxy.log.request-response.response-headers:false}")
    private boolean responseHeadersEnabled = false;

    /**
     * 是否启用请求响应日志的字段掩码
     */
    @Value("${galaxy.log.request-response.mask-field:false}")
    private boolean requestResponseMaskFieldEnabled = false;

    /**
     * 是否启用性能日志
     */
    @Value("${galaxy.log.performance.enabled:true}")
    private boolean performanceLogEnabled = true;

    /**
     * SSE端点路径模式列表
     * 
     * <p>用于识别SSE（Server-Sent Events）端点，这些端点将跳过响应体缓存以避免阻塞流式传输。</p>
     * <p>默认包含常见的SSE端点模式：/sse, /mcp/sse, /events, /stream</p>
     */
    @Value("${galaxy.log.sse.endpoint-patterns:/sse,/mcp/sse,/events,/stream}")
    private List<String> sseEndpointPatterns = Arrays.asList("/sse", "/mcp/sse", "/events", "/stream");

    /**
     * 是否启用SSE响应特殊处理
     * 
     * <p>当启用时，SSE响应将跳过响应体缓存，确保流式传输不被阻塞。</p>
     */
    @Value("${galaxy.log.sse.enabled:true}")
    private boolean sseResponseHandlingEnabled = true;

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public LogCategory getDefaultCategory() {
        return defaultCategory;
    }

    public void setDefaultCategory(LogCategory defaultCategory) {
        this.defaultCategory = defaultCategory;
    }

    public boolean isExceptionPrettyPrint() {
        return exceptionPrettyPrint;
    }

    public void setExceptionPrettyPrint(boolean exceptionPrettyPrint) {
        this.exceptionPrettyPrint = exceptionPrettyPrint;
    }

    public int getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public boolean isRequestResponseEnabled() {
        return requestResponseEnabled;
    }

    public void setRequestResponseEnabled(boolean requestResponseEnabled) {
        this.requestResponseEnabled = requestResponseEnabled;
    }

    public boolean isRequestHeadersEnabled() {
        return requestHeadersEnabled;
    }

    public void setRequestHeadersEnabled(boolean requestHeadersEnabled) {
        this.requestHeadersEnabled = requestHeadersEnabled;
    }

    public boolean isResponseHeadersEnabled() {
        return responseHeadersEnabled;
    }

    public void setResponseHeadersEnabled(boolean responseHeadersEnabled) {
        this.responseHeadersEnabled = responseHeadersEnabled;
    }

    public boolean isRequestResponseMaskFieldEnabled() {
        return requestResponseMaskFieldEnabled;
    }

    public void setRequestResponseMaskFieldEnabled(boolean requestResponseMaskFieldEnabled) {
        this.requestResponseMaskFieldEnabled = requestResponseMaskFieldEnabled;
    }

    public boolean isPerformanceLogEnabled() {
        return performanceLogEnabled;
    }

    public void setPerformanceLogEnabled(boolean performanceLogEnabled) {
        this.performanceLogEnabled = performanceLogEnabled;
    }

    public Boolean isReqResLogEnabledButNotControllerLog() {
        return requestResponseEnabled && !requestResponseMaskFieldEnabled;
    }

    public Boolean isControllerLogEnabled() {
        return requestResponseEnabled && requestResponseMaskFieldEnabled;
    }

    public boolean isWebfluxRequestSeriesLogEnabled() {
        return webfluxRequestSeriesLogEnabled;
    }

    public void setWebfluxRequestSeriesLogEnabled(boolean webfluxRequestSeriesLogEnabled) {
        this.webfluxRequestSeriesLogEnabled = webfluxRequestSeriesLogEnabled;
    }

    public List<String> getSseEndpointPatterns() {
        return sseEndpointPatterns;
    }

    public void setSseEndpointPatterns(List<String> sseEndpointPatterns) {
        this.sseEndpointPatterns = sseEndpointPatterns;
    }

    public boolean isSseResponseHandlingEnabled() {
        return sseResponseHandlingEnabled;
    }

    public void setSseResponseHandlingEnabled(boolean sseResponseHandlingEnabled) {
        this.sseResponseHandlingEnabled = sseResponseHandlingEnabled;
    }
}