package cn.com.chinastock.cnf.core.log;

import org.slf4j.MDC;
import org.slf4j.helpers.MessageFormatter;

import static cn.com.chinastock.cnf.core.log.context.LogMDCKeys.LOG_CATEGORY;

/**
 * <p>该类中包含以下方法：</p>
 * <ul>
 *     <li>{@link #info(String, Object...)}：记录信息级别的日志。</li>
 *     <li>{@link #error(String, Throwable)}：记录错误日志，包含异常对象。</li>
 *     <li>{@link #error(String, Object...)}：记录错误日志，支持格式化参数。</li>
 *     <li>{@link #warn(String, Object...)}：记录警告日志。</li>
 *     <li>{@link #debug(String, Object...)}：打印调试信息。</li>
 *     <li>{@link #info(LogCategory, String, Object...)}：记录指定类别的INFO级别日志。</li>
 *     <li>{@link #error(LogCategory, String, Throwable)}：记录指定类别的ERROR级别日志，包含异常对象。</li>
 *     <li>{@link #error(LogCategory, String, Object...)}：记录指定类别的ERROR级别日志，支持格式化参数。</li>
 *     <li>{@link #warn(LogCategory, String, Object...)}：记录指定类别的WARN级别日志。</li>
 *     <li>{@link #debug(LogCategory, String, Object...)}：记录指定类别的DEBUG级别日志。</li>
 * </ul>
 *
 * <AUTHOR>
 * @see LogCategory
 */
public interface IGalaxyLogger {
    String TRUNCATED = "...(truncated)";

    /**
     * 记录信息级别的日志。
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.info("User logged in: {}", user.getName());
     *        // 输出: User logged in: JohnDoe
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数，用于替换占位符
     */
    void info(String message, Object... args);

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *      GalaxyLogger.error("An error occurred", new RuntimeException("error"));
     *      // 输出异常堆栈
     *    }
     * </pre>
     *
     * @param message 错误信息
     * @param t       异常对象
     */
    void error(String message, Throwable t);

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.error("An error occurred: %s", "Invalid input");
     *        // 输出: "An error occurred: Invalid input"
     *    }
     * </pre>
     *
     * @param message 错误信息模板，支持格式化
     * @param args    格式化参数
     */
    void error(String message, Object... args);

    /**
     * 记录警告日志
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.warn("User login failed: {}", username);
     *        // 输出: User login failed: admin
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数
     */
    void warn(String message, Object... args);

    /**
     * 打印调试信息
     *
     * <pre>
     *    {@code
     *        GalaxyLogger.debug("User logged in: %s", user.getName());
     *        // 输出: "User logged in: JohnDoe"
     *    }
     * </pre>
     *
     * @param message 调试信息模板，支持格式化字符串
     * @param args    格式化字符串的参数
     */
    void debug(String message, Object... args);

    /**
     * 记录INFO级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.info(LogCategory.USER_ACTION, "User {} logged in at {}", userId, timestamp);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    void info(LogCategory logCategory, String message, Object... args);

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "An error occurred", new RuntimeException("error"));
     *     // 输出异常堆栈
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param t           异常
     */
    void error(LogCategory logCategory, String message, Throwable t);

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.error(LogCategory.BUSINESS_LOG, "An error occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    void error(LogCategory logCategory, String message, Object... args);

    /**
     * 记录WARN级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.warn(LogCategory.BUSINESS_LOG, "An warning occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    void warn(LogCategory logCategory, String message, Object... args);

    /**
     * 记录DEBUG级别日志
     * <pre>
     *     {@code
     *     GalaxyLogger.debug(LogCategory.BUSINESS_LOG, "An debug message, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    void debug(LogCategory logCategory, String message, Object... args);


    default void setLogCategory(LogCategory category) {
        MDC.put(LOG_CATEGORY, category.name());
    }

    /**
     * 转义信息中的"|"字符，并检查消息最大长度，如果长度过大进行截断
     *
     * @param message   日志消息
     * @param args      参数数组
     * @param maxLength 日志消息最大长度
     * @return 处理后的日志消息
     */
    default String escapeAndTruncateMessage(String message, Object[] args, int maxLength) {
        if (message == null) {
            return "";
        }

        // 使用 SLF4J 的 MessageFormatter 格式化消息
        String formattedMessage = MessageFormatter.arrayFormat(message, args).getMessage();

        // 如果消息超长，进行截断
        if (formattedMessage.length() > maxLength) {
            formattedMessage = formattedMessage.substring(0, maxLength) + TRUNCATED;
        }

        // 转义 | 字符
        return formattedMessage.replace("|", "%7C");
    }
}