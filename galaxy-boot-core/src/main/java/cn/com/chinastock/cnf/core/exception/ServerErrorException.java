package cn.com.chinastock.cnf.core.exception;

/**
 * ServerErrorException 类表示服务器端错误异常。
 *
 * <p>业务代码中，如果发生非预期的问题，如网络超时，空指针等，可以抛出此异常，并传递错误码和错误信息。</p>
 * <p>对于ServerErrorException，框架会返回HttpStatusCode=500，并将错误码和错误信息填写到Meta中返回给前端。</p>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #ServerErrorException(String)}：构造一个带有默认错误码的服务器错误异常。</li>
 *     <li>{@link #ServerErrorException(String, String)}：构造一个带有指定错误码的服务器错误异常。</li>
 *     <li>{@link #ServerErrorException(String, String, Exception)}：构造一个带有指定错误码、错误信息和原始异常的服务器错误异常。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class ServerErrorException extends GalaxyBaseException {

    public ServerErrorException(String message) {
        super(message);
    }

    public ServerErrorException(String code, String message) {
        super(code, message);
    }

    public ServerErrorException(String code, String message, Exception exception) {
        super(code, message, exception);
    }
}
