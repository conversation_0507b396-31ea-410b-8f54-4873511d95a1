package cn.com.chinastock.cnf.core.http.annotations;

import cn.com.chinastock.cnf.core.http.Meta;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * ApiMetaCodes 注解用于swagger文档中的接口，描述{@link Meta}可能出现的返回值。
 *
 * <p>该注解还包含一个嵌套的注解 {@link ApiMetaCode}，用于描述每个{@link Meta}。</p>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface ApiMetaCodes {

    ApiMetaCode[] value() default {};

}
