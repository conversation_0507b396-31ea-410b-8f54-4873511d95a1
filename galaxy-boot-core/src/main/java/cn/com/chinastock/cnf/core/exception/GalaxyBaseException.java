package cn.com.chinastock.cnf.core.exception;

/**
 * BaseGalaxyException 是一个抽象类，用于表示 Galaxy 系统中的基础异常。
 * 该类继承自 {@link RuntimeException}，并提供了额外的异常信息和代码。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #GalaxyBaseException(String)}：使用指定的错误消息构造异常。</li>
 *     <li>{@link #GalaxyBaseException(String, String)}：使用指定的错误代码和错误消息构造异常。</li>
 *     <li>{@link #GalaxyBaseException(String, String, Exception)}：使用指定的错误代码、错误消息和原始异常构造异常。</li>
 *     <li>{@link #getCode()}：获取异常的错误代码。</li>
 *     <li>{@link #getException()}：获取原始的异常对象。</li>
 * </ul>
 *
 * <AUTHOR>
 */
abstract class GalaxyBaseException extends RuntimeException {

    private String code = "";

    private Exception exception = null;

    public GalaxyBaseException(String message) {
        super(message);
    }

    public GalaxyBaseException(String code, String message) {
        super(message);
        this.code = code;
    }

    public GalaxyBaseException(String code, String message, Exception exception) {
        super(message);
        this.code = code;
        this.exception = exception;
    }

    public String getCode() {
        return code;
    }

    public Exception getException() {
        return exception;
    }
}
