package cn.com.chinastock.cnf.core.http;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * Meta 类用于表示元数据信息，包含成功状态、代码和消息。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #Meta()}：默认构造函数。</li>
 *     <li>{@link #Meta(Boolean, String, String)}：带参数的构造函数。</li>
 *     <li>{@link #isSuccess()}：获取成功状态。</li>
 *     <li>{@link #setSuccess(Boolean)}：设置成功状态。</li>
 *     <li>{@link #getCode()}：获取代码。</li>
 *     <li>{@link #setCode(String)}：设置代码。</li>
 *     <li>{@link #getMessage()}：获取消息。</li>
 *     <li>{@link #setMessage(String)}：设置消息。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class Meta {
    /**
     * true：成功，false：失败
     */
    @JSONField(name = "success")
    private Boolean success;

    /**
     * 返回码
     */
    @JSONField(name = "code")
    private String code;

    /**
     * 返回说明
     */
    @JSONField(name = "message")
    private String message;

    @Override
    public String toString() {
        return "Meta [success=" + success + ", code=" + code + ", message=" + message + "]";
    }

    public Meta() {
        this.success = true;
        this.code = "0000";
        this.message = "成功";
    }

    public Meta(Boolean success, String code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Boolean isSuccess() {
        return success;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
