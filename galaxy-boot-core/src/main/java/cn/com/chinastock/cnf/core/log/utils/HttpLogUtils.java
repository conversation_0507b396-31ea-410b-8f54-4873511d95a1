package cn.com.chinastock.cnf.core.log.utils;

import cn.com.chinastock.cnf.core.log.aspect.MaskedField;
import com.alibaba.fastjson2.JSON;
import org.springframework.http.MediaType;

import java.lang.reflect.Field;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * HTTP 工具类，用于处理 HTTP 请求和响应相关的通用功能。
 * 
 * <p>主要功能包括：</p>
 * <ul>
 *     <li>内容类型判断</li>
 *     <li>文件下载判断</li>
 *     <li>请求响应体处理</li>
 *     <li>日志格式化</li>
 *     <li>敏感信息掩码</li>
 * </ul>
 * 
 * <p>主要方法：</p>
 * <ul>
 *     <li>{@link #isRecordableContentType(String)} - 判断是否是可记录的内容类型</li>
 *     <li>{@link #isFileDownloadResponse(String)} - 判断是否是文件下载响应</li>
 *     <li>{@link #getContentLength(String)} - 获取 Content-Length 的值</li>
 *     <li>{@link #extractBody(byte[])} - 从字节数组中提取并处理内容</li>
 *     <li>{@link #flattenJson(String)} - 将 JSON 字符串扁平化处理</li>
 *     <li>{@link #formatLogInfo(Map)} - 格式化日志信息</li>
 *     <li>{@link #maskSensitiveFields(Object)} - 对敏感字段进行掩码处理</li>
 *     <li>{@link #processResponseBody(Object, MediaType)} - 处理响应体对象</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class HttpLogUtils {

    private static final int MAX_DEPTH = 10; // 添加最大递归深度限制

    /**
     * 判断是否是可记录的内容类型。
     * 
     * <p>支持的内容类型包括：</p>
     * <ul>
     *     <li>JSON</li>
     *     <li>XML</li>
     *     <li>纯文本</li>
     *     <li>表单数据</li>
     * </ul>
     *
     * @param contentType Content-Type 头的值
     * @return 如果是可记录的内容类型则返回 true，否则返回 false
     */
    public static boolean isRecordableContentType(String contentType) {
        return contentType != null && (
                contentType.contains(MediaType.APPLICATION_JSON_VALUE) ||
                        contentType.contains(MediaType.APPLICATION_XML_VALUE) ||
                        contentType.contains(MediaType.TEXT_PLAIN_VALUE) ||
                        contentType.contains(MediaType.TEXT_XML_VALUE) ||
                        contentType.contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        );
    }

    /**
     * 判断是否是文件下载响应。
     * 
     * <p>通过检查 Content-Disposition 头中是否包含 "attachment" 来判断。</p>
     *
     * @param contentDisposition Content-Disposition 头的值
     * @return 如果是文件下载响应则返回 true，否则返回 false
     */
    public static boolean isFileDownloadResponse(String contentDisposition) {
        return contentDisposition != null && contentDisposition.toLowerCase().contains("attachment");
    }

    /**
     * 获取 Content-Length 的值。
     *
     * @param contentLengthHeader Content-Length 头的值
     * @return Content-Length 的值，如果无法解析则返回 -1
     */
    public static long getContentLength(String contentLengthHeader) {
        if (contentLengthHeader == null) {
            return -1;
        }

        try {
            return Long.parseLong(contentLengthHeader);
        } catch (NumberFormatException ignored) {
            return -1;
        }
    }

    /**
     * 从字节数组中提取并处理内容。
     * 将字节数组转换为字符串，并对 JSON 格式进行扁平化处理。
     *
     * @param content 需要处理的字节数组
     * @return 处理后的内容字符串，如果内容为空则返回 null
     */
    public static String extractBody(byte[] content) {
        if (content == null || content.length == 0) {
            return null;
        }

        String body = new String(content, StandardCharsets.UTF_8);
        return body.isEmpty() ? null : flattenJson(body);
    }

    /**
     * 获取本机的 IP 地址。
     * 
     * <p>获取顺序：</p>
     * <ol>
     *     <li>首先尝试获取本机网卡IP</li>
     *     <li>如果是回环地址，则遍历网卡获取第一个非回环地址</li>
     *     <li>如果获取失败，返回 "Unknown IP"</li>
     * </ol>
     *
     * @return 本机的 IP 地址
     */
    public static String getLocalIPAddress() {
        try {
            // 首先尝试获取本机网卡IP
            InetAddress localHost = InetAddress.getLocalHost();
            if (!localHost.isLoopbackAddress()) {
                return localHost.getHostAddress();
            }

            // 如果是回环地址，则遍历网卡获取第一个非回环地址
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isUp()) {
                    Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = addresses.nextElement();
                        if (!address.isLoopbackAddress() && address instanceof Inet4Address) {
                            return address.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 发生异常时返回unknown，避免影响应用启动
            return "Unknown IP";
        }
        return "Unknown IP";
    }

    /**
     * 将 JSON 字符串中的换行符去掉，扁平化 JSON 字符串。
     * 同时将空对象 "{}" 替换为 "{ }"，以提高可读性。
     *
     * @param input 需要处理的 JSON 字符串
     * @return 处理后的 JSON 字符串
     */
    public static String flattenJson(String input) {
        return input.replaceAll("\\r?\\n", "").replace("{}", "{ }");
    }

    /**
     * 格式化日志信息。
     * 将 Map 格式的日志信息转换为 key=value 格式的字符串，
     * 其中的等号会被 URL 编码，null 值会被保留。
     *
     * @param info 日志信息 Map
     * @return 格式化后的日志信息字符串
     */
    public static String formatLogInfo(Map<String, Object> info) {
        return info.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + Optional.ofNullable(entry.getValue())
                        .map(Object::toString)
                        .orElse(null))
                .reduce("", (accumulator, actual) -> accumulator + actual + " ");
    }

    /**
     * 判断是否为不需要递归处理的简单类型。
     * 
     * <p>简单类型包括：</p>
     * <ul>
     *     <li>null值</li>
     *     <li>基本数据类型及其包装类</li>
     *     <li>字符串</li>
     *     <li>布尔值</li>
     *     <li>字符</li>
     *     <li>日期时间类型</li>
     *     <li>枚举类型</li>
     * </ul>
     *
     * @param obj 待检查的对象
     * @return 如果是简单类型返回true，否则返回false
     */
    private static boolean isSimpleType(Object obj) {
        return obj == null ||
                obj.getClass().isPrimitive() ||
                obj instanceof Number ||
                obj instanceof String ||
                obj instanceof Boolean ||
                obj instanceof Character ||
                obj instanceof Date ||
                obj instanceof Calendar ||
                obj instanceof Temporal ||
                obj instanceof Enum;
    }

    /**
     * 处理简单类型的值。
     * 
     * <p>处理规则：</p>
     * <ul>
     *     <li>null值：直接返回null</li>
     *     <li>数值类型和布尔类型：直接返回原值</li>
     *     <li>其他简单类型：转换为字符串</li>
     * </ul>
     *
     * @param obj 待处理的简单类型对象
     * @return 处理后的值
     */
    private static Object processSimpleValue(Object obj) {
        if (obj == null) {
            return null;
        }
        // 数值类型直接返回
        if (obj instanceof Number || obj instanceof Boolean) {
            return obj;
        }
        // 其他简单类型转为字符串
        return obj.toString();
    }

    /**
     * 递归处理对象，对需要掩码的字段进行处理。
     * 
     * <p>处理规则：</p>
     * <ul>
     *     <li>null值：返回null</li>
     *     <li>超过最大深度：返回[NESTED TOO DEEP]</li>
     *     <li>循环引用：返回[CIRCULAR REFERENCE]</li>
     *     <li>简单类型：调用processSimpleValue处理</li>
     *     <li>集合类型：递归处理每个元素</li>
     *     <li>数组类型：递归处理每个元素</li>
     *     <li>Map类型：递归处理每个值</li>
     *     <li>普通对象：递归处理每个字段</li>
     * </ul>
     *
     * @param obj 待处理的对象
     * @param depth 当前处理深度
     * @param visited 已访问对象的集合，用于检测循环引用
     * @return 处理后的对象
     */
    private static Object processObject(Object obj, int depth, Set<Object> visited) {
        if (obj == null) {
            return null;
        }

        // 防止递归过深
        if (depth > MAX_DEPTH) {
            return "[NESTED TOO DEEP]";
        }

        // 优先判断简单类型，避免不必要的处理
        if (isSimpleType(obj)) {
            return processSimpleValue(obj);
        }

        // 检测循环引用
        if (visited.contains(obj)) {
            return "[CIRCULAR REFERENCE]";
        }

        // 将对象添加到已访问集合
        visited.add(obj);

        try {
            // 处理集合类型
            if (obj instanceof Collection<?>) {
                return ((Collection<?>) obj).stream()
                        .map(item -> processObject(item, depth + 1, visited))
                        .collect(Collectors.toList());
            }

            // 处理数组类型
            if (obj.getClass().isArray()) {
                Object[] array = (Object[]) obj;
                return Arrays.stream(array)
                        .map(item -> processObject(item, depth + 1, visited))
                        .toArray();
            }

            // 处理 Map 类型
            if (obj instanceof Map<?, ?> map) {
                Map<Object, Object> processedMap = new HashMap<>();
                map.forEach((key, value) -> 
                    processedMap.put(key, processObject(value, depth + 1, visited))
                );
                return processedMap;
            }

            // 处理普通对象
            Map<String, Object> map = new HashMap<>();
            for (Field field : getAllFields(obj.getClass())) {
                field.setAccessible(true);
                try {
                    Object value = field.get(obj);
                    if (null == value) {
                        continue;
                    }
                    if (field.isAnnotationPresent(MaskedField.class)) {
                        map.put(field.getName(), "***");
                    } else {
                        map.put(field.getName(), processObject(value, depth + 1, visited));
                    }
                } catch (Exception ignored) {
                }
            }
            return map;
        } finally {
            visited.remove(obj);
        }
    }

    /**
     * 获取类的所有字段，包括父类的字段。
     * 
     * <p>获取规则：</p>
     * <ul>
     *     <li>获取当前类的所有字段</li>
     *     <li>递归获取父类的所有字段</li>
     *     <li>直到Object类为止</li>
     * </ul>
     *
     * @param clazz 需要获取字段的类
     * @return 包含所有字段的列表
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 对对象中标记了 {@link cn.com.chinastock.cnf.core.log.aspect.MaskedField} 注解的字段进行掩码处理。
     * 
     * <p>支持处理的数据类型：</p>
     * <ul>
     *     <li>基本数据类型及其包装类</li>
     *     <li>字符串</li>
     *     <li>日期时间类型</li>
     *     <li>枚举类型</li>
     *     <li>集合类型（List、Set等）</li>
     *     <li>Map类型</li>
     *     <li>数组</li>
     *     <li>自定义对象</li>
     * </ul>
     * 
     * <p>特殊处理：</p>
     * <ul>
     *     <li>循环引用：输出 [CIRCULAR REFERENCE]</li>
     *     <li>超过最大深度：输出 [NESTED TOO DEEP]</li>
     *     <li>敏感字段：输出 ***</li>
     * </ul>
     *
     * @param obj 需要处理的对象
     * @return 处理后的 JSON 字符串，如果处理失败则返回原对象的 JSON 字符串
     */
    public static String maskSensitiveFields(Object obj) {
        try {
            if (obj == null) {
                return null;
            }
            return JSON.toJSONString(processObject(obj, 0, new HashSet<>()));
        } catch (Exception e) {
            return JSON.toJSONString(obj);
        }
    }

    /**
     * 处理响应体对象，将特殊类型的响应体转换为合适的格式。
     * 
     * <p>处理规则：</p>
     * <ul>
     *     <li>如果是字节数组且内容类型是可记录的：转换为字符串</li>
     *     <li>其他情况：直接返回原对象</li>
     * </ul>
     *
     * @param body 响应体对象
     * @param contentType 内容类型
     * @return 处理后的响应体对象，如果是字节数组且内容类型符合要求，则返回转换后的字符串，否则返回原对象
     */
    public static Object processResponseBody(Object body, MediaType contentType) {
        if (body instanceof byte[] && contentType != null && isRecordableContentType(contentType.toString())) {
            try {
                return new String((byte[]) body, StandardCharsets.UTF_8);
            } catch (Exception ignored) {
            }
        }
        return body;
    }
}