## Galaxy Boot Pool


HikariCP 是 [Spring Boot 3.x 的默认连接池](https://docs.spring.io/spring-boot/reference/data/sql.html#data.sql.datasource.connection-pool)，
而 Druid 是国内企业级项目中使用较多的连接池。

**总览对比**

基于性能、安全性、数据完整性及社区活跃度的总体对比如下：

| **比较维度**  | **HikariCP**                       | **Druid**                            |
|-----------|------------------------------------|--------------------------------------|
| **性能**    | - 性能极高，延迟低<br>- 适合高并发场景            | - 性能较高，但通常稍逊于 HikariCP               |
| **稳定性**   | - 稳定性强<br>- 经过严格测试                 | - 稳定性也较高<br>- 广泛应用于国内互联网企业           |
| 国产数据库支持   | - 支持 OceanBase 数据库                 | - 支持 OceanBase 数据库                   |
| **内存占用**  | - 内存占用小，设计轻量级                      | - 内存占用相对较高                           |
| **功能丰富性** | - 专注于高性能和核心功能<br>- 功能较少            | - 功能丰富：内置 SQL 监控、慢查询分析、防止 SQL 注入等    |
| **配置难度**  | - 配置简单，默认值优化良好                     | - 配置稍复杂，功能选项较多                       |
| **监控能力**  | - 提供基础的监控指标，通过 JMX 或 Micrometer 集成 | - 内置强大的监控面板，支持慢查询、SQL 执行统计、Web 界面查看  |
| **社区支持**  | - 国际化社区活跃<br>- Spring Boot 默认连接池   | - 国内使用广泛<br>- 社区活跃，文档全面              |
| **适用场景**  | - 高性能、高并发场景<br>- 需要极低连接池开销的应用      | - 需要强大的监控能力和 SQL 管理的场景<br>- 中大型企业级项目 |

HikariCP 的 metric

- Micrometer 集成：[micrometer](https://github.com/brettwooldridge/HikariCP/tree/dev/src/main/java/com/zaxxer/hikari/metrics/micrometer)
- Dropwizard 集成：[dropwizard](https://github.com/brettwooldridge/HikariCP/tree/dev/src/main/java/com/zaxxer/hikari/metrics/dropwizard)
- Prometheus 集成：[prometheus](https://github.com/brettwooldridge/HikariCP/tree/dev/src/main/java/com/zaxxer/hikari/metrics/prometheus)

### **推荐选择**

1. **选择 HikariCP：**
    - 追求高性能和低延迟的项目，如微服务、高并发场景。
    - 对连接池的监控要求不高，或已有独立的监控体系（如 Prometheus）。
2. **选择 Druid：**
    - 需要内置强大监控能力的场景，如需要 SQL 分析、慢查询、执行时间统计等。
    - 国内企业级项目，尤其是数据分析相关系统。
    - 需要丰富的功能支持（如黑白名单、SQL 防护等）。

### 性能测试

OpenJDK [JMH](https://github.com/openjdk/jmh)（Java Microbenchmark Harness）

### OceanBase 支持

- [HikariCP 连接池配置示例](https://www.oceanbase.com/docs/common-oceanbase-database-cn-1000000000033991)

