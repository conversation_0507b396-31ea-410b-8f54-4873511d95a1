# Galaxy Boot Security WebFlux 用户手册

## 概述

Galaxy Boot Security WebFlux 是专为 Spring WebFlux 响应式应用设计的安全框架，提供了完整的安全防护功能，包括 XSS 防护、CSRF 防护、CORS 配置等。

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-security-webflux</artifactId>
</dependency>
```

### 2. 基础配置

```yaml
galaxy:
  security:
    input-protect: true   # 启用输入防护
    output-protect: true  # 启用输出防护
```

### 3. 创建 WebFlux 应用

```java
@SpringBootApplication
public class WebFluxSecurityApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebFluxSecurityApplication.class, args);
    }
}
```

## 功能详解

### XSS 防护

#### 输入防护

自动过滤以下内容中的 XSS 攻击代码：
- 请求参数
- 请求头
- JSON 请求体

```yaml
galaxy:
  security:
    input-protect: true  # 启用输入防护
```

**支持的过滤规则**：
- `<script>` 标签及其内容
- `javascript:` 协议
- `on*` 事件处理器
- `<iframe>`, `<object>`, `<embed>` 等危险标签

#### 输出防护

对响应体进行 XSS 过滤：

```yaml
galaxy:
  security:
    output-protect: true  # 启用输出防护
```

**支持的响应类型**：
- JSON 对象（FastJson 和 Jackson）
- 字符串响应
- Mono/Flux 响应式流



### CSRF 防护

#### 基础配置

```yaml
galaxy:
  security:
    csrf:
      protect: true  # 启用 CSRF 防护
```

#### 高级配置

```yaml
galaxy:
  security:
    csrf:
      protect: true
      whitelist:  # 白名单：不需要 CSRF 保护的路径
        - /api/public/**
        - /health/**
      blacklist:  # 黑名单：强制需要 CSRF 保护的路径
        - /api/admin/**
        - /api/sensitive/**
```

**匹配规则**：
1. 优先检查黑名单，匹配则需要 CSRF 保护
2. 然后检查白名单，匹配则跳过 CSRF 保护
3. 默认情况下需要 CSRF 保护

### CORS 配置

#### 启用 CORS

```yaml
galaxy:
  security:
    cors:
      protect: true
      whitelist:
        - https://*.chinastock.com.cn
        - https://localhost:3000
```

#### 配置说明

- `protect: true`：启用 CORS 支持
- `whitelist`：允许的源域名列表
- 支持通配符匹配（如 `*.example.com`）
- 如果不配置白名单，默认允许所有域名

### Actuator 保护

#### IP 白名单保护

```yaml
galaxy:
  security:
    actuator:
      protect: true
      whitelist:
        - 127.0.0.1          # 单个 IPv4 地址
        - ***********/24     # IPv4 CIDR 网段
        - ::1                # IPv6 地址
        - 10.0.0.0/8         # 大型内网网段
```

**IP 白名单特性**：
- 支持单个 IP 地址配置
- 支持 IP 网段配置（如 ***********/24）
- 支持多个白名单 IP 配置

#### HTTP Basic 认证

```yaml
galaxy:
  security:
    actuator:
      protect: true  # 不配置 whitelist 时使用 Basic 认证
    user:
      name: admin
      password: secret
```

### 用户认证

#### 默认用户配置

```yaml
galaxy:
  security:
    user:
      name: admin
      password: P@ssw0rd
```

#### 自定义用户服务

```java
@Bean
public ReactiveUserDetailsService customUserDetailsService() {
    UserDetails user1 = User.withUsername("user1")
            .password(passwordEncoder().encode("password1"))
            .roles("USER")
            .build();
    
    UserDetails admin = User.withUsername("admin")
            .password(passwordEncoder().encode("admin123"))
            .roles("USER", "ADMIN")
            .build();
    
    return new MapReactiveUserDetailsService(user1, admin);
}
```
