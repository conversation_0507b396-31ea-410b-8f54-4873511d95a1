package cn.com.chinastock.cnf.security.webflux.filter;

import cn.com.chinastock.cnf.security.webflux.handler.SecurityHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * SecurityWebFilter 测试
 * 
 * <AUTHOR> Boot Team
 */
class SecurityWebFilterTest {

    private SecurityWebFilter securityWebFilter;
    private WebFilterChain mockChain;
    private SecurityHandler mockHandler1;
    private SecurityHandler mockHandler2;

    @BeforeEach
    void setUp() {
        mockChain = mock(WebFilterChain.class);
        mockHandler1 = mock(SecurityHandler.class);
        mockHandler2 = mock(SecurityHandler.class);
        
        List<SecurityHandler> handlers = Arrays.asList(mockHandler1, mockHandler2);
        securityWebFilter = new SecurityWebFilter(handlers);
        
        when(mockChain.filter(any())).thenReturn(Mono.empty());
    }

    @Test
    void shouldApplyAllHandlersInOrder() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/test")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        ServerWebExchange modifiedExchange1 = exchange.mutate()
                .request(request.mutate().header("X-Handler-1", "applied").build())
                .build();
        ServerWebExchange modifiedExchange2 = modifiedExchange1.mutate()
                .request(request.mutate().header("X-Handler-2", "applied").build())
                .build();
        
        when(mockHandler1.handle(exchange)).thenReturn(Mono.just(modifiedExchange1));
        when(mockHandler2.handle(modifiedExchange1)).thenReturn(Mono.just(modifiedExchange2));

        // When & Then
        StepVerifier.create(securityWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldHaveCorrectOrder() {
        // Then
        assertThat(securityWebFilter.getOrder()).isEqualTo(-2147483646); // HIGHEST_PRECEDENCE + 2
    }

    @Test
    void shouldHandleEmptyHandlersList() {
        // Given
        SecurityWebFilter emptyFilter = new SecurityWebFilter(Arrays.asList());
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/test")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(emptyFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldHandleHandlerError() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/test")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(mockHandler1.handle(exchange)).thenReturn(Mono.error(new RuntimeException("Handler error")));

        // When & Then
        StepVerifier.create(securityWebFilter.filter(exchange, mockChain))
                .expectError(RuntimeException.class)
                .verify();
    }
}
