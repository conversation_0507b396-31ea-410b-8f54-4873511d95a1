package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.security.webflux.filter.SecurityWebFilter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ReactiveWebApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Galaxy Security WebFlux 自动配置测试
 * 
 * 
 */
class GalaxySecurityWebfluxAutoConfigurationTest {

    private final ReactiveWebApplicationContextRunner contextRunner = new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxySecurityWebfluxAutoConfiguration.class));

    @Test
    void shouldCreateSecurityWebFilterChain() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SecurityWebFilterChain.class);
            assertThat(context).hasSingleBean(PasswordEncoder.class);
        });
    }

    @Test
    void shouldCreateSecurityWebFilterWhenOutputProtectEnabled() {
        contextRunner
                .withUserConfiguration(TestWebFluxConfiguration.class)
                .withPropertyValues("galaxy.security.output-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(SecurityWebFilter.class);
                });
    }

    @Test
    void shouldCreateSecurityWebFilterWhenInputProtectEnabled() {
        contextRunner
                .withUserConfiguration(TestWebFluxConfiguration.class)
                .withPropertyValues("galaxy.security.input-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(SecurityWebFilter.class);
                });
    }

    @Test
    void shouldNotCreateSecurityWebFilterWhenBothProtectionsDisabled() {
        contextRunner
                .withUserConfiguration(TestWebFluxConfiguration.class)
                .withPropertyValues(
                        "galaxy.security.input-protect=false",
                        "galaxy.security.output-protect=false"
                )
                .run(context -> {
                    assertThat(context).doesNotHaveBean(SecurityWebFilter.class);
                });
    }


    @Test
    void shouldCreateReactiveUserDetailsServiceWhenUserConfigured() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.user.name=testuser",
                        "galaxy.security.user.password=testpass"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(org.springframework.security.core.userdetails.ReactiveUserDetailsService.class);
                });
    }

    @Test
    void shouldNotCreateReactiveUserDetailsServiceWhenUserNotConfigured() {
        contextRunner
                .run(context -> {
                    assertThat(context).doesNotHaveBean(org.springframework.security.core.userdetails.ReactiveUserDetailsService.class);
                });
    }

    /**
     * 测试配置类，提供 WebFlux 必需的 Bean
     */
    @Configuration
    static class TestWebFluxConfiguration {

        @Bean
        public ServerCodecConfigurer serverCodecConfigurer() {
            return ServerCodecConfigurer.create();
        }

        @Bean
        public RequestedContentTypeResolver requestedContentTypeResolver() {
            return new org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder().build();
        }
    }
}
