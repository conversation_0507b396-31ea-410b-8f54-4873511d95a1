package cn.com.chinastock.cnf.security.webflux.authorization;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ReactiveIpWhitelistAuthorizationManagerTest {

    private ReactiveIpWhitelistAuthorizationManager authorizationManager;
    private List<String> whitelist;

    @BeforeEach
    void setUp() {
        whitelist = Arrays.asList(
                "127.0.0.1",
                "***********/24",
                "::1"
        );
        authorizationManager = new ReactiveIpWhitelistAuthorizationManager(whitelist);
    }

    @Test
    void shouldAllowExactIpMatch() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .remoteAddress(new InetSocketAddress("127.0.0.1", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertTrue(decision.isGranted()))
                .verifyComplete();
    }

    @Test
    void shouldAllowCidrMatch() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .remoteAddress(new InetSocketAddress("*************", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertTrue(decision.isGranted()))
                .verifyComplete();
    }

    @Test
    void shouldDenyNonWhitelistedIp() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .remoteAddress(new InetSocketAddress("********", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertFalse(decision.isGranted()))
                .verifyComplete();
    }

    @Test
    void shouldAllowXForwardedForHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .header("X-Forwarded-For", "127.0.0.1, ********")
                .remoteAddress(new InetSocketAddress("********", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertTrue(decision.isGranted()))
                .verifyComplete();
    }

    @Test
    void shouldAllowXRealIpHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .header("X-Real-IP", "************")
                .remoteAddress(new InetSocketAddress("********", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertTrue(decision.isGranted()))
                .verifyComplete();
    }

    @Test
    void shouldAllowIpv6Address() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health")
                .remoteAddress(new InetSocketAddress("::1", 8080))
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        AuthorizationContext context = new AuthorizationContext(exchange);

        // When
        Mono<AuthorizationDecision> result = authorizationManager.check(Mono.empty(), context);

        // Then
        StepVerifier.create(result)
                .assertNext(decision -> assertTrue(decision.isGranted()))
                .verifyComplete();
    }
}
