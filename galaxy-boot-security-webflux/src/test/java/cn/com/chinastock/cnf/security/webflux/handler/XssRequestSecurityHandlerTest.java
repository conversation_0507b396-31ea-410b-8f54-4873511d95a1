package cn.com.chinastock.cnf.security.webflux.handler;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.test.StepVerifier;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * XssRequestSecurityHandler 测试
 * 
 * <AUTHOR> Boot Team
 */
class XssRequestSecurityHandlerTest {

    private XssRequestSecurityHandler handler;

    @BeforeEach
    void setUp() {
        handler = new XssRequestSecurityHandler();
    }

    @Test
    void shouldSkipStaticResources() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/static/app.js")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(handler.handle(exchange))
                .assertNext(result -> {
                    assertThat(result).isSameAs(exchange);
                })
                .verifyComplete();
    }

    @Test
    void shouldProcessNonStaticResources() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/test")
                .header("X-Test-Header", "<script>alert('xss')</script>")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(handler.handle(exchange))
                .assertNext(result -> {
                    assertThat(result).isNotSameAs(exchange);
                    // 验证请求头被过滤
                    String headerValue = result.getRequest().getHeaders().getFirst("X-Test-Header");
                    assertThat(headerValue).contains("&lt;script&gt;");
                })
                .verifyComplete();
    }

    @Test
    void shouldSkipCssFiles() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/styles/main.css")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(handler.handle(exchange))
                .assertNext(result -> {
                    assertThat(result).isSameAs(exchange);
                })
                .verifyComplete();
    }

    @Test
    void shouldSkipImageFiles() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/images/logo.png")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(handler.handle(exchange))
                .assertNext(result -> {
                    assertThat(result).isSameAs(exchange);
                })
                .verifyComplete();
    }

    @Test
    void shouldProcessJsonRequest() {
        // Given
        String jsonBody = "{\"message\":\"<script>alert('xss')</script>\"}";
        MockServerHttpRequest request = MockServerHttpRequest
                .post("/api/test")
                .contentType(MediaType.APPLICATION_JSON)
                .body(jsonBody);
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(handler.handle(exchange))
                .assertNext(result -> {
                    assertThat(result).isNotSameAs(exchange);
                    // 验证请求体会被处理（具体的 JSON 过滤逻辑在装饰器中）
                })
                .verifyComplete();
    }
}
