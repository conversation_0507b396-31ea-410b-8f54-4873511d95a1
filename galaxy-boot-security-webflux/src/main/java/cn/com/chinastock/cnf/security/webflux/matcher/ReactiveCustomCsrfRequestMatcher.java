package cn.com.chinastock.cnf.security.webflux.matcher;

import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatcher;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * 响应式 CSRF 请求匹配器
 * 
 * <p>该匹配器用于在 WebFlux 环境下判断请求是否需要 CSRF 保护：</p>
 * <ul>
 *     <li>黑名单中的路径强制需要 CSRF 保护</li>
 *     <li>白名单中的路径跳过 CSRF 保护</li>
 *     <li>其他路径默认需要 CSRF 保护</li>
 * </ul>
 */
public class ReactiveCustomCsrfRequestMatcher implements ServerWebExchangeMatcher {
    
    private final List<String> whitelistPatterns = new ArrayList<>();
    private final List<String> blacklistPatterns = new ArrayList<>();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 构造函数
     *
     * @param whitelist 白名单路径模式列表（跳过 CSRF 保护）
     * @param blacklist 黑名单路径模式列表（强制 CSRF 保护）
     */
    public ReactiveCustomCsrfRequestMatcher(List<String> whitelist, List<String> blacklist) {
        if (whitelist != null) {
            this.whitelistPatterns.addAll(whitelist);
        }
        if (blacklist != null) {
            this.blacklistPatterns.addAll(blacklist);
        }
    }

    @Override
    public Mono<MatchResult> matches(ServerWebExchange exchange) {
        String requestPath = exchange.getRequest().getURI().getPath();
        
        // 检查黑名单：如果匹配黑名单，需要 CSRF 保护
        for (String pattern : blacklistPatterns) {
            if (pathMatcher.match(pattern, requestPath)) {
                return MatchResult.match();
            }
        }
        
        // 检查白名单：如果匹配白名单，跳过 CSRF 保护
        for (String pattern : whitelistPatterns) {
            if (pathMatcher.match(pattern, requestPath)) {
                return MatchResult.notMatch();
            }
        }
        
        // 默认需要 CSRF 保护
        return MatchResult.match();
    }
}
