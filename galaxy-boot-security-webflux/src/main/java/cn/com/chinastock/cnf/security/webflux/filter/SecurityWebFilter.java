package cn.com.chinastock.cnf.security.webflux.filter;

import cn.com.chinastock.cnf.security.webflux.handler.SecurityHandler;
import org.springframework.core.Ordered;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 统一安全 Web 过滤器
 * 
 * <p>该过滤器整合了多个安全处理器，提供统一的安全防护功能，包括：</p>
 * <ul>
 *     <li>XSS 请求过滤</li>
 *     <li>XSS 响应过滤</li>
 *     <li>其他安全处理（可扩展）</li>
 * </ul>
 * 
 * <p>通过组合多个 {@link SecurityHandler} 实现，遵循单一职责原则和开闭原则</p>
 * 
 * <AUTHOR> Boot Team
 */
public class SecurityWebFilter implements WebFilter, Ordered {
    
    private final List<SecurityHandler> securityHandlers;
    
    public SecurityWebFilter(List<SecurityHandler> securityHandlers) {
        this.securityHandlers = securityHandlers;
    }
    
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 2;
    }
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        // 依次应用所有安全处理器
        return applySecurityHandlers(exchange, 0)
                .flatMap(processedExchange -> chain.filter(processedExchange));
    }
    
    /**
     * 递归应用安全处理器
     * 
     * @param exchange 当前的交换对象
     * @param index 当前处理器索引
     * @return 处理后的交换对象
     */
    private Mono<ServerWebExchange> applySecurityHandlers(ServerWebExchange exchange, int index) {
        if (index >= securityHandlers.size()) {
            return Mono.just(exchange);
        }
        
        SecurityHandler handler = securityHandlers.get(index);
        return handler.handle(exchange)
                .flatMap(processedExchange -> applySecurityHandlers(processedExchange, index + 1));
    }
}
