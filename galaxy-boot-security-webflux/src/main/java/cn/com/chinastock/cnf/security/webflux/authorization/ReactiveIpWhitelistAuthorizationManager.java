package cn.com.chinastock.cnf.security.webflux.authorization;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

/**
 * 响应式 IP 白名单授权管理器
 *
 * <p>该授权管理器用于在 WebFlux 环境下实现 IP 白名单访问控制，支持：</p>
 * <ul>
 *     <li>单个 IP 地址匹配</li>
 *     <li>CIDR 网段匹配</li>
 *     <li>IPv4 和 IPv6 地址支持</li>
 *     <li>X-Forwarded-For 和 X-Real-IP 头部支持</li>
 * </ul>
 */
public class ReactiveIpWhitelistAuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(ReactiveIpWhitelistAuthorizationManager.class);

    // HTTP 头部常量
    private static final String X_FORWARDED_FOR_HEADER = "X-Forwarded-For";
    private static final String X_REAL_IP_HEADER = "X-Real-IP";

    // IP 相关常量
    private static final String UNKNOWN_IP = "unknown";
    private static final String CIDR_SEPARATOR = "/";
    private static final String IP_SEPARATOR = ",";

    // 位操作常量
    private static final int BYTE_MASK = 0xFF; // 字节掩码，用于位运算
    private static final int BITS_PER_BYTE = 8; // 每字节位数

    private final List<String> whitelist;

    public ReactiveIpWhitelistAuthorizationManager(List<String> whitelist) {
        this.whitelist = whitelist;
    }

    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext context) {
        return Mono.fromCallable(() -> {
            String clientIp = getClientIpAddress(context);
            boolean allowed = isIpAllowed(clientIp);
            
            if (allowed) {
                logger.debug(LogCategory.FRAMEWORK_LOG, "IP {} is allowed by whitelist", clientIp);
            } else {
                logger.warn(LogCategory.FRAMEWORK_LOG, "IP {} is not in whitelist, access denied", clientIp);
            }
            
            return new AuthorizationDecision(allowed);
        });
    }

    private String getClientIpAddress(AuthorizationContext context) {
        var request = context.getExchange().getRequest();

        // 优先从 X-Forwarded-For 头获取（代理服务器设置）
        String xForwardedFor = request.getHeaders().getFirst(X_FORWARDED_FOR_HEADER);
        if (StringUtils.hasText(xForwardedFor)) {
            String[] ips = xForwardedFor.split(IP_SEPARATOR);
            if (ips.length > 0) {
                return ips[0].trim();
            }
        }

        // 从 X-Real-IP 头获取（Nginx 等设置）
        String xRealIp = request.getHeaders().getFirst(X_REAL_IP_HEADER);
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp.trim();
        }

        // 从远程地址获取
        var remoteAddress = request.getRemoteAddress();
        if (remoteAddress != null) {
            return remoteAddress.getAddress().getHostAddress();
        }

        return UNKNOWN_IP;
    }

    private boolean isIpAllowed(String clientIp) {
        if (!StringUtils.hasText(clientIp) || UNKNOWN_IP.equals(clientIp)) {
            return false;
        }

        return whitelist.stream().anyMatch(allowedIp -> matchesIpPattern(clientIp, allowedIp));
    }

    private boolean matchesIpPattern(String clientIp, String pattern) {
        try {
            if (pattern.contains(CIDR_SEPARATOR)) {
                return matchesCidr(clientIp, pattern);
            } else {
                return normalizeIpAddress(clientIp).equals(normalizeIpAddress(pattern));
            }
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to match IP pattern: {} against {}", clientIp, pattern, e);
            return false;
        }
    }

    private String normalizeIpAddress(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.getHostAddress();
        } catch (UnknownHostException e) {
            return ip;
        }
    }

    private boolean matchesCidr(String clientIp, String cidr) throws UnknownHostException {
        String[] parts = cidr.split(CIDR_SEPARATOR);
        if (parts.length != 2) {
            return false;
        }

        InetAddress clientAddress = InetAddress.getByName(clientIp);
        InetAddress networkAddress = InetAddress.getByName(parts[0]);
        int prefixLength = Integer.parseInt(parts[1]);

        // 检查地址族是否匹配（IPv4 vs IPv6）
        if (clientAddress.getAddress().length != networkAddress.getAddress().length) {
            return false;
        }

        byte[] clientBytes = clientAddress.getAddress();
        byte[] networkBytes = networkAddress.getAddress();

        int bytesToCompare = prefixLength / BITS_PER_BYTE;
        int bitsToCompare = prefixLength % BITS_PER_BYTE;

        // 比较完整字节
        for (int i = 0; i < bytesToCompare; i++) {
            if (clientBytes[i] != networkBytes[i]) {
                return false;
            }
        }

        // 比较剩余位（如果有）
        if (bitsToCompare > 0 && bytesToCompare < clientBytes.length) {
            int mask = BYTE_MASK << (BITS_PER_BYTE - bitsToCompare);
            return (clientBytes[bytesToCompare] & mask) == (networkBytes[bytesToCompare] & mask);
        }

        return true;
    }
}
