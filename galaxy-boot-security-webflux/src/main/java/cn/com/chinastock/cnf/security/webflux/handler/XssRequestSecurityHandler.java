package cn.com.chinastock.cnf.security.webflux.handler;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.input.XssSanitizerUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;

/**
 * XSS 请求安全处理器
 *
 * <p>负责处理请求中的 XSS 防护，包括：</p>
 * <ul>
 *     <li>请求参数 XSS 过滤</li>
 *     <li>请求头 XSS 过滤</li>
 *     <li>请求体 XSS 过滤（JSON 格式）</li>
 *     <li>静态资源跳过处理</li>
 * </ul>
 *
 * <AUTHOR> Boot Team
 */
public class XssRequestSecurityHandler implements SecurityHandler {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(XssRequestSecurityHandler.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 静态资源路径前缀 - 用于快速路径匹配
    private static final String[] STATIC_RESOURCE_PREFIXES = {
            "/static/", "/assets/", "/public/", "/resources/", "/webjars/", "/css/", "/js/", "/images/", "/img/"
    };

    // 预编译的静态资源正则表达式
    private static final Pattern STATIC_RESOURCE_PATTERN = Pattern.compile(
            ".*\\.(css|js|png|jpe?g|gif|ico|svg|woff2?|ttf|eot|mp[34]|avi|mov|pdf|xlsx?|docx?|pptx?|zip|rar)$",
            Pattern.CASE_INSENSITIVE
    );

    // 路径匹配结果缓存
    private static final ConcurrentMap<String, Boolean> STATIC_RESOURCE_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_PATH_CACHE_SIZE = 500;

    @Override
    public Mono<ServerWebExchange> handle(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();

        // 跳过静态资源
        if (isStaticResource(request.getURI().getPath())) {
            return Mono.just(exchange);
        }

        // 创建 XSS 防护的请求装饰器
        XssServerHttpRequestDecorator xssRequest = new XssServerHttpRequestDecorator(request);
        return Mono.just(exchange.mutate().request(xssRequest).build());
    }

    /**
     * 优化的静态资源判断方法
     *
     * @param path 请求路径
     * @return 如果是静态资源返回 true，否则返回 false
     */
    private boolean isStaticResource(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }

        // 检查缓存
        Boolean cached = STATIC_RESOURCE_CACHE.get(path);
        if (cached != null) {
            return cached;
        }

        boolean isStatic = performStaticResourceCheck(path);

        // 更新缓存
        if (STATIC_RESOURCE_CACHE.size() < MAX_PATH_CACHE_SIZE) {
            STATIC_RESOURCE_CACHE.put(path, isStatic);
        }

        return isStatic;
    }

    /**
     * 执行实际的静态资源检查
     *
     * @param path 请求路径
     * @return 如果是静态资源返回 true，否则返回 false
     */
    private boolean performStaticResourceCheck(String path) {
        // 1. 首先检查路径前缀（最快）
        for (String prefix : STATIC_RESOURCE_PREFIXES) {
            if (path.startsWith(prefix)) {
                return true;
            }
        }

        // 2. 使用预编译的正则表达式检查扩展名
        return STATIC_RESOURCE_PATTERN.matcher(path).matches();
    }

    /**
     * XSS 防护的 ServerHttpRequest 装饰器
     */
    private static class XssServerHttpRequestDecorator extends ServerHttpRequestDecorator {

        private final ServerHttpRequest delegate;
        private final DataBufferFactory dataBufferFactory;

        public XssServerHttpRequestDecorator(ServerHttpRequest delegate) {
            super(delegate);
            this.delegate = delegate;
            // 使用默认的 DataBufferFactory，避免阻塞操作
            this.dataBufferFactory = org.springframework.core.io.buffer.DefaultDataBufferFactory.sharedInstance;
        }

        @Override
        public HttpHeaders getHeaders() {
            HttpHeaders originalHeaders = super.getHeaders();
            HttpHeaders sanitizedHeaders = new HttpHeaders();

            originalHeaders.forEach((name, values) -> {
                values.forEach(value -> {
                    String sanitizedValue = XssSanitizerUtil.stripXSS(value);
                    sanitizedHeaders.add(name, sanitizedValue);
                });
            });

            return sanitizedHeaders;
        }

        @Override
        public Flux<DataBuffer> getBody() {
            MediaType contentType = delegate.getHeaders().getContentType();

            // 只处理 JSON 类型的请求体
            if (contentType == null || !MediaType.APPLICATION_JSON.isCompatibleWith(contentType)) {
                return super.getBody();
            }

            return super.getBody()
                    .collectList()
                    .flatMapMany(dataBuffers -> {
                        if (dataBuffers.isEmpty()) {
                            return Flux.empty();
                        }

                        return sanitizeJsonBody(dataBuffers);
                    });
        }

        /**
         * 对 JSON 请求体进行 XSS 过滤
         *
         * @param dataBuffers 数据缓冲区列表
         * @return 过滤后的数据缓冲区流
         */
        private Flux<DataBuffer> sanitizeJsonBody(java.util.List<DataBuffer> dataBuffers) {
            return Mono.fromCallable(() -> {
                        // 合并所有 DataBuffer
                        DataBuffer joinedBuffer = dataBufferFactory.join(dataBuffers);

                        try {
                            // 读取 JSON 内容
                            byte[] bytes = new byte[joinedBuffer.readableByteCount()];
                            joinedBuffer.read(bytes);
                            String jsonContent = new String(bytes, StandardCharsets.UTF_8);

                            if (jsonContent.trim().isEmpty()) {
                                return dataBufferFactory.wrap(bytes);
                            }

                            // 解析并过滤 JSON
                            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonContent);
                            JsonNode sanitizedNode = sanitizeJsonNode(jsonNode);
                            String sanitizedJson = OBJECT_MAPPER.writeValueAsString(sanitizedNode);

                            return dataBufferFactory.wrap(sanitizedJson.getBytes(StandardCharsets.UTF_8));

                        } catch (Exception e) {
                            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to sanitize JSON body, using original content", e);
                            // 如果解析失败，返回原始内容
                            byte[] bytes = new byte[joinedBuffer.readableByteCount()];
                            joinedBuffer.read(bytes);
                            return dataBufferFactory.wrap(bytes);
                        } finally {
                            // 释放原始 DataBuffer
                            DataBufferUtils.release(joinedBuffer);
                            dataBuffers.forEach(DataBufferUtils::release);
                        }
                    })
                    .flux()
                    .cast(DataBuffer.class);
        }

        /**
         * 递归过滤 JSON 节点
         *
         * @param node JSON 节点
         * @return 过滤后的 JSON 节点
         */
        private JsonNode sanitizeJsonNode(JsonNode node) {
            if (node == null) {
                return null;
            }

            if (node.isTextual()) {
                String sanitizedText = XssSanitizerUtil.stripXSS(node.asText());
                return new TextNode(sanitizedText);
            } else if (node.isObject()) {
                ObjectNode objectNode = OBJECT_MAPPER.createObjectNode();
                node.fields().forEachRemaining(entry -> {
                    String key = entry.getKey();
                    JsonNode value = entry.getValue();
                    objectNode.set(key, sanitizeJsonNode(value));
                });
                return objectNode;
            } else if (node.isArray()) {
                ArrayNode arrayNode = OBJECT_MAPPER.createArrayNode();
                for (JsonNode element : node) {
                    arrayNode.add(sanitizeJsonNode(element));
                }
                return arrayNode;
            }

            // 其他类型（数字、布尔值等）直接返回
            return node;
        }
    }
}
