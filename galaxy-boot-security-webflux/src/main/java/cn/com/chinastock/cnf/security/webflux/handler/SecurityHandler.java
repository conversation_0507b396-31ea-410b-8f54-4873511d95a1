package cn.com.chinastock.cnf.security.webflux.handler;

import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 安全处理器接口
 * 
 * <p>提供统一的安全处理抽象，用于处理各种安全相关的操作</p>
 * 
 * <AUTHOR> Boot Team
 */
public interface SecurityHandler {
    
    /**
     * 处理安全相关操作
     * 
     * @param exchange 服务器 Web 交换对象
     * @return 处理后的交换对象，如果不需要修改则返回原对象
     */
    Mono<ServerWebExchange> handle(ServerWebExchange exchange);
}
