# Galaxy Boot 开发框架

> 🚀 基于 Spring Boot 的企业级微服务开发框架，提供开箱即用的生产级组件

[![Maven Central](https://img.shields.io/maven-central/v/cn.com.chinastock/galaxy-boot-parent.svg)](https://search.maven.org/search?q=g:cn.com.chinastock%20AND%20a:galaxy-boot-parent)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.3.x-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![JDK](https://img.shields.io/badge/JDK-8%20%7C%2021-blue.svg)](https://openjdk.java.net/)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)

## ✨ 特性

- 🎯 **开箱即用** - 提供丰富的 Starter 组件，零配置快速启动
- 🏗️ **企业级** - 内置日志、监控、安全、链路追踪等生产级特性
- 🔧 **高度集成** - 深度集成 TongWeb、OceanBase、Redis、Kafka 等企业组件
- 📊 **可观测性** - 完整的监控指标、日志规范和链路追踪支持
- 🛡️ **安全可靠** - 内置安全框架和最佳实践
- 🚀 **高性能** - 优化的配置和组件选择，提供最佳性能

## 🚀 快速开始

### 5分钟创建第一个应用

**1. 创建项目**
```bash
mkdir my-galaxy-app && cd my-galaxy-app
```

**2. 创建 `pom.xml`**
```xml
<parent>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-parent</artifactId>
    <version>0.0.22</version>
    <relativePath/>
</parent>

<groupId>com.example</groupId>
<artifactId>my-galaxy-app</artifactId>
<version>1.0.0</version>

<dependencies>
    <dependency>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-starter-web</artifactId>
    </dependency>
</dependencies>
```

**3. 创建应用类**
```java
@SpringBootApplication
@RestController
public class Application {
    @GetMapping("/hello")
    public String hello() {
        return "Hello Galaxy Boot!";
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

**4. 启动应用**
```bash
mvn spring-boot:run
curl http://localhost:8080/hello
```

🎉 **完成！** 您的第一个 Galaxy Boot 应用已经运行起来了！

## 📚 文档

- 📖 [快速开始](docs/QuickStart.md) - 5分钟快速体验
- 📋 [完整文档](tmp/UserManual.md) - 详细使用指南
- 🎯 [示例项目](galaxy-boot-examples/) - 完整示例代码
- 💡 [开发规范](docs/development/Standard.md) - 最佳实践
- 🐛 [常见问题](docs/FAQ.md) - 问题解答
- 📝 [更新日志](docs/CHANGELOG.md) - 版本历史

## 🧩 核心组件

| 组件 | 功能 | 文档 |
|------|------|------|
| **galaxy-boot-core** | 核心功能：日志、异常、状态管理 | [📖](tmp/galaxy-boot-core.md) |
| **galaxy-boot-starter-web** | Web 应用开发 | [📖](tmp/galaxy-boot-starter-web.md) |
| **galaxy-boot-starter-redis** | Redis 缓存支持 | [📖](tmp/galaxy-boot-starter-redis.md) |
| **galaxy-boot-starter-feign** | 服务间调用 | [📖](tmp/galaxy-boot-starter-feign.md) |
| **galaxy-boot-starter-kafka** | 消息队列 | [📖](tmp/galaxy-boot-starter-kafka.md) |
| **galaxy-boot-security** | 安全框架 | [📖](tmp/galaxy-boot-security.md) |

[查看所有组件 →](tmp/UserManual.md#可用组件列表)

## 🏗️ 架构

```
Galaxy Boot Framework
├── galaxy-boot-core          # 核心功能
├── galaxy-boot-security      # 安全框架
├── galaxy-boot-utils         # 工具库
└── galaxy-boot-starters/     # Starter 组件
    ├── galaxy-boot-starter-web
    ├── galaxy-boot-starter-redis
    ├── galaxy-boot-starter-kafka
    ├── galaxy-boot-starter-feign
    └── ...
```

## 🔧 版本支持

| Galaxy Boot | Spring Boot | JDK | 状态 |
|-------------|-------------|-----|------|
| 0.0.22      | 3.3.x       | 21  | 🌟 推荐 |
| 0.0.22      | 2.7.x       | 8   | 🔧 维护 |

## 🤝 贡献

欢迎贡献代码！请查看 [开发规范](docs/development/Standard.md) 了解详细信息。

## 📄 许可证

本项目采用 Apache 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
