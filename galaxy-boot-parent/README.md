# Galaxy Boot Parent

Galaxy Boot 是一个基于 Spring Boot 3.x 的企业级应用开发框架，提供了一系列开箱即用的 Starter 组件，简化企业应用的开发流程。

## 技术栈

### 核心框架
- **Java**: 21
- **Spring Boot**: 3.3.7
- **Spring Cloud**: 2023.0.4
- **Spring AI**: 1.0.0-RC1

### Web 框架
- **Jakarta Servlet**: 6.1.0
- **Spring Boot Web**: 3.3.7
- **Spring Boot WebFlux**: 3.3.7

### 数据库相关
- **HikariCP**: 6.2.1 (连接池)
- **MySQL Connector**: 8.4.0
- **OceanBase Client**: 2.4.12
- **MyBatis**: 3.5.17
- **MyBatis Spring Boot Starter**: 3.0.4
- **MyBatisPlus**: 3.5.7

### 日志框架
- **SLF4J BOM**: 2.0.16
- **Log4j2 SLF4J Impl**: 2.23.1
- **Disruptor**: 3.4.4

### 缓存
- **Caffeine**: 3.1.8 (本地缓存)

### 消息队列
- **Kafka Clients**: 3.7.2

### API 文档
- **SpringDoc OpenAPI**: 2.6.0
- **Spring REST Docs**: 3.0.3

### 配置中心
- **Apollo Client**: 2.3.0

### 应用服务器
- **TongWeb**: 8.0.E.3

### 工具库
- **FastJSON2**: 2.0.53
- **Commons IO**: 2.18.0
- **ESB Auth**: 2.0.1
- **Micrometer Context Propagation**: 1.1.3

### 测试框架
- **JUnit Jupiter**: 5.10.2
- **AssertJ Core**: 3.26.3
- **JaCoCo**: 0.8.12
