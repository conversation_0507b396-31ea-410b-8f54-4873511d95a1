# Development

## 添加新模块

1. 创建对应的模块，如 `galaxy-boot-starters/galaxy-boot-starter-xxx`
2. 在 [galaxy-boot-dependencies](galaxy-boot-dependencies) 中添加新模块
3. 在 Confluence 上添加新模块的文档，并记录 pageId，将 pageId 记录到  `docs/Confluence.json`

## 发布

以 `revision=0.0.1` 为例

1. 修改版本号:
    - 修改 [galaxy-boot-dependencies/pom.xml](galaxy-boot-dependencies/pom.xml) 中的版本号: `0.0.1`
    - 修改 [galaxy-boot-parent/pom.xml](galaxy-boot-parent/pom.xml) 中的版本号: `0.0.1`
2. 创建分支: `git branch release/v0.0.1`
3. 提交分支：`git push origin release/v0.0.1`
4. 流水线手动触发: `发布到 Maven`（人工审核）

## 版本发布规范

版本格式：`主版本号.次版本号.修订版本号`，版本号递增规则如下：

### 主版本号（Major Version）

* **何时修改**：当进行不兼容的 API 修改时。例如：
    * 更新 JDK 8 到 JDK 11
    * 更新 Spring Boot 2 到 Spring Boot 3
    * 其他重大不兼容更改
* **示例**：从 `v1.0.0` 更新到 `v2.0.0`。

### 次版本号（Minor Version）

* **何时修改**：当添加了新功能且保持向后兼容时。
    * **发布节奏**：尽可能控制次版本号的发布节奏，推荐多个相关 starter 组合发布。
    * **特殊情况**：对于影响范围广（如 log4j 安全漏洞）的紧急修复，可以直接通过次版本号发布。
    * **功能更新累积**：建议将 bugfix 和功能更新积累到次版本发布。
* **示例**：从 `v1.0.0` 更新到 `v1.1.0`。

### 修订版本号（Patch Version）

* **何时修改**：当进行向后兼容的问题修复时。
    * **限制**：修订版本仅对内部 consumer 可见，不对外部 consumer 公布。
* **示例**：从 `v1.1.0` 更新到 `v1.1.1`。

### 组件版本管理

* **范围确定**：`1.0.0` 版本涵盖 `core` 和必要的 starter。
* **功能稳定性**：
    * Core 功能相对稳定。
    * Starter 的变化更多源于其第三方依赖变化。
* **组件版本一致性**：
    * 各组件版本应与所依赖的 Galaxy Boot 版本保持一致。
    * 一个 Boot 版本内，相关组件的版本相同。

### 消费端版本升级规范

* **何时升级**：
    * 使用新功能，尤其是新增 starter。
    * 强制升级场景：如安全问题修复（如 log4j）、现有组件的必要 bug 修复。
    * 试点新组件。

### 版本使用统计

* 定期统计不同版本的 Galaxy Boot 使用情况，以支持版本管理决策。

相关文档：《语义化版本规范》 [https://semver.org/lang/zh-CN/](https://semver.org/lang/zh-CN/)

## 注释代码规范

### 配置类代码（待进一步补充）

配置类代码注释主要是指各类 `starter`，开发者直接接触不到代码时，注释参考银河统一的 JavaDoc 规范即可。如类级别：

```java
/**
 * 类简述
 * <p>详细描述</p>
 * <AUTHOR>
 * @date 2024-11-28
 */
```

### 工具类代码

框架代码注释主要从开发体验的角度出发，开发人员在使用框架时，能够快速了解框架的功能和使用方法。建议：将示例代码和方法说明写在类级和方法级注释中。

类级代码示例：

```java
/**
 * IdUtils 提供用于生成各种长度的 UUID 功能：8 位、16 位、32 位、64 位或 128 位。
 *
 * <p>该类中包含以下静态方法：</p>
 *
 * <ul>
 *     <li>{@link #getId(ID_LEN)}：根据指定的长度生成 UUID。</li>
 * </ul>
 *
 * 使用示例：
 * <pre>
 *     {@code
 *       String id = IdUtils.getId(ID_LEN.LEN32);
 *     }
 * </pre>
 *
 * <AUTHOR>
 * @date 2024/12/03
 */
public class IdUtils {
    // ...
}
```

**模块级注释（按需）**

模块目录下创建 `package-info.java`
，编写模块的主要类和用途。参考：[package-info.java](galaxy-boot-utils/src/main/java/cn/com/chinastock/cgs/utils/id/package-info.java)

参考：cn.com.chinastock.cgs.utils.id 包

```java
/**
 * 工具类包 - ID 生成器
 * <p>主要类和接口：</p>    
 * <ul>
 *     <li>{@link id.utils.cnf.cn.com.chinastock.docs.IdUtils} - ID 生成器工具类</li>
 *     <li>{@link id.utils.cnf.cn.com.chinastock.docs.ID_LEN} - ID 长度枚举类</li>
 * </ul>
 */
```

## 提交信息规范

#### 提交信息格式:

```
[JIRA-ID] <类型>(<范围>): <简要描述>

<详细描述>

<可选附加信息>
```

1. **类型（Type）**

定义提交的目的，常见类型包括：

- **`feat`**: 新功能（feature）
- **`fix`**: 修复问题（bug fix）
- **`docs`**: 文档修改（documentation）
- **`style`**: 代码格式（不影响代码逻辑）
- **`refactor`**: 代码重构（既不是新增功能，也不是修复问题）
- **`perf`**: 性能优化
- **`test`**: 添加或修改测试
- **`build`**: 构建过程或依赖相关的变更
- **`ci`**: CI/CD 配置或脚本变更
- **`chore`**: 杂项任务（如依赖更新）
- **`revert`**: 回滚之前的提交

2. **范围（Scope）**

范围使用模块名称，表示受影响的代码包：

- **`core`**: 影响 `galaxy-boot-core`（核心功能）
- **`dependencies`**: 影响 `galaxy-boot-dependencies`（依赖更新或配置）
- **`examples`**: 影响 `galaxy-boot-examples`（示例项目或演示）
- **`security`**: 影响 `galaxy-boot-security`（安全相关代码）
- **`starters`**: 影响 `galaxy-boot-starters`（起步模块）
- **`test`**: 影响 `galaxy-boot-test`（测试工具或用例）
- **`utils`**: 影响 `galaxy-boot-utils`（工具函数或辅助方法）

3. **简要描述（Description）**

- 简要描述需要用 **现在时态** 描述提交内容，例如“新增”、“修复”、“更新”。
- 描述应控制在 50 个字符以内，清晰明了。

**提交信息示例**

- **新增功能**:
  ```
  [JCKJ-12] feat(core): 新增服务发现机制
  ```

- **修复 Bug**:
  ```
  [JCKJ-12] fix(security): 修复登录模块中的 XSS 漏洞
  ```

- **依赖更新**:
  ```
  [JCKJ-12] chore(dependencies): 更新 Spring Boot 至 3.0.0
  ```

- **文档更新**:
  ```
  [JCKJ-12] docs(examples): 补充示例应用的详细安装说明
  ```

- **代码重构**:
  ```
  [JCKJ-12] refactor(utils): 简化 JSON 解析逻辑
  ```

- **测试相关**:
  ```
  [JCKJ-12] test(test): 添加认证模块的集成测试
  ```

- **构建变更**:
  ```
  [JCKJ-12] build(core): 更新 Maven 插件配置以支持发布
  ```

**提交信息规范注意事项**

1. 提交信息必须以 `[JIRA-ID]` 开头，其中 `JIRA-ID` 是你的 JIRA 任务编号，例如：`JCKJ-12`。
2. 使用模块名称作为范围，清楚标明受影响的项目部分。
3. 提交信息应清晰简洁，方便理解。
4. 对复杂更改或需要额外背景信息的提交，使用 **详细描述** 补充说明。
5. 如果提交涉及问题编号或重大变更，请在附加信息中注明。

示例附加信息：

```
BREAKING CHANGE: 重构 galaxy-boot-core 的方法签名  
Closes #123  
```
