# Http返回码规范
通过http返回码可以对响应做初步分类，方便网关和调用方对返回码进行判断，方便规范服务端返回码。
galaxy框架层对http返回码进行规范如下：
## 正常
### 200
- 接口正常时返回200
- 有业务异常时返回200，具体业务错误码通过响应体判断，参照[meta返回码规范]
## 客户端异常
### 400
- 参数校验异常，这里只校验格式（讨论业务异常的划分，如参数校验作为业务异常时，客户端现有逻辑可以拿到错误信息，如果直接返回400，客户端现有逻辑就不看错误信息了）
### 401
- 身份校验不过，身份校验规范参照[框架安全规范]()
### 403
- 身份验证通过，但是不满足接口鉴权，参照[框架安全规范]()
### 其他
- 未进入galaxy框架并遵循[Http状态码规范](https://datatracker.ietf.org/doc/html/rfc2616)而返回的错误码，例如
  - 404 找不到资源，客户端直接返回的错误码
  - 405 Method不允许，spring直接返回
  - 406 客户端通过Accept头设置的能接收的返回格式，服务端不支持时
## 服务端异常
### 500
- 通用错误，未被定义的异常被框架捕获后统一返回500
  - 空指针异常 
  - 定义的非业务异常，如数据库异常
### 503 
- 服务暂时不可用
- 限流？
### 其他
- 未进入galaxy框架并遵循[Http状态码规范](https://datatracker.ietf.org/doc/html/rfc2616)而返回的错误码，例如
  - 502 网关接收到无效响应的返回码
  - 504 例如，网关未收到DNS响应时的返回码 
  - 5XX PAAS平台提供的限流降级等功能