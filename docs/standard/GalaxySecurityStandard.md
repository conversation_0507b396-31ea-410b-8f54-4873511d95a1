## 安全规范

本文档描述了在安全方面，我们考虑在框架层面实现的需求

### 1. 安全需求说明

通过和安全团队的沟通及业务系统的访谈，目前对`GalaxyBoot`的安全需求主要是以下几个方面：

- 身份认证与授权
- 数据保护
- 接口安全

### 2. API认证与授权

#### 2.1 API认证机制

主要用于服务间API接口调用的验证，有以下几个功能点：

1. API认证方式说明
    - 服务提供方
        - 维护ESB以及各个调用者系统的信息（用户、密码等）
        - 用户密码等信息维护在配置中心
    - 服务调用方
        - 以ESB相同的方式进行身份认证，在Header中传入用户、密码等信息
    - ESB
        - 作为中间层，对服务提供方和服务调用方进行身份认证
        - ESB向后端服务请求时，以ESB自己的用户、密码进行身份认证
        - ESB向后端服务请求时，传递前端真实请求的系统名称
2. 认证机制同ESB：通过Base64(User+Pass+Timestamp+Check)，判断时间戳有效期、判断用户密码、hash值。
3. 调用链路说明：
   ```
   `App` --(APP用户+密码)--> `ESB` --(ESB用户+密码+请求APP三字码)--> `FrameworkAPP`(校验的用户是ESB+PASS)
   `App` --(APP用户+密码)--> `FrameworkAPP`(校验的用户是App+PASS)
   ```
4. 请求头参考示例：
   通过ESB调用后端系统时，ESB需要传递的Header:

```text
"X-REQUEST-Token": "base64("ESB"+pass+timestamp+check)"  
"X-REQUEST-SYSTEM": "BDP"
"X-REQUEST-USER": "ESB "
"X-REQUEST-TIMESTAMP": "timestamp"
```

直接请求后端系统时，需要传递的Header:

```text
"X-REQUEST-Token": "base64(BDP+pass+timestamp+check)"
"X-REQUEST-USER": "BDP"
"X-REQUEST-TIMESTAMP": "timestamp"
```

#### 2.2 API权限校验

1. 后端服务通过配置中心维护所有API的访问权限
2. 正反配置：正向配置：可以访问哪些API，反向配置：不可以访问哪些API，支持通配符配置
3. 示例：

```
service_name /api/* ESB Yes
service_name /api/user/login/* ESB No
```

### 3 Spring Security 数据保护

1. 输入合法性校验，避免参数中一些特殊字符（特殊字符进行转义、过滤）进行注入攻击
    - 安全的starter
2. 输出数据内容安全性检查（API 响应信息中只包含基本的 JSON 格式数据，无 HTML 等信息）
    - 默认关闭
3. endpoints 不安全信息默认关闭，env
    - actuator的 endpoints 默认关闭，只有部分开放
    - 需要看 actuator 放在那个 starter 里面？
4. 防止 XSS 攻击，输入输出数据合法性的校验、转义、过滤等
5. 防止 CSRF 攻击

[Security Properties](https://docs.spring.io/spring-boot/appendix/application-properties/index.html#appendix.application-properties.security)

### 4 Swagger（待定）

1. swagger文档增加用户密码
    - 单独的 starter，basic auth 认证，密码验证做成开关
    - 测试环境默认关闭认证，生产环境开启认证

### 5 数据库连接池（待定）

参考：数据库

1. 数据库连接池后台增加用户密码
    - 连接池的选型，数据库相关的技术栈选型，需要进一步讨论

