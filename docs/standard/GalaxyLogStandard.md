## Galaxy日志规范

本规范定义了 `GalaxyBoot` 框架的日志标准，包括日志格式、关键字段、日志级别，以及对业务日志和应用运行日志的特殊要求。
通过统一的日志规范，确保满足故障分析、问题定位、性能优化及安全审计的需求。

### 1. 日志需求说明

通过对各个系统的问题分析，目前对`GalaxyBoot`的日志需求主要是以下几个方面：

- 日志规范的统一：目前各个系统打印的日志格式多种多样，从日志收集的角度很难以统一的方式解析，也因此无法将关键信息进行结构化而支持更高效的检索，存在一定的查询性能问题。
- 日志支持全链路追踪：目前无法通过日志查询到一个请求的上下游相关日志，很难定位问题。
- 日志保存时限要求：根据运营标准中日志管理域的要求，对于不同的日志类别有不同的保存时限要求，需要在日志收集和转储过程中进行控制。

本规范将分别从这三个方面对日志相关的设计进行说明。

### 2. 日志格式定义

#### 2.1 应用服务日志内容定义

以下是 `GalaxyBoot` 框架日志的标准格式，适用于所有业务日志和应用运行日志。

| 字段名称                  | 框架记录 | 字段说明      | 	详细描述                                                                                   |
|-----------------------|------|-----------|-----------------------------------------------------------------------------------------|
| `version`             | Y    | 日志格式版本    | 初始版本为V1。                                                                                |
| `timestamp`           | Y    | 时间戳       | 毫秒级，格式为 ISO8601。e.g. 2024-12-08T16:06:17.046+0800                                       |
| `high_precision_time` | Y    | 高精度时间     | 	纳秒级，用于判断日志打印的先后顺序。e.g. 131070950714083                                                 |
| `log_level`           | N    | 日志级别      | 	日志级别（DEBUG, INFO, WARNING, ERROR），                                                     |
| `thread_id`           | Y    | 线程ID      | 	用于跟踪日志的线程来源。                                                                           |
| `ip_addr`             | Y    | IP地址      | 记录服务部署环境的IP地址。                                                                          |
| `request_method`      | Y    | 请求方法      | 	API 的HTTP方法名称（POST/GET/PUT/DELETE）。                                                    |
| `request_uri`         | Y    | 请求URI     | 	记录请求API的访问路径。                                                                          |
| `trace_id`            | Y    | Trace ID  | 	用于分布式系统的链路追踪。                                                                          |
| `parent_span_id`      | Y    | 父 Span ID | 	用于标识调用链路的上一级。                                                                          |
| `span_id`             | Y    | Span ID   | 	用于标识当前链路节点。                                                                            |
| `system_name`         | Y    | 系统名称      | 	当前系统标识（三字码，由数据中心申请和分配）                                                                 |
| `service_name`        | Y    | 服务名称      | 	当前服务名称（默认从 `application.yaml` 的 `spring.application.name` 获取），需要跟APM的service_name保持一致。 |
| `class_name`          | Y    | 类名        | 	记录当前日志的类名。                                                                             |
| `log_category`        | N    | 日志类别      | 	参见`LogCategory`定义。                                                                     |
| `message`             | N    | 日志消息      | 	详细的日志描述信息，在不同的日志类别下格式不同。                                                               |
| `stack_trace`         | N    | 异常堆栈      | 	异常堆栈信息（如果存在）。                                                                          |

#### 2.2 运行容器环境日志内容定义

以下是运行容器环境日志的内容，仅供参考，具体信息请参照MOP实际采集的字段。

| 序号 | 字段名称                | 样例                                                                 |
|----|---------------------|--------------------------------------------------------------------|
| 1  | `container_id`      | `acdea168264a08f9aaca0dfc82ff3551418dfd22d02b713142a6843caa2f61bf` |
| 2  | `container_name`    | `xxx-service-managers`                                             |
| 3  | `host_ip`           | `**********`                                                       |
| 4  | `kube_cluster_name` | `prodA1`                                                           |
| 5  | `namespace`         | `xxx-service`                                                      |
| 6  | `pod_name`          | `xxx-service-managers-v1-64-0-33-6479f897b5-xxx`                   |
| 7  | `node_name`         | `proda1node3.chinastock.com.cn`                                    |

#### 2.3 日志输出约定

1. 应用服务日志会输出到控制台，日志采集需要将控制台日志收集到日志中心。
   `ERROR`/`WARNING`级别日志输出到`stderr`，其他级别日志输出到`stdout`。
2. 每条日志应独立成行，一条完整日志不应使用换行符进行跨行记录；日志中的换行符应转义为`\n`。
3. 单行日志长度建议不超过 5KB，建议仅记录该行日志核心要素，避免记录过多无效内容导致日志质量下降；

#### 2.4 默认日志格式

考虑到日志文本的传输和存储成本，日志格式参照 [公用事件格式 (CEF)](https://www.microfocus.com/documentation/arcsight/arcsight-smartconnectors-8.3/cef-implementation-standard/Content/CEF/Chapter%201%20What%20is%20CEF.htm) 。
Galaxy Boot 默认的日志格式如下：

```text
日志格式版本|时间戳|高精度时间|日志级别|线程ID|IP地址|请求方法|请求URI|Trace ID|父 Span ID|Span ID|系统名称|服务名称|类名|日志类别|**日志消息**|异常堆栈
```

#### 2.5 日志格式说明

1. 日志的字段之间以`|`进行分隔，其中前14个字段为固定字段，每个字段有固定的含义和格式。
2. 第15个字段为`日志消息`，用于记录日志的具体内容，参考`CEF`中字段`Extension`的格式（`KEY-VAULUE`集合），根据不同的`日志类型`
   提供不同的扩展性，以便记录更多的信息。
3. 第16个字段为`stack_trace`，用于记录异常堆栈信息。

#### 2.6 日志输出示例

```text
V1|2025-01-10T14:48:14.349+0800|147306639878875|INFO|http-nio-8088-exec-1|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|28cef34256e84a96|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"content-length":"60","postman-token":"4a5f34c3-e896-41dc-91d8-e27d346652ac","traceparent":"00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01","host":"127.0.0.1:8088","content-type":"application/json","connection":"keep-alive","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body={"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"} query_string=null |-
V1|2025-01-10T14:48:14.349+0800|147306640006916|INFO|http-nio-8088-exec-1|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|28cef34256e84a96|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|BUSINESS_LOG|请求体: LogDTO{data='test data，  包括中文  data， '}|-
V1|2025-01-10T14:48:14.354+0800|147306645264291|INFO|http-nio-8088-exec-1|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|28cef34256e84a96|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=200 meta_code=0 body={"data":{"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"},"meta":{"code":"0","success":true,"message":"成功"},"pageInfo":null} |-
V1|2025-01-10T14:48:14.374+0800|147306665232833|INFO|http-nio-8088-exec-1|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|28cef34256e84a96|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=106 unit=ms|-
V1|2025-01-10T14:48:17.643+0800|147309933818666|WARN|Apollo-RemoteConfigLongPollService-1|***********|-|-|-|-|-|-|-|com.ctrip.framework.apollo.internals.RemoteConfigLongPollService|-|Long polling failed, will retry in 16 seconds. appId: galaxy-boot, cluster: default, namespaces: application, long polling url: http://localhost:8080/notifications/v2?cluster=default&appId=galaxy-boot&ip=*************&notifications=%5B%7B%22namespaceName%22%3A%22application%22%2C%22notificationId%22%3A-1%7D%5D, reason: Could not complete get operation [Cause: ConnectException(Connection refused)]|-
V1|2025-01-10T14:48:20.769+0800|147313059985666|INFO|http-nio-8088-exec-2|***********|POST|/api/test/log/upload|1c8b2890f63a4f8c963d2bdf4762369e|-|bfbf407117fd4546|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"content-length":"4348","postman-token":"42b07ac6-02f0-4b82-a01e-60efca6c2893","host":"127.0.0.1:8088","connection":"keep-alive","content-type":"multipart/form-data; boundary=--------------------------656184665826648118783334","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body=Files:[files-样例2.txt-4.0KB] query_string=null |-
V1|2025-01-10T14:48:20.769+0800|147313060102291|INFO|http-nio-8088-exec-2|***********|POST|/api/test/log/upload|1c8b2890f63a4f8c963d2bdf4762369e|-|bfbf407117fd4546|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|BUSINESS_LOG|文件上传: 样例2.txt|-
V1|2025-01-10T14:48:20.771+0800|147313061665958|INFO|http-nio-8088-exec-2|***********|POST|/api/test/log/upload|1c8b2890f63a4f8c963d2bdf4762369e|-|bfbf407117fd4546|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=200 meta_code=null body={"data":"文件上传成功","maskData":"***"} |-
V1|2025-01-10T14:48:20.774+0800|147313064508750|INFO|http-nio-8088-exec-2|***********|POST|/api/test/log/upload|1c8b2890f63a4f8c963d2bdf4762369e|-|bfbf407117fd4546|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=29 unit=ms|-
V1|2025-01-10T14:48:25.281+0800|147317572368500|INFO|http-nio-8088-exec-3|***********|GET|/api/test/log/download|9f3ddb3685174ae2a5b5a55b59923209|-|809ec47d953d4980|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"postman-token":"929cc421-3bba-4ebb-95fa-c8b6b4253e4a","host":"127.0.0.1:8088","connection":"keep-alive","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body=null query_string=null |-
V1|2025-01-10T14:48:25.282+0800|147317572858000|INFO|http-nio-8088-exec-3|***********|GET|/api/test/log/download|9f3ddb3685174ae2a5b5a55b59923209|-|809ec47d953d4980|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|BUSINESS_LOG|文件下载: file-to-download.md|-
V1|2025-01-10T14:48:25.286+0800|147317577019250|INFO|http-nio-8088-exec-3|***********|GET|/api/test/log/download|9f3ddb3685174ae2a5b5a55b59923209|-|809ec47d953d4980|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={"Content-Length":"25240","Content-Type":"application/octet-stream","Content-Disposition":"attachment; filename=\"file-to-download.md\"","Accept-Ranges":"bytes"} http_status_code=200 meta_code=null body=File:[file-to-download.md-24.6KB] |-
V1|2025-01-10T14:48:25.288+0800|147317579342000|INFO|http-nio-8088-exec-3|***********|GET|/api/test/log/download|9f3ddb3685174ae2a5b5a55b59923209|-|809ec47d953d4980|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=12 unit=ms|-
V1|2025-01-10T14:48:33.649+0800|147325939996458|WARN|Apollo-RemoteConfigLongPollService-1|***********|-|-|-|-|-|-|-|com.ctrip.framework.apollo.internals.RemoteConfigLongPollService|-|Long polling failed, will retry in 32 seconds. appId: galaxy-boot, cluster: default, namespaces: application, long polling url: http://localhost:8080/notifications/v2?cluster=default&appId=galaxy-boot&ip=*************&notifications=%5B%7B%22namespaceName%22%3A%22application%22%2C%22notificationId%22%3A-1%7D%5D, reason: Could not complete get operation [Cause: ConnectException(Connection refused)]|-
V1|2025-01-10T14:48:35.978+0800|147328268927916|INFO|http-nio-8088-exec-4|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|0435e545867a474c|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"content-length":"60","postman-token":"c7b77ebc-b039-4493-bf14-77f71da0300e","traceparent":"00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01","host":"127.0.0.1:8088","content-type":"application/json","connection":"keep-alive","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body={"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"} query_string=null |-
V1|2025-01-10T14:48:35.978+0800|147328269106583|INFO|http-nio-8088-exec-4|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|0435e545867a474c|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|BUSINESS_LOG|请求体: LogDTO{data='test data，  包括中文  data， '}|-
V1|2025-01-10T14:48:35.979+0800|147328269887625|INFO|http-nio-8088-exec-4|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|0435e545867a474c|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=200 meta_code=0 body={"data":{"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"},"meta":{"code":"0","success":true,"message":"成功"},"pageInfo":null} |-
V1|2025-01-10T14:48:35.981+0800|147328272017500|INFO|http-nio-8088-exec-4|***********|POST|/api/test/log/request-body|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|0435e545867a474c|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=6 unit=ms|-
V1|2025-01-10T14:48:43.190+0800|147335481103208|INFO|http-nio-8088-exec-5|***********|GET|/api/test/exception/unauthorized|716bc8e070974c1ab4384522f3f9b24e|-|b8829da5fea74a4f|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"postman-token":"b52ebeaa-0b28-4ab4-b3b6-f2309a1f59e4","host":"127.0.0.1:8088","connection":"keep-alive","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body=null query_string=null |-
V1|2025-01-10T14:48:43.194+0800|147335485093916|INFO|http-nio-8088-exec-5|***********|GET|/api/test/exception/unauthorized|716bc8e070974c1ab4384522f3f9b24e|-|b8829da5fea74a4f|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=401 meta_code=EXPTCNF401 body={"data":null,"meta":{"code":"EXPTCNF401","success":false,"message":"认证失败"},"pageInfo":null} |-
V1|2025-01-10T14:48:43.195+0800|147335486795625|INFO|http-nio-8088-exec-5|***********|GET|/api/test/exception/unauthorized|716bc8e070974c1ab4384522f3f9b24e|-|b8829da5fea74a4f|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=8 unit=ms|-
V1|2025-01-10T14:48:43.192+0800|147335483784250|ERROR|http-nio-8088-exec-5|***********|GET|/api/test/exception/unauthorized|716bc8e070974c1ab4384522f3f9b24e|-|b8829da5fea74a4f|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.exception.ControllerAdvice|EXCEPTION_LOG|认证失败|cn.com.chinastock.cnf.core.exception.UnauthorizedException: 用户未认证\n\t	at cn.com.chinastock.cnf.examples.controller.ExceptionController.unauthorizedError(ExceptionController.java:23) ~[classes/:?]\n\t	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\n\t	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\n\t	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at cn.com.chinastock.cnf.core.log.aspect.ControllerLogAspect.around(ControllerLogAspect.java:54) ~[classes/:?]\n\t	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\n\t	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\n\t	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720) ~[spring-aop-6.1.16.jar:6.1.16]\n\t	at cn.com.chinastock.cnf.examples.controller.ExceptionController$$SpringCGLIB$$0.unauthorizedError(<generated>) ~[classes/:?]\n\t	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\n\t	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\n\t	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.34.jar:6.0]\n\t	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.34.jar:6.0]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at cn.com.chinastock.cnf.security.input.XssInputValidationFilter.doFilter(XssInputValidationFilter.java:31) ~[classes/:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.3.6.jar:6.3.6]\n\t	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195) ~[spring-webmvc-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230) ~[spring-security-config-6.3.6.jar:6.3.6]\n\t	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at cn.com.chinastock.cnf.core.log.filter.LogFilter.doFilterInternal(LogFilter.java:52) ~[classes/:?]\n\t	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.16.jar:6.1.16]\n\t	at com.tongweb.container.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:159) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:134) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardWrapperValve.invoke(StandardWrapperValve.java:151) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardContextValve.invoke(StandardContextValve.java:74) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:457) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardHostValve.invoke(StandardHostValve.java:99) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.valves.ErrorReportValve.invoke(ErrorReportValve.java:78) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.valves.ErrorReportValve.invoke(ErrorReportValve.java:78) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.core.StandardEngineValve.invoke(StandardEngineValve.java:57) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.container.connector.CoyoteAdapter.service(CoyoteAdapter.java:324) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.http11.Http11Processor.service(Http11Processor.java:353) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.AbstractProcessorLight.process(AbstractProcessorLight.java:47) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.connector.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:874) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1721) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.net.SocketProcessorBase.run(SocketProcessorBase.java:37) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at com.tongweb.web.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:48) ~[tongweb-embed-core-3.x-8.0.E.3.jar:?]\n\t	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]\n\t
V1|2025-01-10T14:50:46.578+0800|147458871401250|INFO|http-nio-8088-exec-7|***********|POST|/api/test/log/query-string|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|42bf93a1fe914dbc|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"content-length":"88","postman-token":"a4d2ed02-9b34-45cd-995e-908cf47354d6","traceparent":"00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01","host":"127.0.0.1:8088","content-type":"application/json","connection":"keep-alive","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.43.0","accept":"*/*"} body={"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"} query_string=level=INFO&category=APP_LOG |-
V1|2025-01-10T14:50:46.579+0800|147458872224291|INFO|http-nio-8088-exec-7|***********|POST|/api/test/log/query-string|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|42bf93a1fe914dbc|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|APP_LOG|日志级别: INFO, 日志类别: APP_LOG, 日志信息: test data，  包括中文  data， |-
V1|2025-01-10T14:50:46.579+0800|147458872726625|INFO|http-nio-8088-exec-7|***********|POST|/api/test/log/query-string|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|42bf93a1fe914dbc|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=200 meta_code=0 body={"data":{"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"},"meta":{"code":"0","success":true,"message":"成功"},"pageInfo":null} |-
V1|2025-01-10T14:50:46.581+0800|147458873793708|INFO|http-nio-8088-exec-7|***********|POST|/api/test/log/query-string|0af7651916cd43dd8448eb211c80319c|00f067aa0ba902b7|42bf93a1fe914dbc|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=15 unit=ms|-
```

#### 2.7 LogCategory定义

```java
// 日志类别
public enum LogCategory {
    /**
     * 交易流水日志。记录字段包括用户标识、交易业务参数、交易状态及结果等信息，安全级别（公司数据分类分级）为4级的数据（一般指投资者的账户认证信息，包含投资者用于身份认证的账户编码、密码等）敏感信息应进行脱敏处理。
     */
    TRANSACTION_LOG,
    /**
     * 业务行为日志。记录字段包括业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。
     */
    BUSINESS_LOG,
    /**
     * 应用系统用户操作行为日志。记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。
     */
    USER_ACTION_LOG,
    /**
     * 应用运行日志
     */
    APP_LOG,
    /**
     * 框架日志
     */
    FRAMEWORK_LOG,
    /**
     * 异常处理相关日志
     */
    EXCEPTION_LOG,
    /**
     * SQL语句日志
     */
    SQL_LOG,
    /**
     * 请求日志
     */
    REQUEST_LOG,
    /**
     * 响应日志
     */
    RESPONSE_LOG,
    /**
     * 性能相关日志
     */
    PERFORMANCE_LOG
}
```

其中`REQUEST_LOG`/`RESPONSE_LOG`/`PERFORMANCE_LOG`/`SQL_LOG`/`FRAMEWORK_LOG`由框架打印。
其他类型的日志可以由业务代码在不同场景下选择合适的`LogCategory`进行打印。

- `TRANSACTION_LOG`：记录交易流水信息，例如用户标识、交易业务参数、交易状态及结果等信息。
- `BUSINESS_LOG`：记录业务行为信息，例如业务标识、业务处理耗时、业务请求参数、处理状态及处理结果等信息。
- `USER_ACTION_LOG`：记录系统用户的增加、删除、修改和导出等操作行为，必须记录系统用户对于客户资产和交易数据查询的操作行为，以满足合规、审计、内部控制等工作的需要。
- `APP_LOG`：记录其他未归类的系统运行日志。

#### 2.8 可扩展字段说明

对于`REQUEST_LOG`/`RESPONSE_LOG`/`PERFORMANCE_LOG`三种类型的日志，在`message`字段中存储的是`KEY-VALUE`的集合，具体结构定义如下：

2、 `REQUEST_LOG`：存储请求的参数信息，例如`header`、`body`和`query_string`。示例如下：

```text
headers={"content-length":"88","traceparent":"00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01","host":"127.0.0.1:8088","content-type":"application/json","connection":"keep-alive","accept-encoding":"gzip, deflate, br","accept":"*/*"} body={"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"} query_string=level=INFO&category=APP_LOG
```

2、`RESPONSE_LOG`：存储响应的参数信息，例如`headers`、`http_status_code`、`meta_code`和`body`。示例如下：

```text
headers={"Content-Length":"25240","Content-Type":"application/octet-stream","Content-Disposition":"attachment; filename=\"file-to-download.md\"","Accept-Ranges":"bytes"} http_status_code=200 meta_code=0 body={"data":{"data":"test data，\r\n 包括中文  data，\r\n","maskData":"***"},"meta":{"code":"0","success":true,"message":"成功"},"pageInfo":null}
```

3、 `PERFORMANCE_LOG`：存储性能相关的信息，例如`cost`、`unit`。示例如下：

```text
cost=2 unit=ms
```

#### 2.9 日志输出的脱敏与转义

1. 对于`REQUEST_LOG`、`RESPONSE_LOG`中的敏感字段或超长字段，可以进行掩码处理
   如果要开启掩码功能，需要在配置文件中开启相关的配置项，参见[galaxy-boot-core-log]()使用说明文档。
2. 对于日志中的特殊字符，如`|`，需要进行转义处理，保证能够正常解析。
   默认转义为`%7C`（URL 编码方式），解析时如有必要，可以进行反转义。

### 3. 基于日志的分布式链路追踪

日志内容中记录了`trace_id`、`parent_span_id`、`span_id`，可以通过这三个字段对日志进行链路追踪，从而实现全链路追踪的功能。
为了保证和APM工具的兼容，`trace_id`、`parent_span_id`及`span_id`
的格式定义参考[Trace Context HTTP Headers Format](https://www.w3.org/TR/trace-context/#trace-context-http-headers-format)
的定义。
在具体实现中，不同的APM工具可能有不同的要求，需要留出可扩展的接口，以便应对不同情况。

#### 3.1 格式定义

`trace_id`是整个链路的唯一标识，格式如下：

```
以 16 字节数组表示，例如 4bf92f3577b34da6a3ce929d0e0e4736。
所有字节均为零（000000000000000000000000）将被视为无效值。
```

`parent_span_id`标识了请求的来源，格式如下：

```
以 8 字节数组表示，例如 00f067aa0ba902b7。
所有字节都为零（0000000000000000）将被视为无效值。
```

`span_id`是整个链路在当前系统中的标识，格式同`parent_span_id`。当需要请求其他系统时，会把当前的`span_id`作为
`parent_span_id`传递给下游系统。

#### 3.2 分布式链路的上下文传递

为了实现分布式链路追踪，参照[W3C Trace Context](https://www.w3.org/TR/trace-context/)的规范，要求在HTTP请求头中传递
`trace_id`、`parent_span_id`，格式如下：

```
Header Name: traceparent
Format: traceparent: <version>-<trace-id>-<parent-span-id>-<trace-flags>
Sample: traceparent: 00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01
```

`version`和`trace_flags`
的取值参考[Trace Context HTTP Headers Format](https://www.w3.org/TR/trace-context/#trace-context-http-headers-format)
的定义。 无特殊设计情况下，可以分别设置为`00`和`01`。

#### 3.3 其他说明

在GalaxyBoot框架中会默认实现`trace_id`、`parent_span_id`、`span_id`的生成和传递，使用者不需要任何额外编码操作。
规则：

1. 当一个请求进入系统时，如果请求头中没有`traceparent`，则生成一个新的`trace_id`和`span_id`，`parent_span_id`为空。
2. 当一个请求进入系统时，如果请求头中有`traceparent`，则解析`traceparent`中的`trace_id`和`parent_span_id`，生成一个新的
   `span_id`。
3. 当调用其他系统时，将当前的`span_id`作为`parent_span_id`，同`trace_id`通过`traceparent`传递给其他系统。

### 4. 日志保存时限

#### 4.1 日志保存时限要求

在文档[《附件1：新版日志管理域标准.xlsx》](https://cloud.chinastock.com.cn/d/home#/sandbox/5a41/1f92b3c875391027/%2FIT%E6%8A%80%E6%9C%AF%E6%9E%B6%E6%9E%84%E6%B2%BB%E7%90%86%E4%BA%8C%E6%9C%9F%2F%E8%BF%90%E8%90%A5%E6%A0%87%E5%87%86/?previewingFileName=%E9%99%84%E4%BB%B61%EF%BC%9A%E6%96%B0%E7%89%88%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86%E5%9F%9F%E6%A0%87%E5%87%86.xlsx)
中提出了11类日志（交易流水日志，业务行为日志，应用系统用户操作行为日志，应用运行日志，数据库操作行为记录，数据库运行日志，中间件日志，操作系统日志，网络设备日志，信息安全设备日志）的记录要求，
这里讨论的日志主要是 **交易流水日志，业务行为日志，应用系统用户操作行为日志，应用运行日志** 这4类日志的输出内容要求，其中*
*交易流水日志和应用系统用户操作行为日志**只在一些特定的系统中存在，其他日志应通过相应的中间件或系统进行记录和获取，但从日志收集的信息上可以保持统一。

其中：

- 交易流水日志、业务行为日志、应用系统用户操作行为日志，可归类为业务日志，具体的记录内容要求需要业务服务根据要求在日志信息中详细记录。
- 应用运行日志 主要记录应用运行过程中的一些关键信息，尽量通过开发框架进行记录，以保证日志的统一性。

具体的日志保存时限参照[《附件1：新版日志管理域标准.xlsx》](https://cloud.chinastock.com.cn/d/home#/sandbox/5a41/1f92b3c875391027/%2FIT%E6%8A%80%E6%9C%AF%E6%9E%B6%E6%9E%84%E6%B2%BB%E7%90%86%E4%BA%8C%E6%9C%9F%2F%E8%BF%90%E8%90%A5%E6%A0%87%E5%87%86/?previewingFileName=%E9%99%84%E4%BB%B61%EF%BC%9A%E6%96%B0%E7%89%88%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86%E5%9F%9F%E6%A0%87%E5%87%86.xlsx)

#### 4.2 日志采集和转储

在《附件1：新版日志管理域标准.xlsx》中规定了不同日志类型，以及不同级别的系统对日志的保存和转储要求。
为了实现这些要求，需要在日志收集和转储过程中对日志进行分类，根据日志的`system_name`、`service_name`和`log_category`
字段进行分类，然后根据不同的日志类别进行保存和转储。
其中`system_name`为系统的三字码，`service_name`为该系统下具体的服务名称，这两个字段用于标识日志的来源，在具体实现层面可以通过
**配置文件或环境变量**进行设置（*待定）。
`log_category`表示日志的不同类型，在框架中会提供枚举类，便于在日志打印时进行设置。

日志按天进行转储，每日凌晨对前一天的日志转储，按照日志中的system_name、service_name、log_category三个字段将日志存储为不同的文件（压缩后存储），不同log_category日志的保留时间按照《附件1：新版日志管理域标准.xlsx》如下所示

| log_category    | 存储时间      | 备注                   |
|-----------------|-----------|----------------------|
| TRANSACTION_LOG | 20年       |                      |
| BUSINESS_LOG    | 5年        |                      |
| USER_ACTION_LOG | 5年        |                      |
| APP_LOG         | 1-3年      | 等保3级系统保存3年，其他系统保存1年  |
| FRAMEWORK_LOG   |           | 同APP_LOG             |
| PERFORMANCE_LOG |           | 同APP_LOG             |
| EXCEPTION_LOG   |           | 同APP_LOG             |
| REQUEST_LOG     |           | 同APP_LOG             |
| RESPONSE_LOG    |           | 同APP_LOG             |
| SQL_LOG         | 6个月       |                      |

另外，对于配置的特定日期，可以将指定system_name、service_name在特定日期的日志保存10年（发生网络安全时间时，当天的日志保存10年）
