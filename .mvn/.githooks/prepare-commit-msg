#!/bin/sh

commitMsgFile="$1"
commitSource="$2"
commitMsgContent=$(cat "$commitMsgFile")
regString='^\[JCKJ-[0-9]{0,10}\]( )?(build|ci|docs|feat|fix|perf|refactor|style|test|chore)(\([a-zA-Z0-9_-]+\))?: .{10,}$'

# merge 不做检查
if [ "${commitSource}" = "merge" ]; then
  exit 0
fi

# 检查是否符合格式
# shellcheck disable=SC3010
if [[ $commitMsgContent =~ $regString ]]; then
  exit 0
fi

# 不符合格式进行提醒
echo "❌ 提交信息格式不正确: '$commitMsgContent'"
echo "✅ 正确格式: [JIRA-ID] type(scope): description"
echo ""
echo "📌 说明："
echo "  - [JIRA-ID] 为 JIRA 任务编号，例如：JCKJ-12"
echo "  - type 必须是以下之一: build, ci, docs, feat, fix, perf, refactor, style, test, chore"
echo "  - scope 描述模块或影响范围，例如：security, utils"
echo "  - description 为简要描述，长度至少 10 个字符"
echo ""
echo "📋 示例："
echo "  [JCKJ-12] feat(starter-tongweb): 添加基础 TongWeb Spring Boot 3.x 版本的 LICENSE 自动配置"
echo "  [JCKJ-12] fix(security): 修复 XSS 漏洞"
echo "  [JCKJ-12] chore(dependencies): 更新 Spring Boot 至 3.4.0"
echo "  [JCKJ-12] docs(examples): 补充示例应用的详细安装说明"
echo "  [JCKJ-12] refactor(utils): 简化 JSON 解析逻辑"
echo "  [JCKJ-12] test(test): 添加认证模块的集成测试"
echo "  [JCKJ-12] build(core): 更新 Maven 插件配置以支持发布"
echo ""
exit 1
